erDiagram
    CIF_CLIENT {
        varchar(20) CLIENT_NO PK
        varchar(200) CLIENT_SHORT
        varchar(200) CLIENT_NAME
        varchar(200) CH_CLIENT_NAME
        varchar(200) EN_CLIENT_NAME
        varchar(100) EN_CLIENT_SHORT
        varchar(3) CLIENT_TYPE
        varchar(3) CATEGORY_TYPE
    }
    CIF_CLIENT_INDVL {
        varchar(20) CLIENT_NO PK
        varchar(200) CH_GIVEN_NAME
        varchar(20) CH_SURNAME
        varchar(200) GIVEN_NAME
        varchar(50) SURNAME
        varchar(50) NATION
        varchar(10) RACE
        char(1) SURNAME_FIRST
    }
    CIF_CLIENT_CORP {
        varchar(20) CLIENT_NO PK
        varchar(200) CLIENT_NAME
        varchar(50) ORGAN
        varchar(30) FIN_APP_CODE
        varchar(30) INP_EXP_NO
        char(1) BUSILICENCE_STATUS
        varchar(5) CORP_SIZE
        int EMP_NUM
    }
    CIF_CLIENT_DOCUMENT {
        varchar(20) CLIENT_NO PK
        varchar(3) DOCUMENT_TYPE PK
        varchar(50) DOCUMENT_ID PK
        varchar(3) ISS_COUNTRY PK
        varchar(10) ISS_STATE
        varchar(50) ISSUE_BRANCH
        varchar(200) ISS_PLACE
        varchar(10) ISS_CITY
    }
    CIF_CLIENT_CONTACT_TBL {
        varchar(20) CLIENT_NO PK
        varchar(20) CONTACT_TYPE PK
        varchar(5) CITY_TEL
        varchar(50) CONTACT_TEL
        varchar(50) MOBILE_PHONE
        varchar(3) COUNTRY
        varchar(5) COUNTRY_TEL
        varchar(10) STATE
    }
    CIF_CLIENT_BLOCK {
        varchar(50) SEQ_NO PK
        varchar(20) CLIENT_NO
        datetime BLOCK_DATE
        char(1) BLOCK_GROUP_FLAG
        varchar(2) BLOCK_REASON
        varchar(30) BLOCK_USER_ID
        varchar(30) BLOCK_AUTH_USER_ID
        char(1) UNFROZEN_FLAG
    }
    CIF_CHANNEL_CONTROL {
        varchar(50) CONTROL_SEQ_NO PK
        varchar(3) CONTROL_TYPE
        varchar(30) AUTH_USER_ID
        datetime LAST_CHANGE_DATE
        varchar(30) LAST_CHANGE_USER_ID
        varchar(20) CLIENT_NO
        varchar(50) TRAN_BRANCH
        char(1) CONTROL_STATUS
    }
