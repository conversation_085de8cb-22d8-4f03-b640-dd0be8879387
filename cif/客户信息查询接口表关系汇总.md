# 客户信息查询接口表关系汇总

## 涉及表清单

### 核心表 (1个)
| 表名 | 说明 | 关系类型 | 查询条件 |
|------|------|----------|----------|
| cif_client | 客户基本信息主表 | 主表 | CLIENT_NO 或通过证件信息关联 |

### 客户扩展表 (2个)
| 表名 | 说明 | 关系类型 | 查询条件 |
|------|------|----------|----------|
| cif_client_indvl | 个人客户扩展信息 | 1:1 | CLIENT_NO = cif_client.CLIENT_NO |
| cif_client_corp | 企业客户扩展信息 | 1:1 | CLIENT_NO = cif_client.CLIENT_NO |

### 证件联系表 (3个)
| 表名 | 说明 | 关系类型 | 查询条件 |
|------|------|----------|----------|
| cif_client_document | 客户证件信息 | 1:N | CLIENT_NO = cif_client.CLIENT_NO |
| cif_client_contact_tbl | 客户联系信息 | 1:N | CLIENT_NO = cif_client.CLIENT_NO |
| cif_contact_list | 客户联系人信息 | 1:N | CLIENT_NO = cif_client.CLIENT_NO |

### 身份核实表 (1个)
| 表名 | 说明 | 关系类型 | 查询条件 |
|------|------|----------|----------|
| cif_client_verification | 个人客户身份核实 | 1:1 | CLIENT_NO = cif_client.CLIENT_NO |

### NRA税务表 (2个)
| 表名 | 说明 | 关系类型 | 查询条件 |
|------|------|----------|----------|
| cif_client_nra | 客户NRA主表 | 1:1 | CLIENT_NO = cif_client.CLIENT_NO |
| cif_client_nra_detail | 客户NRA详细信息 | 1:N | CLIENT_NO = cif_client.CLIENT_NO |

### 对公客户专用表 (3个)
| 表名 | 说明 | 关系类型 | 查询条件 |
|------|------|----------|----------|
| cif_control_contact | 控制人税收居民联系 | 1:N | CLIENT_NO = cif_client.CLIENT_NO |
| cif_crop_compnay_info | 法人/财务主管信息 | 1:N | CLIENT_NO = cif_client.CLIENT_NO |
| cif_client_information | 对公客户附属信息 | 1:1 | CLIENT_NO = cif_client.CLIENT_NO |

### 关联信息表 (1个)
| 表名 | 说明 | 关系类型 | 查询条件 |
|------|------|----------|----------|
| cif_client_attach | 客户信息关联表 | 1:1 | CLIENT_NO = cif_client.CLIENT_NO |

### 权限控制表 (1个)
| 表名 | 说明 | 关系类型 | 查询条件 |
|------|------|----------|----------|
| cif_cross_relations | 客户关系表 | N:N | CLIENT_NO = cif_client.CLIENT_NO AND USER_ID |

### 参数配置表 (2个)
| 表名 | 说明 | 关系类型 | 查询条件 |
|------|------|----------|----------|
| cif_business_parameter | 业务参数表 | 配置表 | PARA_KEY IN ('CLIENT_TRAN_STATUS_C_FLAG', 'CLIENT_CLIENT_USER_LEVEL') |
| cif_define_column_information | 用户自定义信息 | 配置表 | APPLICATION_FLAG = 'Cif' |

## 查询逻辑流程

### 1. 客户定位阶段
```sql
-- 优先通过客户号查询
SELECT * FROM cif_client WHERE CLIENT_NO = ?

-- 备选通过三要素查询
SELECT c.* FROM cif_client c
JOIN cif_client_document d ON c.CLIENT_NO = d.CLIENT_NO
WHERE d.DOCUMENT_ID = ? AND d.DOCUMENT_TYPE = ? AND d.ISS_COUNTRY = ?
```

### 2. 业务参数检查
```sql
-- 检查是否需要验证客户注销状态
SELECT PARA_VALUE FROM cif_business_parameter
WHERE PARA_KEY = 'CLIENT_TRAN_STATUS_C_FLAG'
```

### 3. 客户类型分支查询
```sql
-- 个人客户扩展信息
SELECT * FROM cif_client_indvl WHERE CLIENT_NO = ?
SELECT * FROM cif_client_verification WHERE CLIENT_NO = ?

-- 对公客户扩展信息
SELECT * FROM cif_client_corp WHERE CLIENT_NO = ?
SELECT * FROM cif_control_contact WHERE CLIENT_NO = ?
SELECT * FROM cif_crop_compnay_info WHERE CLIENT_NO = ?
```

### 4. 通用信息查询
```sql
-- NRA信息
SELECT * FROM cif_client_nra WHERE CLIENT_NO = ?
SELECT * FROM cif_client_nra_detail WHERE CLIENT_NO = ?

-- 证件信息（含首选证件判断）
SELECT * FROM cif_client_document WHERE CLIENT_NO = ? ORDER BY PREF_FLAG DESC

-- 联系信息
SELECT * FROM cif_client_contact_tbl WHERE CLIENT_NO = ?
SELECT * FROM cif_contact_list WHERE CLIENT_NO = ?

-- 关联信息
SELECT * FROM cif_client_attach WHERE CLIENT_NO = ?
```

### 5. 权限检查
```sql
-- 检查柜员客户关系
SELECT * FROM cif_cross_relations
WHERE CLIENT_NO = ? AND USER_ID = ?

-- 检查用户级别参数
SELECT PARA_VALUE FROM cif_business_parameter
WHERE PARA_KEY = 'CLIENT_CLIENT_USER_LEVEL'
```

### 6. 自定义信息查询
```sql
-- 自定义字段配置
SELECT * FROM cif_define_column_information
WHERE APPLICATION_FLAG = 'Cif'

-- 对公客户附属信息
SELECT * FROM cif_client_information WHERE CLIENT_NO = ?
```

## 关键业务规则

### 1. 查询优先级
- **第一优先级**: CLIENT_NO（客户号）
- **第二优先级**: 三要素（证件号 + 证件类型 + 发证机关）
- **特殊逻辑**: 发证机关的匹配规则需要特殊处理

### 2. 客户类型判断
- **个人客户**: 查询 cif_client_indvl、cif_client_verification
- **对公客户**: 查询 cif_client_corp、cif_control_contact、cif_crop_compnay_info

### 3. 权限控制
- **关系检查**: 柜员与客户不能存在客户关系
- **级别控制**: level=3的柜员需要对敏感信息打码
- **信息掩码**: 手机号和证件号码进行脱敏处理

### 4. 证件管理
- **首选证件**: 通过 PREF_FLAG 标识
- **到期提醒**: 根据 MATURITY_DATE 判断到期情况
- **多证件支持**: 一个客户可以有多个证件

### 5. NRA税务信息
- **主从关系**: cif_client_nra 为主表，cif_client_nra_detail 为明细
- **税收居民**: 支持多个税收居民国家

## 总结

客户信息查询接口涉及 **16个核心表**，通过 **客户号(CLIENT_NO)** 作为主要关联键，形成了一个以客户为中心的星型数据模型。查询逻辑包含了完整的权限控制、业务规则验证和信息脱敏处理，体现了银行客户信息管理的严谨性和安全性要求。

### 核心业务表 (15个)

| 表名 | 中文名称 | 关系类型 | 主要用途 |
|------|----------|----------|----------|
| cif_client | 客户基本信息表 | 主表 | 存储客户基本信息，所有查询的起点 |
| cif_client_indvl | 个人客户扩展表 | 1:1 | 个人客户的详细信息扩展 |
| cif_client_corp | 企业客户扩展表 | 1:1 | 企业客户的详细信息扩展 |
| cif_client_document | 客户证件表 | 1:N | 客户的各种证件信息，支持多证件 |
| cif_client_contact_tbl | 客户联系信息表 | 1:N | 客户的联系方式信息 |
| cif_client_verification | 客户身份核实表 | 1:1 | 个人客户身份核实情况 |
| cif_client_nra | 客户NRA主表 | 1:1 | 客户非居民涉税信息主表 |
| cif_client_nra_detail | 客户NRA详细表 | 1:N | 客户非居民涉税详细信息 |
| cif_client_attach | 客户关联信息表 | 1:1 | 客户的附加关联信息 |
| cif_contact_list | 客户联系人表 | 1:N | 客户的联系人列表 |
| cif_cross_relations | 客户关系表 | N:N | 客户与柜员的关系信息 |
| cif_control_contact | 控制人税收居民联系表 | 1:N | 对公客户控制人信息 |
| cif_crop_compnay_info | 法人财务主管信息表 | 1:N | 对公客户法人和财务主管信息 |
| cif_client_information | 对公客户附属信息表 | 1:1 | 对公客户的附加信息 |
| cif_define_column_information | 自定义字段信息表 | 配置表 | 用户自定义字段配置 |

### 参数配置表 (1个)

| 表名 | 中文名称 | 主要用途 |
|------|----------|----------|
| cif_business_parameter | 业务参数表 | 控制查询行为的业务开关参数 |

## 表关系详细说明

### 1. 主表关系链
```
cif_client (客户主表)
├── cif_client_indvl (个人客户扩展) [1:1]
├── cif_client_corp (企业客户扩展) [1:1]
├── cif_client_document (客户证件) [1:N]
├── cif_client_contact_tbl (客户联系信息) [1:N]
├── cif_client_verification (身份核实) [1:1]
├── cif_client_nra (NRA主表) [1:1]
├── cif_client_nra_detail (NRA详细) [1:N]
├── cif_client_attach (关联信息) [1:1]
├── cif_contact_list (联系人列表) [1:N]
├── cif_cross_relations (客户关系) [N:N]
├── cif_control_contact (控制人联系) [1:N]
├── cif_crop_compnay_info (法人财务信息) [1:N]
└── cif_client_information (对公附属信息) [1:1]
```

### 2. 查询入口关系
- **主键查询**: CLIENT_NO → cif_client
- **三要素查询**: (DOCUMENT_ID + DOCUMENT_TYPE + ISS_COUNTRY) → cif_client_document → CLIENT_NO → cif_client

### 3. 条件分支关系
- **个人客户分支**: CLIENT_TYPE = 个人 → cif_client_indvl + cif_client_verification
- **对公客户分支**: CLIENT_TYPE = 企业 → cif_client_corp + cif_control_contact + cif_crop_compnay_info

### 4. NRA信息关系
```
cif_client_nra (NRA主表)
└── cif_client_nra_detail (NRA详细表) [1:N]
```

## 关键字段关联

### 主键关联
- **CLIENT_NO**: 所有客户相关表的主要关联字段
- **SEQ_NO**: 明细表的序列号字段
- **USER_ID**: 柜员相关的关联字段

### 复合主键
- **cif_client_document**: CLIENT_NO + DOCUMENT_TYPE + DOCUMENT_ID + ISS_COUNTRY
- **cif_client_contact_tbl**: CLIENT_NO + CONTACT_TYPE
- **cif_client_nra_detail**: CLIENT_NO + SEQ_NO
- **cif_cross_relations**: CLIENT_NO + USER_ID

## 查询逻辑特点

### 1. 分层查询策略
1. **第一层**: 客户基本信息查询 (cif_client)
2. **第二层**: 根据客户类型查询扩展信息
3. **第三层**: 查询通用附加信息 (证件、联系方式等)
4. **第四层**: 权限检查和信息处理

### 2. 条件控制机制
- **业务开关控制**: 通过 cif_business_parameter 控制查询行为
- **客户类型分支**: 个人客户和对公客户查询不同的扩展表
- **权限级别控制**: 根据柜员级别决定信息掩码处理

### 3. 数据完整性保证
- **主外键约束**: 通过 CLIENT_NO 保证数据关联完整性
- **业务规则验证**: 客户状态、关系检查等业务规则
- **权限控制**: 柜员与客户关系检查，防止利益冲突

## 性能优化建议

### 1. 索引优化
- **cif_client**: CLIENT_NO (主键索引)
- **cif_client_document**: (DOCUMENT_TYPE, ISS_COUNTRY, DOCUMENT_ID) 复合索引
- **cif_cross_relations**: (CLIENT_NO, USER_ID) 复合索引
- **cif_business_parameter**: PARA_KEY (主键索引)

### 2. 查询优化
- **批量查询**: 尽量减少数据库往返次数
- **条件过滤**: 在数据库层面进行条件过滤
- **字段选择**: 只查询需要的字段，避免全字段查询

### 3. 缓存策略
- **参数缓存**: cif_business_parameter 表数据可以缓存
- **客户基本信息缓存**: 高频查询的客户基本信息可以缓存
- **权限信息缓存**: 柜员权限信息可以缓存

## 业务规则总结

### 1. 查询优先级
1. CLIENT_NO 优先查询
2. 三要素备选查询
3. 特殊逻辑处理

### 2. 权限控制
1. 客户注销状态检查
2. 柜员客户关系检查
3. 信息掩码处理

### 3. 数据完整性
1. 必要信息完整性检查
2. 业务规则验证
3. 数据一致性保证

### 4. 扩展性设计
1. 支持自定义字段
2. 支持多种客户类型
3. 支持灵活的参数配置

## 接口设计优势

1. **完整性**: 覆盖客户信息的各个维度
2. **灵活性**: 支持多种查询方式和客户类型
3. **安全性**: 完善的权限控制和信息保护机制
4. **可配置性**: 通过参数表控制业务行为
5. **可扩展性**: 支持自定义字段和新的业务需求
