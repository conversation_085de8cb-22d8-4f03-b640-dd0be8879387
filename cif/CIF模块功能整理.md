# CIF模块功能整理

## 概述

CIF（Customer Information File）模块是客户信息管理系统的核心模块，负责客户信息的全生命周期管理。根据Excel文档分析，该模块共包含 **63个API接口**，涵盖客户信息管理的各个方面。

## 功能分类汇总

### 1. CIF81-客户信息管理 (22个接口)
这是CIF模块的核心功能，负责客户信息的创建、维护和管理。

#### 主要功能：
- **客户信息维护**：
  - 对公客户信息维护(修改版) - `/cif/nfin/client/public/maint/new`
  - 对公客户信息维护 - `/cif/nfin/client/public/maint`
  - 对私客户信息维护 - `/cif/nfin/client/private/maint`
  - 对私客户信息维护简化版 - `/cif/nfin/client/private/jsmaint`

- **客户创建与管理**：
  - 快速建立客户信息 - `/cif/nfin/client/create`
  - 批量开立客户信息 - 文件接口
  - 客户信息注销与激活 - `/cif/nfin/client/cancel`
  - 客户变更 - `/cif/nfin/client/change`

- **特殊客户管理**：
  - 联名客户关系建立 - `/cif/nfin/client/joint/maint`
  - FATCA信息维护 - `/cif/nfin/client/fatca/maint`
  - CRS信息维护 - `/cif/nfin/client/crs/maint`

- **客户关系管理**：
  - 客户经理维护 - `/cif/nfin/client/acctexec/maint`
  - 客户关系维护 - `/cif/nfin/client/relation/maint`
  - 客户密码维护 - `/cif/nfin/client/password/maint`

### 2. CIF82-客户信息查询 (19个接口)
提供全面的客户信息查询功能。

#### 主要功能：
- **基础查询**：
  - 客户基本信息查询 - `/cif/inq/client/basic`
  - 客户详细信息查询 - `/cif/inq/client/detail`
  - 查询客户基本信息(多笔) - `/cif/inq/client/batch`

- **证件查询**：
  - 客户证件信息查询 - `/cif/inq/client/certificate`
  - 客户证件批量查询 - `/cif/inq/client/certificate/batch`
  - 客户证件到期信息查询 - `/cif/inq/client/certificate/expire`

- **关系查询**：
  - 客户关系查询 - `/cif/inq/client/relation`
  - 客户经理信息查询 - `/cif/inq/client/acctexec`
  - 客户组成员信息查询 - `/cif/inq/client/group`

- **相似性查询**：
  - 相似客户查询 - `/cif/inq/client/resemble`
  - 客户相似信息查询 - `/cif/inq/client/similarity/query`

### 3. CIF83-客户冻结限制 (6个接口)
管理客户的各种限制和冻结状态。

#### 主要功能：
- **客户级限制**：
  - 客户级限制维护 - `/cif/nfin/client/restraint/maint`
  - 客户级限制查询 - `/cif/inq/client/restraint`

- **渠道限制**：
  - 客户渠道限制维护 - `/cif/nfin/channel/control`
  - 客户渠道限制查询 - `/cif/inq/channel/control`

- **冻结管理**：
  - 客户冻结维护 - `/cif/nfin/client/block/maint`
  - 客户冻结查询 - `/cif/inq/client/block`

### 4. CIF85-客户合并 (3个接口)
处理客户信息合并相关业务。

#### 主要功能：
- 客户合并 - `/cif/nfin/client/merge`
- 客户合并查询 - `/cif/inq/client/mergeinfo`
- 客户合并处理状态修改 - `/cif/nfin/client/merge/statusupdate`

### 5. CIF84-客户身份核查 (2个接口)
客户身份验证和核查功能。

#### 主要功能：
- 批量核查结果登记 - 文件接口
- 批量核查结果文件下载 - 文件接口

### 6. 其他功能模块

#### RB47-客户账户查询 (2个接口)
- FATCA信息查询 - `/cif/inq/client/fatca`
- CRS信息查询 - `/cif/inq/client/res`

#### CIF86-名单管理 (1个接口)
- 批量导入查询 - `/cif/inq/list/importInfo`

#### 机构撤并 (1个接口)
- 机构撤并客户校验项查询 - `/cif/inq/branch/change/checkinfo`

#### 柜员管理 (2个接口)
- 柜员权限查询 - `/cif/inq/user/access`
- 柜员权限维护 - `/cif/nfin/user/access`

## 业务分类统计

### 按交易类型分类：
- **1400-查询交易**: 26个接口 (41.3%)
- **1200-非金融交易**: 21个接口 (33.3%)
- **1220-文件交易**: 5个接口 (7.9%)
- **其他**: 11个接口 (17.5%)

### URL路径分析：
- `/cif/inq/*`: 30个接口 (查询类)
- `/cif/nfin/*`: 26个接口 (非金融交易类)
- `/cif/file/*`: 6个接口 (文件处理类)
- 其他: 1个接口

## 技术特点

1. **RESTful API设计**: 采用标准的REST风格URL设计
2. **功能模块化**: 按业务功能清晰分类
3. **支持批量操作**: 提供文件批量处理接口
4. **完整的CRUD操作**: 支持创建、查询、更新、删除操作
5. **多维度查询**: 支持按客户号、证件号、关系等多种方式查询
6. **状态管理**: 支持客户冻结、限制等状态管理

## 主要消费系统

- **TLE-综合柜员前端柜面系统**: 主要的消费系统
- **内部系统调用**: 系统间内部调用接口

## 总结

CIF模块是一个功能完整、设计合理的客户信息管理系统，涵盖了客户信息管理的全生命周期，从客户创建、维护、查询到状态管理、合并等各个环节。接口设计遵循RESTful规范，功能分类清晰，既支持单笔操作也支持批量处理，能够满足银行业务对客户信息管理的各种需求。
