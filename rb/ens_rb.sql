

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for batch_online_check
-- ----------------------------
DROP TABLE IF EXISTS `batch_online_check`;
CREATE TABLE `batch_online_check` (
  `JOB_ID` varchar(50) NOT NULL COMMENT 'JOB_ID',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'JOB_RUN_ID',
  `CHANNEL_SEQ_NO` varchar(50) NOT NULL COMMENT '渠道流水号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号',
  `OPERATION_ID` varchar(50) DEFAULT NULL COMMENT 'sonic执行序号',
  `STEP_TYPE` varchar(50) NOT NULL COMMENT '业务类型',
  `STEP_DESC` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `FILE_TYPE` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `PROCESS_TYPE` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `TRAN_DATE` date NOT NULL COMMENT '交易日期',
  `RUN_DATE` date DEFAULT NULL COMMENT '交易日期',
  `FILE_NAME` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_PATH` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_MD5` varchar(255) DEFAULT NULL COMMENT '文件MD5校验值',
  `START_TIME` datetime DEFAULT NULL COMMENT '流程开始时间',
  `END_TIME` datetime DEFAULT NULL COMMENT '流程结束时间',
  `TOTAL_NUMBER` bigint DEFAULT NULL COMMENT '文件总笔数',
  `SUCCESS_NUMBER` bigint DEFAULT NULL COMMENT '成功笔数',
  `FAILURE_NUMBER` bigint DEFAULT NULL COMMENT '失败笔数',
  `TRAN_STATUS` varchar(16) DEFAULT NULL COMMENT '业务处理状态 S成功 F失败 P处理中',
  `ERROR_CODE` varchar(6) DEFAULT NULL COMMENT '交易状态码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '交易状态描述信息',
  `HOST_IP` varchar(16) DEFAULT NULL COMMENT 'IP',
  `BATCH_CLASS` varchar(10) DEFAULT NULL COMMENT '批量交易类型',
  `BRANCH_ID` varchar(20) DEFAULT NULL COMMENT '机构代码',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道',
  `SYS_HEAD` blob COMMENT '联机调批量请求头信息',
  `APP_HEAD` blob COMMENT '联机调批量应用头信息',
  `ATTR_JSON` blob COMMENT '联机请求报文',
  PRIMARY KEY (`BATCH_NO`,`STEP_TYPE`,`CHANNEL_SEQ_NO`,`JOB_ID`,`TRAN_DATE`) USING BTREE,
  KEY `idx1_check` (`BATCH_NO`,`STEP_TYPE`,`PROCESS_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='文件登记表(UPRGHT)';

-- ----------------------------
-- Table structure for batch_online_check_details
-- ----------------------------
DROP TABLE IF EXISTS `batch_online_check_details`;
CREATE TABLE `batch_online_check_details` (
  `JOB_ID` varchar(50) NOT NULL COMMENT 'JOB_ID',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'JOB_RUN_ID',
  `STEP_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'STEP_RUN_ID',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号',
  `OPERATION_ID` varchar(50) DEFAULT NULL COMMENT 'sonic执行序号',
  `STEP_TYPE` varchar(50) NOT NULL COMMENT '业务类型',
  `FILE_TYPE` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `PROCESS_TYPE` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `TRAN_DATE` date NOT NULL COMMENT '交易日期',
  `RUN_DATE` date DEFAULT NULL COMMENT '交易日期',
  `FILE_NAME` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_PATH` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_MD5` varchar(255) DEFAULT NULL COMMENT '文件MD5校验值',
  `START_TIME` datetime DEFAULT NULL COMMENT '流程开始时间',
  `END_TIME` datetime DEFAULT NULL COMMENT '流程结束时间',
  `TOTAL_NUMBER` bigint DEFAULT NULL COMMENT '文件总笔数',
  `SUCCESS_NUMBER` bigint DEFAULT NULL COMMENT '成功笔数',
  `FAILURE_NUMBER` bigint DEFAULT NULL COMMENT '失败笔数',
  `TRAN_STATUS` varchar(16) DEFAULT NULL COMMENT '业务处理状态 S:成功 F:失败 P 处理中',
  `ERROR_CODE` varchar(6) DEFAULT NULL COMMENT '交易状态码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '交易状态描述信息',
  `HOST_IP` varchar(16) DEFAULT NULL COMMENT 'IP',
  PRIMARY KEY (`JOB_ID`,`BATCH_NO`,`STEP_TYPE`,`TRAN_DATE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='文件登记表明细(UPRGHT)';

-- ----------------------------
-- Table structure for batch_online_upload
-- ----------------------------
DROP TABLE IF EXISTS `batch_online_upload`;
CREATE TABLE `batch_online_upload` (
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'JOB_RUN_ID',
  `STEP_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'STEP_RUN_ID',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号',
  `STEP_TYPE` varchar(50) NOT NULL COMMENT '业务类型',
  `FILE_NAME` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_PATH` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `UPLOAD_PATH` varchar(255) DEFAULT NULL COMMENT '上传文件路径',
  `BATCH_CLASS` varchar(10) DEFAULT NULL COMMENT '批量交易类型',
  `BRANCH_ID` varchar(20) DEFAULT NULL COMMENT '机构代码',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道',
  PRIMARY KEY (`BATCH_NO`) USING BTREE,
  KEY `idx1_upload` (`BATCH_NO`),
  KEY `idx2_upload` (`BATCH_NO`,`STEP_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量文件上传登记表(UPRGHT)';

-- ----------------------------
-- Table structure for card_doc_ext_info
-- ----------------------------
DROP TABLE IF EXISTS `card_doc_ext_info`;
CREATE TABLE `card_doc_ext_info` (
  `CD_SERVICE_CODE` varchar(5) DEFAULT NULL COMMENT '卡服务代码|卡服务代码',
  `DOC_TYPE` varchar(10) NOT NULL COMMENT '凭证类型|凭证类型',
  `CD_CATEGORY` varchar(3) DEFAULT NULL COMMENT '卡细类|卡片细类|0-借记卡,1-贷记卡,2-借贷一体卡,3-单位结算卡,ALL-均支持',
  `CD_SELF_SEL` varchar(1) DEFAULT NULL COMMENT '自选卡号标志|自选卡号标志',
  `CD_TYPE` varchar(10) DEFAULT NULL COMMENT '卡de类型|卡de类型',
  `CD_CARD_BIN` varchar(10) DEFAULT NULL COMMENT '卡 BIN |卡 BIN ',
  `CD_RULE` varchar(5) DEFAULT NULL COMMENT '卡号规则|卡号规则',
  `CD_VALID_TIME` varchar(5) DEFAULT NULL COMMENT '卡介质有效期|卡介质有效期',
  `CD_MEDIUM` varchar(5) DEFAULT NULL COMMENT '卡媒介|卡媒介',
  `CD_APPLY_MAX_NUM` varchar(10) DEFAULT NULL COMMENT '制卡申请允许最大数|制卡申请允许最大数',
  `CD_CARD_TYPE` varchar(3) DEFAULT NULL COMMENT '卡的类型|卡的类型',
  `CD_SAME_FLAG` varchar(1) DEFAULT NULL COMMENT '同号换卡|同号换卡',
  `CD_MADE_STANDARD` varchar(10) DEFAULT NULL COMMENT '制卡标准|制卡标准',
  `CD_IS_APPER` varchar(1) DEFAULT NULL COMMENT '是否为附属卡|是否为附属卡',
  `CD_APPLY_MIN_NUM` varchar(200) DEFAULT NULL COMMENT '制卡申请允许最小数|制卡申请允许最小数',
  `WITHDRAWAL_TYPE` varchar(1) DEFAULT NULL COMMENT '支取方式|支取方式|S-凭印鉴支取,P-凭密码支取,W-无密码无印鉴支取,B-凭印鉴和密码支取,O-凭证件支取,R-支付密码器和印鉴',
  PRIMARY KEY (`DOC_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='卡凭证扩展信息表|卡凭证扩展信息表';

-- ----------------------------
-- Table structure for cd_apply_client_reg
-- ----------------------------
DROP TABLE IF EXISTS `cd_apply_client_reg`;
CREATE TABLE `cd_apply_client_reg` (
  `CARD_NO` varchar(50) NOT NULL COMMENT '卡号|卡号',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `APPLY_NO` varchar(50) NOT NULL COMMENT '申请编号|申请编号',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `MOBILE_PHONE` varchar(50) DEFAULT NULL COMMENT '移动电话|移动电话',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  PRIMARY KEY (`CARD_NO`,`APPLY_NO`,`CLIENT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='自选卡申请客户登记|用于登记自选卡行内或行外客户的三要素';

-- ----------------------------
-- Table structure for cd_card_file_job
-- ----------------------------
DROP TABLE IF EXISTS `cd_card_file_job`;
CREATE TABLE `cd_card_file_job` (
  `BATCH_JOB_NO` varchar(50) NOT NULL COMMENT '制卡文件批次号|制卡文件批次号',
  `APPLY_NO` varchar(50) DEFAULT NULL COMMENT '申请编号|申请编号',
  `MAKE_CARD_TYPE` char(1) DEFAULT NULL COMMENT '制卡类型|制卡类型|A-预约卡,N-非吉祥卡,V-吉祥卡',
  `SAME_CARD_FLAG` char(1) DEFAULT NULL COMMENT '是否同号换卡|是否同号换卡|Y-是,N-否',
  `APPLY_DATE` datetime DEFAULT NULL COMMENT '申请日期|申请日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `START_TIME` varchar(26) DEFAULT NULL COMMENT '起始时间|起始时间',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `CARD_NO_FROM` varchar(50) DEFAULT NULL COMMENT '起始卡号|起始卡号',
  `CARD_NO_THRU` varchar(50) DEFAULT NULL COMMENT '截止卡号|截止卡号',
  `CARD_NUM` int NOT NULL COMMENT '制卡数量|制卡数量',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|渠道类型',
  `FILE_TYPE` varchar(50) NOT NULL COMMENT '文件类型|文件类型',
  `FILE_NAME` varchar(200) DEFAULT NULL COMMENT '文件名称|文件名称',
  `FILE_PATH` varchar(200) DEFAULT NULL COMMENT '文件路径|文件路径',
  `FILE_STATUS` char(1) NOT NULL COMMENT '文件状态|文件状态|0-制卡文件/清单待生成,1-制卡文件/清单生成中,2-制卡文件/清单生成成功,3-制卡文件/清单生成失败,4-制卡文件生成完成(待IC卡系统加工)',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_JOB_NO`,`FILE_TYPE`),
  KEY `IDX_CD_CARD_FILE_JOB_1M` (`APPLY_NO`,`APPLY_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='制卡文件与清单信息表|登记制卡文件与清单，制卡状态、产品、卡类型等信息';

-- ----------------------------
-- Table structure for cd_card_prev
-- ----------------------------
DROP TABLE IF EXISTS `cd_card_prev`;
CREATE TABLE `cd_card_prev` (
  `QUERY_PWD` varchar(200) DEFAULT NULL COMMENT '查询密码',
  `BATCH_JOB_NO` varchar(50) DEFAULT NULL COMMENT '制卡文件批次号|制卡文件批次号',
  `APPLY_NO` varchar(50) DEFAULT NULL COMMENT '申请编号|申请编号',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `APPLY_USER_ID` varchar(30) DEFAULT NULL COMMENT '申请柜员|申请柜员',
  `CARD_NO` varchar(50) NOT NULL COMMENT '卡号|卡号',
  `CARD_STATUS` char(1) NOT NULL COMMENT '卡状态|卡状态|1-申请,2-待发,3-活动,4-注销,5-CVN锁定,6-单日密码锁定,7-密码错误次数累计达到上限锁定,8-密码失效',
  `CARD_MEDIUM_TYPE` char(1) DEFAULT NULL COMMENT '卡介质类型|卡介质类型|A-磁条卡,B-IC卡',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `APP_FLAG` char(1) DEFAULT NULL COMMENT '附属卡标志|附属卡标志|Y-是,N-否',
  `CARD_PB_UNION_FLAG` char(1) DEFAULT NULL COMMENT '卡折合一标志|卡折合一标志|Y-是,N-否',
  `SIGN_FLAG` char(1) DEFAULT NULL COMMENT '是否记名卡|是否记名卡|Y-是,N-否',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `CHANGE_CARD_NUM` int DEFAULT NULL COMMENT '换卡次数|换卡次数，若是同号换卡，记录换卡次数',
  `MAIN_CARD_NO` varchar(50) DEFAULT NULL COMMENT '主卡卡号|主卡卡号',
  `ISSUE_DATE` datetime DEFAULT NULL COMMENT '发行日期|发行日期',
  `VALID_FROM_DATE` datetime DEFAULT NULL COMMENT '有效期起始日期|有效期起始日期',
  `VALID_THRU_DATE` datetime DEFAULT NULL COMMENT '有效期截止日期|有效期截止日期',
  `TREAD_PWD` varchar(200) DEFAULT NULL COMMENT '交易密码|交易密码',
  `CARD_CVN` varchar(200) DEFAULT NULL COMMENT '卡片CVN信息|卡片CVN信息',
  `DAC_VALUE` varchar(200) DEFAULT NULL COMMENT 'DAC值防篡改加密|DAC值防篡改加密',
  `ISSUE_USER_ID` varchar(30) DEFAULT NULL COMMENT '发卡柜员|发卡柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `CARD_VOUCHER_STATUS` char(1) DEFAULT NULL COMMENT '卡凭证状态|用于描述预制卡/吉祥卡/同号换卡/自编卡当前卡库存的状态|0-已分配,1-未入库,2-已入库',
  `CARD_CVN_MAC` varchar(200) DEFAULT NULL COMMENT '卡cvn信息密文mac值|卡cvn信息密文mac值',
  `VALID_THRU_DATE_MAC` varchar(200) DEFAULT NULL COMMENT '卡有效期截止日期密文MAC值|卡有效期截止日期密文MAC值',
  `VALID_THRU_DATE_PWD` varchar(100) DEFAULT NULL COMMENT '卡有效期截止日期密文|卡有效期截止日期密文',
  `CARD_CVN2` varchar(200) DEFAULT NULL COMMENT '卡片CVN信息（存储的等效二磁信息，包含D的）|卡片CVN信息（存储的等效二磁信息，包含D的）',
  `CARD_CVN2_MAC` varchar(200) DEFAULT NULL COMMENT '卡CVN信息密文MAC值（存储等效二磁信息，包含D的）|卡CVN信息密文MAC值（存储等效二磁信息，包含D的）',
  PRIMARY KEY (`CARD_NO`),
  KEY `IDX_CD_CARD_PREV_1M` (`BATCH_JOB_NO`),
  KEY `IDX_CD_CARD_PREV_2M` (`APPLY_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='卡片基本前置信息表|记录还未出售给客户的信息，包含卡状态、机构等信息';

-- ----------------------------
-- Table structure for cd_make_card_reg
-- ----------------------------
DROP TABLE IF EXISTS `cd_make_card_reg`;
CREATE TABLE `cd_make_card_reg` (
  `BATCH_JOB_NO` varchar(50) DEFAULT NULL COMMENT '制卡文件批次号|制卡文件批次号',
  `APPLY_DATE` datetime NOT NULL COMMENT '申请日期|申请日期',
  `TRAN_BRANCH` varchar(50) NOT NULL COMMENT '交易机构|交易机构',
  `APPLY_NO` varchar(50) NOT NULL COMMENT '申请编号|申请编号',
  `CARD_APPLY_TYPE` char(1) DEFAULT NULL COMMENT '制卡申请类型|制卡申请类型|1-非记名卡批量制卡,2-记名卡批量制卡,3-记名卡单张制卡,4-补换卡申请',
  `CARD_NUM` int DEFAULT NULL COMMENT '制卡数量|制卡数量',
  `MAKE_CD_STATUS` char(1) DEFAULT NULL COMMENT '制卡状态|制卡状态|0-制卡申请完成(待生成制卡文件/清单),1-制卡文件/清单生成成功,2-制卡申请(异步),3-制卡文件/清单待生成(异步),4-制卡文件/清单生成失败,6-待入库(同批次制卡),7-部分入库(同批次制卡),8-卡凭证已入库,9-制卡申请撤销(同号换卡/自编卡)',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `LUCKY_CARD_FLAG` char(1) DEFAULT NULL COMMENT '是否吉祥卡|是否吉祥卡|Y-是,N-否',
  `MAKE_CARD_TYPE` char(1) DEFAULT NULL COMMENT '制卡类型|制卡类型|A-自编卡,N-非吉祥卡,V-吉祥卡,S-同号换卡',
  `AREA_CODE` varchar(5) DEFAULT NULL COMMENT '地区码|地区码',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `RECEIVE_FLAG` char(1) DEFAULT NULL COMMENT '签收标志|用于记录预约卡的签收状态|0-未签收,1-已签收',
  `GAIN_TYPE` char(1) DEFAULT NULL COMMENT '卡片领取方式|卡片领取方式|O-自取,M-邮寄 ',
  `MAKE_CARD_DATE` datetime DEFAULT NULL COMMENT '制卡日期|制卡日期',
  `PICK_TYPE` varchar(2) DEFAULT NULL COMMENT '选号类型|自选卡号类型标志|01-内部选号,02-普通选号',
  `AUTH_USER_ID` varchar(30) DEFAULT NULL COMMENT '授权柜员|授权柜员',
  PRIMARY KEY (`APPLY_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='制卡申请登记簿|登记申请制卡的信息，包含数量、类型、柜员、制卡状态、是否吉祥卡等信息';

-- ----------------------------
-- Table structure for cd_make_card_split_msg
-- ----------------------------
DROP TABLE IF EXISTS `cd_make_card_split_msg`;
CREATE TABLE `cd_make_card_split_msg` (
  `APPLY_NO` varchar(50) NOT NULL COMMENT '申请编号|申请编号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `START_NO` varchar(50) DEFAULT NULL COMMENT '起始号码数字串|起始号码，以数字类型存储',
  `END_NO` varchar(50) DEFAULT NULL COMMENT '终止号码数字串|终止号码，以数字类型存储',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `CARD_NUM` int DEFAULT NULL COMMENT '制卡数量|制卡数量',
  `CARD_VOUCHER_STATUS` char(1) DEFAULT NULL COMMENT '卡凭证状态|用于描述预制卡/吉祥卡/同号换卡/自编卡当前卡库存的状态|0-已分配,1-未入库,2-已入库',
  PRIMARY KEY (`APPLY_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='卡片分段信息表|用于存储存款预制卡申请的分段信息';

-- ----------------------------
-- Table structure for cd_safe_lock
-- ----------------------------
DROP TABLE IF EXISTS `cd_safe_lock`;
CREATE TABLE `cd_safe_lock` (
  `INTERNAL_KEY` bigint DEFAULT NULL COMMENT '账户内部键值|账户内部键值',
  `CARD_NO` varchar(50) NOT NULL COMMENT '卡号|卡号',
  `UNION_PAY_LOCK` varchar(2) DEFAULT NULL COMMENT '银联快捷支付锁|银联快捷支付锁',
  `UNION_PAY_LOCK_START_TIME` varchar(10) DEFAULT NULL COMMENT '银联快捷支付锁开始时间|银联快捷支付锁开始时间',
  `UNION_PAY_LOCK_END_TIME` varchar(10) DEFAULT NULL COMMENT '银联快捷支付锁结束时间|银联快捷支付锁结束时间',
  `THIRD_PAY_LOCK` varchar(2) DEFAULT NULL COMMENT '第三方支付锁|第三方支付锁',
  `THIRD_PAY_LOCK_START_TIME` varchar(10) DEFAULT NULL COMMENT '第三方支付锁开始时间|第三方支付锁开始时间',
  `THIRD_PAY_LOCK_END_TIME` varchar(10) DEFAULT NULL COMMENT '第三方支付锁结束时间|第三方支付锁结束时间',
  `CROSS_BORDER_LOCK` varchar(2) DEFAULT NULL COMMENT '跨境锁|跨境锁',
  `FRE_USED_ADDR_LOCK` varchar(2) DEFAULT NULL COMMENT '常用地锁|常用地锁',
  `FRE_USED_ADDRESS1` varchar(200) DEFAULT NULL COMMENT '常用地1|常用地1',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `UNION_PAY_LOCK_AUTH` varchar(10) DEFAULT NULL COMMENT '银联支付权限种类|银联支付权限种类',
  `UNION_PAY_LOCK_AMT_CCY` varchar(10) DEFAULT NULL COMMENT '银联支付币种|银联支付币种',
  `UNION_PAY_LOCK_AMT` decimal(17,2) DEFAULT NULL COMMENT '银联支付金额|银联支付金额',
  `THIRD_PAY_LOCK_AUTH` varchar(10) DEFAULT NULL COMMENT '三方支付权限种类|三方支付权限种类',
  `THIRD_PAY_LOCK_AMT_CCY` varchar(10) DEFAULT NULL COMMENT '三方支付锁币种|三方支付锁币种',
  `THIRD_PAY_LOCK_AMT` decimal(17,2) DEFAULT NULL COMMENT '三方支付锁金额|三方支付锁金额',
  `CROSS_BORDER_LOCK_AUTH` varchar(10) DEFAULT NULL COMMENT '跨境权限种类|跨境权限种类',
  `CROSS_BORDER_LOCK_AMT_CCY` varchar(10) DEFAULT NULL COMMENT '跨境币种|跨境币种',
  `CROSS_BORDER_LOCK_AMT` decimal(17,2) DEFAULT NULL COMMENT '跨境金额|跨境金额',
  `FRE_USED_ADDR_LOCK_AUTH` varchar(10) DEFAULT NULL COMMENT '常用地锁权限种类|常用地锁权限种类',
  `FRE_USED_ADDR_AMT_CCY` varchar(10) DEFAULT NULL COMMENT '常用地锁币种|常用地锁币种',
  `FRE_USED_ADDR_LOCK_AMT` decimal(17,2) DEFAULT NULL COMMENT '常用地权限金额|常用地权限金额',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`CARD_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='卡安全锁|卡安全锁';

-- ----------------------------
-- Table structure for cd_seq_skip
-- ----------------------------
DROP TABLE IF EXISTS `cd_seq_skip`;
CREATE TABLE `cd_seq_skip` (
  `INTERNAL_KEY` bigint NOT NULL COMMENT '账户内部键值|账户内部键值',
  `CARD_BIN` varchar(10) DEFAULT NULL COMMENT '卡bin|卡bin，用来区分借记卡、信用卡的前缀',
  `START_NO` varchar(50) DEFAULT NULL COMMENT '起始号码数字串|起始号码，以数字类型存储',
  `END_NO` varchar(50) DEFAULT NULL COMMENT '终止号码数字串|终止号码，以数字类型存储',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`INTERNAL_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='制卡跳号号段信息表';

-- ----------------------------
-- Table structure for cfca_risk_black_deal_info
-- ----------------------------
DROP TABLE IF EXISTS `cfca_risk_black_deal_info`;
CREATE TABLE `cfca_risk_black_deal_info` (
  `SND_ORG` varchar(10) NOT NULL COMMENT '发送机构|发送机构',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `DEAL_RESULT` varchar(200) DEFAULT NULL COMMENT '处理结果|处理结果',
  `MSG_DESC` varchar(200) DEFAULT NULL COMMENT '返回码描述|返回码描述',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `BANK_ID` varchar(20) DEFAULT NULL COMMENT '银行ID|银行ID',
  `PS_LNKM` varchar(100) DEFAULT NULL COMMENT '公安联系人|公安联系人',
  `OPER_SIGN` varchar(2) DEFAULT NULL COMMENT '操作符|操作符',
  `BLKL_TYP` varchar(10) NOT NULL COMMENT '黑名单类型|黑名单类型',
  `VALID_DT` varchar(30) DEFAULT NULL COMMENT '有效期|有效期',
  `OPEN_CARD_TM` varchar(10) DEFAULT NULL COMMENT '开卡时间|开卡时间',
  `DATA_TYP` varchar(100) NOT NULL COMMENT '数据类型|数据类型',
  `PS_LNKM_TEL` varchar(100) DEFAULT NULL COMMENT '公安联系人电话|公安联系人电话',
  `VERIFY_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '发送机构审核通过黑名单时间戳|发送机构审核通过黑名单时间戳',
  `LIST_COMM` varchar(100) DEFAULT NULL COMMENT '名单说明|名单说明',
  `VCHR_NUM` varchar(100) NOT NULL COMMENT '凭证号|凭证号',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SND_ORG`,`DATA_TYP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数仓黑名单文件处理信息表|数仓黑名单文件处理信息表';

-- ----------------------------
-- Table structure for cfca_risk_black_info
-- ----------------------------
DROP TABLE IF EXISTS `cfca_risk_black_info`;
CREATE TABLE `cfca_risk_black_info` (
  `DATA_TYP` varchar(100) NOT NULL COMMENT '数据类型|数据类型',
  `VCHR_NUM` varchar(100) NOT NULL COMMENT '凭证号|凭证号',
  `OPER_SIGN` varchar(2) NOT NULL COMMENT '操作符|操作符',
  `SND_ORG` varchar(10) DEFAULT NULL COMMENT '发送机构|发送机构',
  `BLKL_TYP` varchar(10) DEFAULT NULL COMMENT '黑名单类型|黑名单类型',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `BANK_ID` varchar(20) DEFAULT NULL COMMENT '银行ID|银行ID',
  `PS_LNKM` varchar(100) DEFAULT NULL COMMENT '公安联系人|公安联系人',
  `PS_LNKM_TEL` varchar(100) DEFAULT NULL COMMENT '公安联系人电话|公安联系人电话',
  `OPEN_CARD_TM` varchar(10) DEFAULT NULL COMMENT '开卡时间|开卡时间',
  `VALID_DT` varchar(30) DEFAULT NULL COMMENT '有效期|有效期',
  `LIST_COMM` varchar(100) DEFAULT NULL COMMENT '名单说明|名单说明',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `VERIFY_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '发送机构审核通过黑名单时间戳|发送机构审核通过黑名单时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`DATA_TYP`,`VCHR_NUM`,`OPER_SIGN`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='诈骗风险交易名单报文';

-- ----------------------------
-- Table structure for flyway_schema_history
-- ----------------------------
DROP TABLE IF EXISTS `flyway_schema_history`;
CREATE TABLE `flyway_schema_history` (
  `installed_rank` int NOT NULL,
  `version` varchar(50) DEFAULT NULL,
  `description` varchar(200) NOT NULL,
  `type` varchar(20) NOT NULL,
  `script` varchar(1000) NOT NULL,
  `checksum` int DEFAULT NULL,
  `installed_by` varchar(100) NOT NULL,
  `installed_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `execution_time` int NOT NULL,
  `success` tinyint(1) NOT NULL,
  PRIMARY KEY (`installed_rank`),
  KEY `flyway_schema_history_s_idx` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- ----------------------------
-- Table structure for fm_acct_exec
-- ----------------------------
DROP TABLE IF EXISTS `fm_acct_exec`;
CREATE TABLE `fm_acct_exec` (
  `ACCT_EXEC` varchar(30) NOT NULL COMMENT '客户经理|客户经理',
  `ACCT_EXEC_NAME` varchar(200) NOT NULL COMMENT '客户经理姓名|客户经理姓名',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `ISS_COUNTRY` varchar(3) DEFAULT NULL COMMENT '发证国家|发证国家',
  `ACCT_EXEC_TYPE` varchar(3) DEFAULT NULL COMMENT '客户经理类型|客户经理类型',
  `ACCT_EXEC_STATUS` char(1) DEFAULT NULL COMMENT '客户经理状态|客户经理状态|A-活动,C-失效',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `COLLAT_MGR_IND` char(1) DEFAULT NULL COMMENT '是否担保经理|是否担保经理|Y-是,N-否',
  `CONTACT_ID` varchar(100) DEFAULT NULL COMMENT '联系类型ID|联系类型',
  `CONTACT_TEL` varchar(50) DEFAULT NULL COMMENT '联系电话  |联系电话  ',
  `MANAGER` varchar(30) DEFAULT NULL COMMENT '主管经理|主管经理',
  `PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '利润中心 |利润中心 ',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`ACCT_EXEC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户经理表|客户经理表';

-- ----------------------------
-- Table structure for fm_backup_clean_param
-- ----------------------------
DROP TABLE IF EXISTS `fm_backup_clean_param`;
CREATE TABLE `fm_backup_clean_param` (
  `BASE_TABLE_NAME` varchar(100) NOT NULL COMMENT '业务表|业务表',
  `HIST_TABLE_NAME` varchar(100) DEFAULT NULL COMMENT '历史表|历史表',
  `FREQUENCY` varchar(20) DEFAULT NULL COMMENT '对应频率|对应频率',
  `NEXT_DATE` datetime DEFAULT NULL COMMENT '下一日期|下一处理日期',
  `BACKUP_TYPE` varchar(10) DEFAULT NULL COMMENT '备份类型（01-分区备份、02-条件备份、03-不备份）|备份类型（01-分区备份、02-条件备份、03-不备份）',
  `BACKUP_SEGMENT_FLAG` varchar(10) DEFAULT NULL COMMENT '备份分段(Y-是、N-否)|备份分段(Y-是、N-否)',
  `CLEAN_TYPE` varchar(10) DEFAULT NULL COMMENT '清理类型(01-分区清理、02-条件清理、03-不清理）|清理类型(01-分区清理、02-条件清理、03-不清理）',
  `CLEAN_SEGMENT_FLAG` varchar(10) DEFAULT NULL COMMENT '清理分段(Y-是、N-否)|清理分段(Y-是、N-否)',
  `FIELD_NAME` varchar(50) DEFAULT NULL COMMENT '字段名|字段名',
  `SEGMENT_SIZE` int DEFAULT NULL COMMENT '分段大小|分段大小',
  `BACKUP_SQL_ID` varchar(50) DEFAULT NULL COMMENT '备份SQLID|备份SQLID',
  `CLEAN_SQL_ID` varchar(50) DEFAULT NULL COMMENT '清理SQLID|清理SQLID',
  `BACKUP_STATUS` varchar(10) DEFAULT NULL COMMENT '备份状态(R – 备份中  E –备份结束  N-未开始)|备份状态(R – 备份中  E –备份结束)',
  `CLEAN_STATUS` varchar(2) DEFAULT NULL COMMENT '结清标志|结清标志',
  `BACKUP_START_TIME` datetime DEFAULT NULL COMMENT '备份起始时间|备份起始时间',
  `BACKUP_END_TIME` datetime DEFAULT NULL COMMENT '备份结束时间|备份结束时间',
  `CLEAN_START_TIME` datetime DEFAULT NULL COMMENT '清理起始时间|清理起始时间',
  `CLEAN_END_TIME` datetime DEFAULT NULL COMMENT '清理结束时间|清理结束时间',
  `BACKUP_SEGMENT_ID` varchar(100) DEFAULT NULL COMMENT '备份分段ID|备份分段ID',
  `CLEAN_SEGMENT_ID` varchar(100) DEFAULT NULL COMMENT '清理分段ID|清理分段ID',
  `CLASS_NAME` varchar(200) DEFAULT NULL COMMENT '分段映射实体类名|分段映射实体类名',
  `RETAIN_TIME_LIMIT` varchar(20) DEFAULT NULL COMMENT '保留期限|保留期限',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BASE_TABLE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='备份清理参数配置表|配置核心数据库大表备份和清理的周期，处理方式，留存期限等参数';

-- ----------------------------
-- Table structure for fm_branch
-- ----------------------------
DROP TABLE IF EXISTS `fm_branch`;
CREATE TABLE `fm_branch` (
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `BRANCH_NAME` varchar(200) NOT NULL COMMENT '机构名称|机构名称',
  `BRANCH_SHORT` varchar(30) NOT NULL COMMENT '机构简称|机构简称',
  `BRANCH_TYPE` char(1) NOT NULL COMMENT '机构类型|区分机构是行内还是行外，目前默认为I-本行机构|I-本行,O-他行',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_BR_IND` char(1) DEFAULT NULL COMMENT '是否交易机构|只有交易行才能开门办理业务，接收上送报文请求，否则只能在核算和报表场景中使用；已被撤并的机构此字段也登记为N|Y-交易行,N-非交易行',
  `INTERNAL_CLIENT` varchar(20) DEFAULT NULL COMMENT '内部客户号|默认按机构编号在cif应用注册一套客户信息，用户核心系统内部业务场景使用，ecif无需管控，有OM初始化导入，后续机构新增时也会自动创建一套客户信息',
  `HIERARCHY_CODE` varchar(50) DEFAULT NULL COMMENT '层级代码|机构的总分支级别',
  `ATTACHED_TO` varchar(50) DEFAULT NULL COMMENT '所属上级|机构的上级管理机构',
  `SUB_BRANCH_CODE` varchar(50) DEFAULT NULL COMMENT '分行代码|默认配置机构编号前三位，业务系统实际暂未使用',
  `BRANCH_STATUS` char(1) DEFAULT NULL COMMENT '机构开关门状态|机构开关门状态|I-开门,O-关门,X-已撤并',
  `FTA_FLAG` char(1) DEFAULT NULL COMMENT '是否自贸区机构|是否自贸区机构|Y-是,N-否',
  `FTA_CODE` varchar(10) DEFAULT NULL COMMENT '自贸区代码|自贸区代码',
  `LOCAL_CCY` varchar(3) DEFAULT NULL COMMENT '当地币种|机构当地默认的币种，一般为人民币',
  `BASE_CCY` varchar(3) DEFAULT NULL COMMENT '基础币种|机构在系统中的默认基础币种，一般为人民币',
  `CCY_CTRL_BRANCH` varchar(50) DEFAULT NULL COMMENT '结售汇平盘机构|结售汇平盘机构',
  `CHEQUE_ISSUING_BRANCH_FLAG` char(1) DEFAULT NULL COMMENT '是否签发支票|机构是否允许签发出售支票|Y-支票发行行,N-非支票发行',
  `INT_TAX_LEVY` char(1) NOT NULL COMMENT '利息税征收标志|利息税征收标志|Y-是,N-否',
  `TAX_RPT_BRANCH` varchar(50) DEFAULT NULL COMMENT '税收机构（总账用）|税收机构（总账用）',
  `SURTAX_TYPE` varchar(30) DEFAULT NULL COMMENT '附加税类型|附加税的税率类型',
  `PBOC_FUND_CHECK_FLAG` char(1) DEFAULT NULL COMMENT '人行备付金检查标志|是否检查人行备付金余额，目前系统暂未使用|Y-检查余额,N-不检查余额',
  `PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '利润中心 |利润中心',
  `IP_ADDR` varchar(200) DEFAULT NULL COMMENT '机构IP地址|机构下所有终端的IP地址，英文逗号分隔，目前系统暂未使用',
  `COUNTRY` varchar(3) NOT NULL COMMENT '国家|国家',
  `STATE` varchar(10) NOT NULL COMMENT '省别代码|省、州',
  `CITY` varchar(10) DEFAULT NULL COMMENT '城市|城市',
  `POSTAL_CODE` varchar(10) DEFAULT NULL COMMENT '邮政编码|邮政编码',
  `DISTRICT` varchar(10) DEFAULT NULL COMMENT '区号|区号',
  `AREA_CODE` varchar(5) DEFAULT NULL COMMENT '地区码|地区码',
  `CNY_BUSINESS_UNIT` varchar(10) DEFAULT NULL COMMENT '账套(人民币)|账套(人民币)，目前系统暂未使用',
  `HKD_BUSINESS_UNIT` varchar(10) DEFAULT NULL COMMENT '账套(港币)|账套(港币)，目前系统暂未使用',
  `FX_ORGAN_CODE` varchar(10) DEFAULT NULL COMMENT '外汇金融机构代码|外汇金融机构代码，目前系统暂未使用',
  `CREATE_DATE` datetime DEFAULT NULL COMMENT '创建日期|机构成立创建的日期，不大于正式开门营业的日期，仅登记',
  `START_DATE` datetime NOT NULL COMMENT '开始日期|机构参数启用开始日期，也是机构正式开门营业的日期',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|机构参数失效日期，为空默认永不失效，一般在机构撤并时会给被撤并机构设置此日期',
  `EOD_FLAG` char(1) DEFAULT NULL COMMENT '日终标识|表示当前机构是否处于日终状态，目前系统暂未使用|Y-日终状态,N-营业状态',
  `DEFAULT_TELLER_LOGIN` char(1) NOT NULL COMMENT '默认柜员登录认证方式|当前机构下柜员新增时默认的登录方式，目前系统暂未使用|1-密码,2-指纹,3-密码+指纹',
  `ABNORMAL_OPEN_CONTROL` char(1) DEFAULT NULL COMMENT '非正常时间开门控制方式|定义机构在非正常时间开门时的控制方式，目前系统暂未使用|1-不控制,2-拒绝,3-提醒',
  `OPER_MAX_LEVEL` varchar(5) DEFAULT NULL COMMENT '操作最高级别|目前系统暂未使用',
  `AUTH_FLAG` char(1) DEFAULT NULL COMMENT '授权标志|用于机构二次开门时，需要先授权，授权后此标志修改为Y，开门接口就可以正常开门了，目前系统暂未使用|Y-已授权,N-未授权',
  `TAILBOX_DETACH_FLAG` char(1) NOT NULL COMMENT '尾箱控制方式|柜员正式签退和网点日终时，对于没有脱离绑定关系的尾箱，根据此配置默认进行脱离处理|Y-必须上缴,N-不上缴,O-不控制',
  `VOUCHER_USER_CONTRAL_FLAG` char(1) DEFAULT NULL COMMENT '是否限制凭证入库柜员|行外和机构间凭证出入库时，是否必须由凭证库管操作|Y-限制,N-不限制',
  `ACCOUNTING_BRANCH_FLAG` char(1) DEFAULT NULL COMMENT '是否核算机构|当前机构是否是核算机构',
  `ACCOUNTING_BRANCH` varchar(50) DEFAULT NULL COMMENT '核算机构|当前机构对应的核算机构编号，可以是自己',
  `AUTO_CREATE_INTERNAL_ACCT_FLAG` char(1) DEFAULT NULL COMMENT '自动开立内部户标志|自动开立内部户标志|Y-是,N-否',
  `NORMAL_OPEN_TIME` varchar(8) DEFAULT NULL COMMENT '正常开门时间|配置机构在此时间之前不能对外开门营业，对应JAVA类型：HH:mm:ss，举例：13:25:45，目前系统暂未使用',
  `NORMAL_CLOSE_TIME` varchar(8) DEFAULT NULL COMMENT '正常关门时间|配置机构在此时间后不能再对外营业，对应JAVA类型：HH:mm:ss，举例：13:25:45，目前系统暂未使用',
  `CITY_BRANCH_FLAG` char(1) NOT NULL COMMENT '市区支行标志|市区支行标志|Y-是,N-否',
  `PBOC_FINANCING_NO` varchar(30) DEFAULT NULL COMMENT '人行金融机构编码|人行金融机构编码，目前系统暂未使用',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `BIC_CODE` varchar(10) DEFAULT NULL COMMENT 'BIC代码|BIC代码',
  PRIMARY KEY (`BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构信息表|机构信息表';

-- ----------------------------
-- Table structure for fm_branch_status_detail
-- ----------------------------
DROP TABLE IF EXISTS `fm_branch_status_detail`;
CREATE TABLE `fm_branch_status_detail` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `REG_TYPE` char(1) DEFAULT NULL COMMENT '登记类型|登记类型|1-开户/卡,2-销户/卡',
  `REG_VALUE` varchar(50) DEFAULT NULL COMMENT '登记值|登记值',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构状态历史登记明细表|登记机构开关门历史记录';

-- ----------------------------
-- Table structure for fm_client_out_info
-- ----------------------------
DROP TABLE IF EXISTS `fm_client_out_info`;
CREATE TABLE `fm_client_out_info` (
  `OK_FLAG` varchar(1) DEFAULT NULL COMMENT '是否已完成|是否已完成|Y-是,N-否 ',
  `BUSI_DATA` blob COMMENT '业务数据|业务数据',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `CLASS_NAME` varchar(200) DEFAULT NULL COMMENT '分段映射实体类名|分段映射实体类名',
  `CHANGE_DATE` datetime DEFAULT NULL COMMENT '交换日期|交换日期',
  `OLD_CLIENT_NO` varchar(20) NOT NULL COMMENT '原客户号|原客户号',
  `TABLE_NAME` varchar(50) DEFAULT NULL COMMENT '表名|表名',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|新客户号',
  `MERGE_NO` varchar(50) NOT NULL COMMENT '合并编号|合并编号',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户信息迁出表|客户信息迁出表';

-- ----------------------------
-- Table structure for fm_data_storage_reg
-- ----------------------------
DROP TABLE IF EXISTS `fm_data_storage_reg`;
CREATE TABLE `fm_data_storage_reg` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `DEAL_FLAG` char(1) DEFAULT NULL COMMENT '处理标识|处理标识|1-未处理  ,2-已处理 ',
  `DEAL_DATE` datetime NOT NULL COMMENT '处理日期|处理日期',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`TABLE_NAME`,`DEAL_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载入库文件处理结果表';

-- ----------------------------
-- Table structure for fm_data_unload_param
-- ----------------------------
DROP TABLE IF EXISTS `fm_data_unload_param`;
CREATE TABLE `fm_data_unload_param` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `MODULE_NAME` varchar(50) DEFAULT NULL COMMENT '模块名称|模块名称',
  `TARGET_SYSTEM` varchar(50) DEFAULT NULL COMMENT '目标系统|目标系统',
  `FREQUENCE` varchar(5) DEFAULT NULL COMMENT '频率|频率',
  PRIMARY KEY (`TABLE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载参数配置表|用于数据据卸载参数配置';

-- ----------------------------
-- Table structure for fm_lang_translation
-- ----------------------------
DROP TABLE IF EXISTS `fm_lang_translation`;
CREATE TABLE `fm_lang_translation` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `TRANS_COLUMN` varchar(50) NOT NULL COMMENT '国际化字段|国际化字段',
  `TRANS_COLUMN_VALUE` varchar(500) DEFAULT NULL COMMENT '国际化字段取值|国际化字段取值',
  `BUSI_KEY` varchar(200) DEFAULT NULL COMMENT '业务主键|业务主键',
  `BUSI_KEY_VALUE` varchar(500) NOT NULL COMMENT '业务主键取值|业务主键取值',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `LANGUAGE` varchar(10) NOT NULL COMMENT '语言|语言',
  PRIMARY KEY (`TABLE_NAME`,`TRANS_COLUMN`,`BUSI_KEY_VALUE`,`LANGUAGE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='业务参数多语言码值定义表|保存业务参数表中国际化字段在多语言中的取值信息';

-- ----------------------------
-- Table structure for fm_run_date_notice
-- ----------------------------
DROP TABLE IF EXISTS `fm_run_date_notice`;
CREATE TABLE `fm_run_date_notice` (
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `SWITCH_YN` char(1) NOT NULL COMMENT '开关|是否为外部日期接受|Y-开,N-关 ',
  `NEXT_RUN_DATE` datetime DEFAULT NULL COMMENT '下一运行日|下一运行日',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`TRAN_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='营业日期通知表|营业日期通知表';

-- ----------------------------
-- Table structure for fm_service_access_conf
-- ----------------------------
DROP TABLE IF EXISTS `fm_service_access_conf`;
CREATE TABLE `fm_service_access_conf` (
  `SERVICE_ID` varchar(30) NOT NULL COMMENT '服务ID|服务ID',
  `AGENT_ACCOUNT_FLAG` char(1) DEFAULT NULL COMMENT '代理记账标志|接口是否支持柜员代理记账|Y-支持,N-不支持',
  PRIMARY KEY (`SERVICE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='代理记账可访问的接口配置表|代理记账可访问的接口配置表';

-- ----------------------------
-- Table structure for fm_sys_lang_translation
-- ----------------------------
DROP TABLE IF EXISTS `fm_sys_lang_translation`;
CREATE TABLE `fm_sys_lang_translation` (
  `LANGUAGE` varchar(10) NOT NULL COMMENT '语言|语言',
  `TRANS_COLUMN` varchar(50) NOT NULL COMMENT '国际化字段|国际化字段',
  `BUSI_KEY` varchar(200) NOT NULL COMMENT '业务主键|业务主键',
  `TRANS_COLUMN_VALUE` varchar(500) DEFAULT NULL COMMENT '国际化字段取值|国际化字段取值',
  PRIMARY KEY (`LANGUAGE`,`TRANS_COLUMN`,`BUSI_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='系统参数多语言码值定义表|保存系统参数表中国际化字段在多语言中的取值信息';

-- ----------------------------
-- Table structure for fm_system
-- ----------------------------
DROP TABLE IF EXISTS `fm_system`;
CREATE TABLE `fm_system` (
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|系统运行默认法人编号',
  `COY_NAME` varchar(50) NOT NULL COMMENT '银行全称|银行全称',
  `COY_SHORT` varchar(50) NOT NULL COMMENT '银行简称|银行简称',
  `DEFAULT_BRANCH` varchar(50) DEFAULT NULL COMMENT '默认机构|默认机构，一般为默认法人的总行清算中心',
  `LAST_RUN_DATE` datetime DEFAULT NULL COMMENT '上一运行日期|上一运行日期',
  `RUN_DATE` datetime NOT NULL COMMENT '运行日期|系统当前运行会计日期',
  `NEXT_RUN_DATE` datetime NOT NULL COMMENT '下一运行日|下一运行日',
  `MTH_END_DATE` datetime NOT NULL COMMENT '本月月末日期|本月月末日期',
  `QUR_END_DATE` datetime NOT NULL COMMENT '季末日期|季末日期',
  `HALF_END_DATE` datetime NOT NULL COMMENT '半年末日期|半年末日期',
  `YR_END_DATE` datetime NOT NULL COMMENT '本年年末日期|本年年末日期',
  `MAIN_BRANCH_CODE` varchar(50) DEFAULT NULL COMMENT '总行层级代码|总行层级代码，目前系统暂未使用',
  `HEAD_OFFICE_CLIENT` varchar(20) DEFAULT NULL COMMENT '总行清算行内部客户|总行清算行内部客户，目前系统暂未使用',
  `SYSTEM_PHASE` varchar(3) NOT NULL COMMENT '系统所处的阶段|当前系统所处的运行阶段|INP-日间,EOD-日终,SOD-日始',
  `AUTO_CLIENT_GEN_FLAG` char(1) DEFAULT NULL COMMENT '是否自动生成客户号|是否自动生成客户号|Y-是,N-否',
  `CLIENT_NO_STRUCTURE_TYPE` varchar(3) DEFAULT NULL COMMENT '客户号结构类型|客户号结构类型，目前系统暂未使用',
  `AUTO_COLL_GEN_FLAG` char(1) DEFAULT NULL COMMENT '是否自动生成抵质押编号|是否自动生成抵质押编号|Y-是,N-否',
  `AUTO_LOCK_BL_CLIENT_FLAG` char(1) DEFAULT NULL COMMENT '自动冻结黑名单客户|自动冻结黑名单客户|Y-是,N-否',
  `MULTI_CORPORATION_FLAG` char(1) DEFAULT NULL COMMENT '是否多法人系统|是否多法人系统|Y-是,N-否',
  `MULTI_CORPORATION_METHOD` char(1) DEFAULT NULL COMMENT '多法人机构间清算方式|多法人机构间清算方式|I-系统内清算模式,P-支付清算模式,N-不允许法人间通存通兑',
  `MULTI_ALL_DEP_FLAG` char(1) DEFAULT NULL COMMENT '法人间通存标志|法人间通存标志',
  `MULTI_ALL_DRA_FLAG` char(1) DEFAULT NULL COMMENT '法人间通兑标志|法人间通兑标志',
  `DEP_DRA_TRAN_BRANCH_FLAG` char(1) DEFAULT NULL COMMENT '通存通兑是否过交易行|目前用作跨法人清算时是否过交易所在法人',
  `LOCAL_CCY` varchar(3) DEFAULT NULL COMMENT '当地币种|当地币种',
  `BASE_CCY` varchar(3) DEFAULT NULL COMMENT '基础币种|基础币种',
  `REPORT_CCY` varchar(3) DEFAULT NULL COMMENT '报表币种|报表币种，目前系统暂未使用',
  `LIMIT_CCY` varchar(3) DEFAULT NULL COMMENT '限制币种|限制币种，目前系统暂未使用',
  `DEFAULT_CHARGE_RATE_TYPE` varchar(10) DEFAULT NULL COMMENT '结售汇内部平盘汇率类型|结售汇内部平盘汇率类型',
  `DEFAULT_PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '默认利润中心|默认利润中心',
  `DEFAULT_RATE_TYPE` varchar(10) DEFAULT NULL COMMENT '本地币种汇率类型|本地币种汇率类型',
  `DEFAULT_RATE_TYPE_LOCAL` varchar(10) DEFAULT NULL COMMENT '默认本地汇率类型|默认本地汇率类型',
  `ALLOW_BACKQRY_DAY` int DEFAULT NULL COMMENT '允许查询的历史天数|允许查询的历史天数',
  `BATCH_CHECK_FLAG` char(1) DEFAULT NULL COMMENT '批处理检查标志|批处理检查标志|Y -是,N -否',
  `BATCH_DEFAULT_USER_ID` varchar(30) DEFAULT NULL COMMENT '默认批处理用户|批处理期间系统自动交易的交易柜员',
  `BATCH_MODULE` varchar(2) DEFAULT NULL COMMENT '当前批处理的模块号|当前批处理的模块号，目前系统暂未使用|RB-存款 ,CL-贷款 ,GL-总账',
  `BATCH_UNIT` varchar(50) DEFAULT NULL COMMENT '当前批处理的业务组编号|当前批处理的业务组编号，目前系统暂未使用',
  `CAPITAL_FUNDS` decimal(17,2) DEFAULT NULL COMMENT '投资资金|投资资金',
  `CLIENT_BLOCK_FLAG` char(1) DEFAULT NULL COMMENT '资料不全客户冻结标志|客户资料不全时是否增加客户冻结|Y-是,N-否',
  `CONTINUOUS_RUN` char(1) DEFAULT NULL COMMENT '是否连续使用指定的数字区间标志|是否连续使用指定的数字区间标志，目前系统暂未使用|Y-是,N-否',
  `CR_DR_CHECK_FLAG` char(1) DEFAULT NULL COMMENT '借贷检查标志|借贷检查标志，目前系统暂未使用|Y-是,N-否',
  `EBH_BRANCH` varchar(50) DEFAULT NULL COMMENT '电子银行机构|电子银行机构，目前系统暂未使用',
  `EXCHANGE_RATE_VARIANCE` decimal(5,2) DEFAULT NULL COMMENT '汇率浮动百分比|汇率浮动百分比，目前系统暂未使用',
  `INTERNAL_RATE_CHARGE_FLAG` char(1) DEFAULT NULL COMMENT '是否行内结售汇平盘|是否行内结售汇平盘|Y-是,N-否',
  `INTER_BRANCH_ACCT_HO` varchar(20) DEFAULT NULL COMMENT '分行间清算科目-同业存放|分行间清算科目-同业存放',
  `INTER_BRANCH_IND` char(1) DEFAULT NULL COMMENT '是否为内部银行|是否为内部银行|Y-是,N-否',
  `GAP_TYPE` varchar(20) DEFAULT NULL COMMENT '敞口类型|敞口类型',
  `PROCESS_SPLIT_IND` char(1) NOT NULL COMMENT '批处理阶段标志|系统当前是否处于批处理阶段|Y-批处理阶段,N-非批处理阶段',
  `PRODUCT30E_FLAG` char(1) DEFAULT NULL COMMENT '是否产品版30E计算天数|是否产品版30E计算天数|Y-是使用产品版30E计算天数,N-否使用成都版30E计算天数',
  `GL_IND` char(1) NOT NULL COMMENT 'SYMBOLS总账分离标志|SYMBOLS总账分离标志，目前系统暂未使用|Y-使用SYMBOLS总账,N-不使用SYMBOLS总账',
  `DAC_IND` char(1) DEFAULT NULL COMMENT 'DAC校验标志|DAC校验标志|Y-使用DAC校验,N-不使用DAC校验',
  `IS_DEBUG` char(1) DEFAULT NULL COMMENT '是否记录业务数据信息|是否记录业务数据信息|Y-是,N-否',
  `IS_ERROR` char(1) DEFAULT NULL COMMENT '是否记录出错时的业务数据信息|是否记录出错时的业务数据信息|Y-是,N-否',
  `RB_RESTRAINT_TYPE` varchar(3) DEFAULT NULL COMMENT '默认存款账户限制类型|默认存款账户限制类型',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `DEFAULT_LANGUAGE` varchar(50) DEFAULT NULL COMMENT '默认语言标志|默认语言标志',
  PRIMARY KEY (`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='系统业务参数表|系统业务参数表';

-- ----------------------------
-- Table structure for fm_unload_conf
-- ----------------------------
DROP TABLE IF EXISTS `fm_unload_conf`;
CREATE TABLE `fm_unload_conf` (
  `SYSTEM_ID` varchar(20) NOT NULL COMMENT '系统ID|系统ID',
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `UNLOAD_FLAG` char(1) NOT NULL COMMENT '是否需要卸数|是否需要卸数|1-卸数,0-不卸数',
  `SCREEN_CONDITION` varchar(500) NOT NULL COMMENT '数据筛选条件|查询卸载数据的筛选条件',
  `NODE_ID` varchar(50) DEFAULT NULL COMMENT '数据库节点ID|对于全局参数来说，只需要卸载一个分片库的数据时，这里配上对应的数据源；不配置的话此张表涉及到的数据源都会卸载。',
  `SEGMENT_FLAG` char(1) NOT NULL COMMENT '针对大表，是否需要分段|1-分段,0-不分段',
  `SEGMENT_FIELD` varchar(50) DEFAULT NULL COMMENT '分段字段|分段字段，当SEGMENT_FLAG为1时生效',
  `SEGMENT_SIZE` int DEFAULT NULL COMMENT '分段大小|分段大小',
  `PAGE_SIZE` int DEFAULT NULL COMMENT '分页大小|分页大小',
  `FILE_MERGE_FLAG` char(1) DEFAULT NULL COMMENT '联合贷是否合并文件标志|文件是否合并|Y-是,N-否',
  `FILE_SEGMENT_SIZE` int DEFAULT NULL COMMENT '文件分段大小|文件分段大小',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`TABLE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载参数配置表|数据卸载参数配置';

-- ----------------------------
-- Table structure for fm_unload_file_result
-- ----------------------------
DROP TABLE IF EXISTS `fm_unload_file_result`;
CREATE TABLE `fm_unload_file_result` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `FILE_NAME` varchar(200) NOT NULL COMMENT '文件名称|文件名称',
  `SEGMENT_START` varchar(50) NOT NULL COMMENT '大段起始值|大段起始值',
  `SEGMENT_END` varchar(50) NOT NULL COMMENT '大段终止值|大段终止值',
  `SEGMENT_SIZE` int NOT NULL COMMENT '分段大小|分段大小',
  `DEAL_DATE` datetime NOT NULL COMMENT '处理日期|处理日期',
  `DEAL_RESULT_FLAG` char(1) NOT NULL COMMENT '处理结果标志|处理结果标志|1-未处理,2-已处理,3-处理中,4-处理失败',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`FILE_NAME`,`SEGMENT_START`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载文件结果表|数据卸载文件结果表';

-- ----------------------------
-- Table structure for fm_unload_running
-- ----------------------------
DROP TABLE IF EXISTS `fm_unload_running`;
CREATE TABLE `fm_unload_running` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `FILE_NAME` varchar(200) NOT NULL COMMENT '文件名称|文件名称',
  `SEGMENT_START` varchar(50) DEFAULT NULL COMMENT '大段起始值|大段起始值',
  `SEGMENT_END` varchar(50) DEFAULT NULL COMMENT '大段终止值|大段终止值',
  `DEAL_RESULT_FLAG` char(1) NOT NULL COMMENT '处理结果标志|处理结果标志|1-未处理,2-已处理,3-处理中,4-处理失败',
  `FILE_INFO_SUM` varchar(10) DEFAULT NULL COMMENT '文件数据条数|文件数据条数',
  `DEAL_DATE` datetime NOT NULL COMMENT '处理日期|处理日期',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`FILE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载运行表|数据卸载运行表';

-- ----------------------------
-- Table structure for fm_user
-- ----------------------------
DROP TABLE IF EXISTS `fm_user`;
CREATE TABLE `fm_user` (
  `USER_ID` varchar(30) NOT NULL COMMENT '交易柜员|柜员编号，一般复用银行员工编号',
  `USER_NAME` varchar(200) NOT NULL COMMENT '柜员名称|柜员名称',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `USER_TYPE` varchar(20) NOT NULL COMMENT '柜员类别|柜员是普通临柜柜员还是虚拟自助设备柜员|DUMMY_TELLER-虚拟柜员,TELLER_USER-普通柜员',
  `USER_SUB_TYPE` char(1) DEFAULT NULL COMMENT '柜员细类|临柜柜员时细类为Y，虚拟柜员时标记具体的虚拟柜员细类|A-ATM柜员,I-ITM柜员,Q-圈存机柜员,S-系统级虚拟柜员,Y-实体柜员',
  `USER_DESC` varchar(50) DEFAULT NULL COMMENT '柜员描述信息|柜员描述信息，目前系统暂未使用',
  `USER_LANG` varchar(30) DEFAULT NULL COMMENT '柜员语言|用于控制柜员登录柜面后界面的文字显示语种，目前系统暂未使用|E-英文,C-中文',
  `USER_LEVEL` char(1) DEFAULT NULL COMMENT '柜员级别|柜员级别，目前系统暂未使用|0-无级别,1-一级,2-二级,3-三级',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|柜员在管理上的归属机构',
  `DEPARTMENT` varchar(10) DEFAULT NULL COMMENT '部门|部门，目前系统暂未使用',
  `ROLE_ID` varchar(200) DEFAULT NULL COMMENT '角色|柜员的岗位编号或角色编号，英文逗号分隔，可以给角色上绑定尾箱限额',
  `PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '利润中心 |利润中心',
  `ACCT_EXEC` varchar(30) DEFAULT NULL COMMENT '客户经理|客户经理，目前系统暂未使用',
  `TBOOK` varchar(2) DEFAULT NULL COMMENT '账薄|账薄，目前系统暂未使用',
  `ACCOUNT_STATUS` char(1) NOT NULL COMMENT '柜员状态|柜员是正常状态还是已经删除|A-有效,D-删除',
  `APPLICATION_USER_FLAG` char(1) DEFAULT NULL COMMENT '是否应用柜员|是否应用柜员，目前系统暂未使用|Y-是,N-否',
  `EOD_SOD_ENABLED_FLAG` char(1) DEFAULT NULL COMMENT '是否批处理用户|是否批处理用户，目前系统暂未使用|Y-是,N-否',
  `INTER_BRANCH_CL` char(1) DEFAULT NULL COMMENT '是否贷款业务机构|柜员是否可以办理贷款业务|Y-是,N-否',
  `INTER_BRANCH_IND` char(1) DEFAULT NULL COMMENT '是否为内部银行|是否为内部银行，目前系统暂未使用|Y-是,N-否',
  `AUTH_LEVEL` char(1) DEFAULT NULL COMMENT '授权级别|授权级别，目前系统暂未使用|Y-允许授权,N-不允许授权',
  `APPR_USER_ID` varchar(30) DEFAULT NULL COMMENT '复核柜员|复核柜员，目前系统暂未使用',
  `CHECK_DATE` datetime DEFAULT NULL COMMENT '检查日期|检查日期，目前系统暂未使用',
  `CREATION_USER_ID` varchar(30) DEFAULT NULL COMMENT '创建柜员|创建柜员，目前系统暂未使用',
  `MAKE_DATE` datetime DEFAULT NULL COMMENT '柜员创建日期|柜员创建日期',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|柜员创建渠道，目前系统暂未使用',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`USER_ID`,`BRANCH`),
  KEY `IDX_FM_USER_2` (`BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='核心柜员信息表|核心柜员信息表';

-- ----------------------------
-- Table structure for fm_user_login_branch
-- ----------------------------
DROP TABLE IF EXISTS `fm_user_login_branch`;
CREATE TABLE `fm_user_login_branch` (
  `USER_ID` varchar(30) NOT NULL COMMENT '交易柜员|柜员ID',
  `LOGIN_BRANCH` varchar(20) NOT NULL COMMENT '可登录机构|默认配置柜员与除本机构之外的可登录的机构的关系数据。如果一个柜员可以登录多个机构，则配置多条关系数据。',
  PRIMARY KEY (`USER_ID`,`LOGIN_BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='柜员登录机构表|该表默认配置柜员与除本机构之外的可登录的机构的关系数据。如果一个柜员可以登录多个机构，则配置多条关系数据。';

-- ----------------------------
-- Table structure for fw_tran_info
-- ----------------------------
DROP TABLE IF EXISTS `fw_tran_info`;
CREATE TABLE `fw_tran_info` (
  `SERVICE_ID` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '服务ID',
  `SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '服务唯一识别号',
  `TRAN_DATE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易日期',
  `TRAN_TIME` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易时间',
  `IN_MSG` longblob COMMENT '输入报文',
  `OUT_MSG` longblob COMMENT '输出报文',
  `RESPONSE_TYPE` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '输出响应类型',
  `END_TIME` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易完成时间',
  `SOURCE_TYPE` varchar(20) DEFAULT NULL,
  `SEQ_NO` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '渠道流水号',
  `PROGRAM_ID` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易屏幕标识',
  `STATUS` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '状态',
  `REFERENCE` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '业务参考号',
  `PLATFORM_ID` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '平台流水号',
  `USER_ID` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作柜员',
  `IP_ADDRESS` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'IP地址',
  `BRANCH_ID` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '网点',
  `COMPENSATE_SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '待补偿原交易唯一识别号',
  `WEEK_DAY` decimal(1,0) DEFAULT NULL COMMENT '日期',
  `CREATE_DATE` datetime NOT NULL COMMENT '记录创建日期',
  PRIMARY KEY (`SERVICE_NO`,`CREATE_DATE`) USING BTREE,
  KEY `FW_TRAN_INFO_IDX1` (`TRAN_DATE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='交易流水表'
/*!50100 PARTITION BY RANGE (to_days(`CREATE_DATE`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for ic_ec_acct_info
-- ----------------------------
DROP TABLE IF EXISTS `ic_ec_acct_info`;
CREATE TABLE `ic_ec_acct_info` (
  `ACCT_NAME` varchar(200) NOT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `CARD_NO` varchar(50) NOT NULL COMMENT '卡号|卡号',
  `ACCT_CLOSE_DATE` datetime DEFAULT NULL COMMENT '销户日期|账户销户日期',
  `OPEN_DATE` datetime DEFAULT NULL COMMENT '开立日期|开立日期',
  `IC_CARD_SEQ` varchar(5) NOT NULL COMMENT '卡序列号|卡序列号',
  `IC_APP_END_DATE` varchar(10) DEFAULT NULL COMMENT '应用失效日期|应用失效日期',
  `CLOSE_SEQ_NUM` varchar(50) DEFAULT NULL COMMENT '销户流水号|销户流水号',
  `EC_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '电子现金账户币种|电子现金账户币种',
  `OPEN_ORG_ID` varchar(20) NOT NULL COMMENT '开户机构|开户机构',
  `IC_ACT_BAL` decimal(17,2) DEFAULT NULL COMMENT '电子现金账户余额|电子现金账户余额',
  `EC_TRAN_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '电子现金单笔交易限额|电子现金单笔交易限额',
  `IC_AID` varchar(50) NOT NULL COMMENT '应用标识符|应用标识符',
  `EC_ACCT_STAT` char(1) NOT NULL COMMENT '电子现金账户状态|电子现金账户状态',
  `AGGR_AMT` decimal(17,2) DEFAULT NULL COMMENT '累计圈存金额|累计圈存金额',
  `EC_BAL_TOP_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '电子现金余额上限|电子现金余额上限'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='IC卡电子现金账户表|IC卡电子现金账户表';

-- ----------------------------
-- Table structure for mq_consumer_msg
-- ----------------------------
DROP TABLE IF EXISTS `mq_consumer_msg`;
CREATE TABLE `mq_consumer_msg` (
  `MESSAGE_ID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息ID',
  `DESTINATION` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息目的地',
  `RECEIVE_TIME` datetime NOT NULL COMMENT '生产者接收时间',
  `STATUS` int DEFAULT NULL COMMENT '消息状态：1-接收成功，3:消费成功，4:消费失败',
  `UPDATE_TIME` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后一次更新状态时间',
  `REMARK` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`MESSAGE_ID`,`RECEIVE_TIME`) USING BTREE,
  KEY `MQ_CONSUMER_MSG_IDX1` (`UPDATE_TIME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ消费者信息消息表(UPRIGHT)'
/*!50100 PARTITION BY RANGE (to_days(`RECEIVE_TIME`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for mq_consumer_msg_hist
-- ----------------------------
DROP TABLE IF EXISTS `mq_consumer_msg_hist`;
CREATE TABLE `mq_consumer_msg_hist` (
  `MESSAGE_ID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息ID',
  `DESTINATION` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息目的地',
  `RECEIVE_TIME` datetime NOT NULL COMMENT '生产者接收时间',
  `STATUS` int DEFAULT NULL COMMENT '消息状态：1-接收成功，3:消费成功，4:消费失败',
  `UPDATE_TIME` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后一次更新状态时间',
  `REMARK` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`MESSAGE_ID`,`RECEIVE_TIME`) USING BTREE,
  KEY `MQ_CONSUMER_MSG_HIST_IDX1` (`UPDATE_TIME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ消费者信息消息表(历史表)(UPRIGHT)'
/*!50100 PARTITION BY RANGE (to_days(`RECEIVE_TIME`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for mq_consumer_repeat
-- ----------------------------
DROP TABLE IF EXISTS `mq_consumer_repeat`;
CREATE TABLE `mq_consumer_repeat` (
  `MESSAGE_ID` varchar(50) NOT NULL COMMENT '消息ID',
  `DESTINATION` varchar(128) DEFAULT NULL COMMENT '消息目的地',
  `RECEIVE_TIME` timestamp NULL DEFAULT NULL COMMENT '生产者接收时间',
  `REMARK` varchar(2000) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`MESSAGE_ID`) USING BTREE,
  KEY `MQ_CONSUMER_REPEAT_IDX1` (`MESSAGE_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ消费者信息消息表(UPRIGHT)';

-- ----------------------------
-- Table structure for mq_producer_msg
-- ----------------------------
DROP TABLE IF EXISTS `mq_producer_msg`;
CREATE TABLE `mq_producer_msg` (
  `MESSAGE_ID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息id',
  `SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务唯一识别号',
  `FLOW_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程id',
  `BROKER_NAME` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'broker名称',
  `OFFSET_MSG_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息发送成功，broker生成 id',
  `MESSAGE` longblob COMMENT '消息内容',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后一次更新时间',
  `STATUS` int DEFAULT NULL COMMENT '状态:1-消息建立；2-待发送；3-发送成 功；4-异常',
  `MESSAGE_TYPE` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息类型',
  `SEQ_NO` int DEFAULT NULL COMMENT '消息序列号',
  `QUEUE_ID` int DEFAULT NULL COMMENT '消息接受队列id',
  PRIMARY KEY (`MESSAGE_ID`,`CREATE_TIME`) USING BTREE,
  KEY `MQ_PRODUCER_MSG_IDX` (`FLOW_ID`,`STATUS`) USING BTREE,
  KEY `MQ_PRODUCER_MSG_IDX1` (`LAST_UPDATE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ生产者信息消息表(UPRIGHT)'
/*!50100 PARTITION BY RANGE (to_days(`CREATE_TIME`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for mq_producer_msg_hist
-- ----------------------------
DROP TABLE IF EXISTS `mq_producer_msg_hist`;
CREATE TABLE `mq_producer_msg_hist` (
  `MESSAGE_ID` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息id',
  `SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务唯一识别号',
  `FLOW_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程id',
  `BROKER_NAME` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'broker名称',
  `OFFSET_MSG_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息发送成功，broker生成 id',
  `MESSAGE` longblob COMMENT '消息内容',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后一次更新时间',
  `STATUS` int DEFAULT NULL COMMENT '状态:1-消息建立；2-待发送；3-发送成 功；4-异常',
  `MESSAGE_TYPE` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息类型',
  `SEQ_NO` int DEFAULT NULL COMMENT '消息序列号',
  `QUEUE_ID` int DEFAULT NULL COMMENT '消息接受队列id',
  PRIMARY KEY (`MESSAGE_ID`,`CREATE_TIME`) USING BTREE,
  KEY `MQ_PRODUCER_MSG_HIST_IDX` (`FLOW_ID`,`STATUS`) USING BTREE,
  KEY `MQ_PRODUCER_MSG_HIST_IDX1` (`LAST_UPDATE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ生产者信息消息表(历史表)(UPRIGHT)'
/*!50100 PARTITION BY RANGE (to_days(`CREATE_TIME`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for mq_producer_send_msg
-- ----------------------------
DROP TABLE IF EXISTS `mq_producer_send_msg`;
CREATE TABLE `mq_producer_send_msg` (
  `MQ_MSG_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息id',
  `FLOW_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程id',
  `STATUS` decimal(11,0) DEFAULT NULL COMMENT '状态:1-消息建立；2-待发送；3-发送成功；4-异常',
  PRIMARY KEY (`MQ_MSG_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ生产者待发送消息表(UPRIGHT)';

-- ----------------------------
-- Table structure for oc_gc_hist
-- ----------------------------
DROP TABLE IF EXISTS `oc_gc_hist`;
CREATE TABLE `oc_gc_hist` (
  `OC_REF_NO` varchar(50) NOT NULL COMMENT '同城交易流水号|同城交易流水号',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `CHANGE_NO` varchar(50) DEFAULT NULL COMMENT '交换号|同城票交中每个交换场次所代表的交换号码',
  `CHANGE_REGION` varchar(10) DEFAULT NULL COMMENT '交换地区|同城票交中每个场次的交换地区',
  `CHANGE_DATE` datetime DEFAULT NULL COMMENT '交换日期|交换日期',
  `CHANGE_SESSION` char(1) DEFAULT NULL COMMENT '交换场次|交换场次|A-一场,B-二场,C-三场,D-四场',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `DR_CR_FLAG` char(1) DEFAULT NULL COMMENT '借贷方向|借贷方向|D-借,C-贷',
  `GC_AMT` decimal(17,2) DEFAULT NULL COMMENT '轧差金额|轧差金额',
  `ICR_AMT` decimal(17,2) DEFAULT NULL COMMENT '提入贷方金额|提入贷方金额',
  `ICRT_AMT` decimal(17,2) DEFAULT NULL COMMENT '提入贷方退票金额|提入贷方退票金额',
  `IDR_AMT` decimal(17,2) DEFAULT NULL COMMENT '提入借方金额|提入借方金额',
  `IDRT_AMT` decimal(17,2) DEFAULT NULL COMMENT '提入借方退票金额|提入借方退票金额',
  `OCR_AMT` decimal(17,2) DEFAULT NULL COMMENT '提出贷方金额|提出贷方金额',
  `ODR_AMT` decimal(17,2) DEFAULT NULL COMMENT '提出借方金额|提出借方金额',
  `ODRT_AMT` decimal(17,2) DEFAULT NULL COMMENT '提出借方退票金额|提出借方退票金额',
  `GC_CANCLE` char(1) DEFAULT NULL COMMENT '清查划拨标记|清查划拨标记|G-已清查,C-取消清查',
  `REVERSAL_FLAG` char(1) DEFAULT NULL COMMENT '交易是否已冲正|交易是否已冲正|Y-是,N-否',
  `SETTLE_FLAG` char(1) DEFAULT NULL COMMENT '结清标志|是否结清标志|Y-未结清,N-结清',
  `ICR_NUM` int DEFAULT NULL COMMENT '提入贷方笔数|提入贷方笔数',
  `ICRT_NUM` int DEFAULT NULL COMMENT '提入贷方退票数|提入贷方退票数',
  `IDR_NUM` int DEFAULT NULL COMMENT '提入借方笔数|提入借方笔数',
  `IDRT_NUM` int DEFAULT NULL COMMENT '提入借方退票数|提入借方退票数',
  `OCRT_NUM` int DEFAULT NULL COMMENT '提出贷方退票数|提出贷方退票数',
  `ODR_NUM` int DEFAULT NULL COMMENT '提出借方笔数|提出借方笔数',
  `ODRT_NUM` int DEFAULT NULL COMMENT '提出借方退票数|提出借方退票数',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `DR_AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '借方发生额 |借方发生额 ',
  `CR_AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '贷方发生额 |贷方发生额 ',
  `OCRT_AMT` decimal(17,2) DEFAULT NULL COMMENT '提出贷方退票金额|提出贷方退票金额',
  `OCR_NUM` int DEFAULT NULL COMMENT '提出贷方笔数|提出贷方笔数',
  `QING_CHA_FU_CHA` decimal(17,2) DEFAULT NULL COMMENT '清差（付差）|清差（付差）',
  `QING_CHA_SHOU_CHA` decimal(17,2) DEFAULT NULL COMMENT '清差（收差）|清差（收差）',
  `TI_CHU_TUO_FU_JZ` decimal(17,2) DEFAULT NULL COMMENT '提出托付结转|提出托付结转',
  `TI_CHU_TUO_SHOU_JZ` decimal(17,2) DEFAULT NULL COMMENT '提出托收结转|提出托收结转',
  `TI_RU_TUO_FU_JZ` decimal(17,2) DEFAULT NULL COMMENT '提入托付结转|提入托付结转',
  `TI_RU_TUO_SHOU_JZ` decimal(17,2) DEFAULT NULL COMMENT '提入托收结转|提入托收结转',
  PRIMARY KEY (`OC_REF_NO`),
  KEY `IDX_OC_GC_HIST_1M` (`TRAN_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='同城票交扎差清算表|记录同城票交轧差清算流水信息';

-- ----------------------------
-- Table structure for oc_input_ass_dtl
-- ----------------------------
DROP TABLE IF EXISTS `oc_input_ass_dtl`;
CREATE TABLE `oc_input_ass_dtl` (
  `ASSIGN_SEQ_NO` varchar(50) NOT NULL COMMENT '清分序号|清分序号',
  `CHANGE_NO` varchar(50) DEFAULT NULL COMMENT '交换号|同城票交中每个交换场次所代表的交换号码',
  `CHANGE_REGION` varchar(10) DEFAULT NULL COMMENT '交换地区|同城票交中每个场次的交换地区',
  `CHANGE_DATE` datetime DEFAULT NULL COMMENT '交换日期|交换日期',
  `CHANGE_SESSION` char(1) DEFAULT NULL COMMENT '交换场次|交换场次|A-一场,B-二场,C-三场,D-四场',
  `RECORD_BRANCH` varchar(50) DEFAULT NULL COMMENT '提入清分录入机构|机构',
  `RECORD_USER_ID` varchar(30) DEFAULT NULL COMMENT '提入清分录入柜员|柜员',
  `OTH_BANK_CODE` varchar(20) DEFAULT NULL COMMENT '对方银行代码|对方银行代码',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `ICR_NUM` int DEFAULT NULL COMMENT '提入贷方笔数|提入贷方笔数',
  `ICR_AMT` decimal(17,2) DEFAULT NULL COMMENT '提入贷方金额|提入贷方金额',
  `IDR_AMT` decimal(17,2) DEFAULT NULL COMMENT '提入借方金额|提入借方金额',
  `IDR_NUM` int DEFAULT NULL COMMENT '提入借方笔数|提入借方笔数',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`ASSIGN_SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='同城票交提入清分明细表|记录同城票交提入清分明细';

-- ----------------------------
-- Table structure for oc_input_assign
-- ----------------------------
DROP TABLE IF EXISTS `oc_input_assign`;
CREATE TABLE `oc_input_assign` (
  `ASSIGN_SEQ_NO` varchar(50) NOT NULL COMMENT '清分序号|清分序号',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `CHANGE_NO` varchar(50) DEFAULT NULL COMMENT '交换号|同城票交中每个交换场次所代表的交换号码',
  `CHANGE_SESSION` char(1) DEFAULT NULL COMMENT '交换场次|交换场次|A-一场,B-二场,C-三场,D-四场',
  `CHANGE_DATE` datetime DEFAULT NULL COMMENT '交换日期|交换日期',
  `CHANGE_REGION` varchar(10) DEFAULT NULL COMMENT '交换地区|同城票交中每个场次的交换地区',
  `ICR_TOTAL_NUM` int DEFAULT NULL COMMENT '提入贷方总笔数|提入贷方总笔数',
  `ICR_TOTAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '提入贷方总金额|提入贷方总金额',
  `IDR_TOTAL_NUM` int DEFAULT NULL COMMENT '提入借方总笔数|提入借方总笔数',
  `IDR_TOTAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '提入借方总金额|提入借方总金额',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`ASSIGN_SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='同城票交提入业务清分表|记录同城票交提入业务清分信息';

-- ----------------------------
-- Table structure for orbit_records
-- ----------------------------
DROP TABLE IF EXISTS `orbit_records`;
CREATE TABLE `orbit_records` (
  `CENTER_IND` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '中心标识',
  `IS_LOCAL` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '本地标识',
  `CACHE_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '缓存名称',
  `CACHE_KEY` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '缓存KEY',
  PRIMARY KEY (`CENTER_IND`,`IS_LOCAL`,`CACHE_NAME`,`CACHE_KEY`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='分布式缓存组件';

-- ----------------------------
-- Table structure for rb_accr_merge_hist
-- ----------------------------
DROP TABLE IF EXISTS `rb_accr_merge_hist`;
CREATE TABLE `rb_accr_merge_hist` (
  `PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '利润中心 |利润中心 ',
  `TRAN_PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '交易利润中心|交易利润中心',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|渠道类型',
  `CHANNEL_DATE` datetime DEFAULT NULL COMMENT '渠道日期|渠道日期',
  `SETTLE_BRANCH` varchar(50) DEFAULT NULL COMMENT '清算机构|清算机构',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `EVENT_TYPE` varchar(20) DEFAULT NULL COMMENT '事件类型|事件类型',
  `AMT_TYPE` varchar(10) DEFAULT NULL COMMENT '金额类型|金额类型|BAL-余额,DDA-发放金额,INTP-逾期利息,LIM-额度金额,OD-透支金额,ODIP-逾期复利,ODPP-逾期罚息,OSL-未到期本金,PRD-逾期本金,PRI-本金',
  `AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '金额|金额',
  `INT_AMT` decimal(17,2) DEFAULT NULL COMMENT '利息金额|利息金额',
  `ODI_AMT` decimal(17,2) DEFAULT NULL COMMENT '复利金额|复利金额',
  `PRI_AMT` decimal(17,2) DEFAULT NULL COMMENT '本金金额|本金金额',
  `TAX_AMT` decimal(17,2) DEFAULT NULL COMMENT '税金|税金',
  `ODP_AMT` decimal(17,2) DEFAULT NULL COMMENT '罚息金额|罚息金额',
  `CR_DR_IND` char(1) DEFAULT NULL COMMENT '借贷标志|借贷标志|C-贷 ,D-借',
  `GL_CODE` varchar(20) DEFAULT NULL COMMENT '科目代码|科目代码',
  `GL_POSTED_FLAG` char(1) DEFAULT NULL COMMENT '过账标记|过账标记|Y-是 ,N-否',
  `REVERSAL_DATE` datetime DEFAULT NULL COMMENT '冲正日期|冲正日期',
  `IN_STATUS` char(1) DEFAULT NULL COMMENT '入账方式|入账方式|B-批量插入状态为,O-流水插入状态为',
  `REVERSAL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '冲正流水号|冲正流水号',
  `REVERSAL_FLAG` char(1) DEFAULT NULL COMMENT '交易是否已冲正|交易是否已冲正|Y-是,N-否',
  `ACCOUNTING_STATUS` varchar(3) DEFAULT NULL COMMENT '核算状态|核算状态，为贷款核算状态类型，会计部门根据借款凭证针对借款合同进行审核的贷款核算分级审批制度|ZHC-正常,YUQ_逾期,FYJ-非应计,FY-手工转非应计,WRN-核销,TER-终止',
  `BANK_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '银行交易序号|银行交易序号,单一机构下发生交易序号，按顺序递增 格式为 "机构_序号"',
  `BUSINESS_UNIT` varchar(10) DEFAULT NULL COMMENT '账套|账套|CBT-综合人民币账套,UBT-综合美元账套,YBT-原币账套',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `SOURCE_MODULE` varchar(3) DEFAULT NULL COMMENT '源模块|源模块|RB-存款,CL-贷款,GL-总账,ALL-所有',
  `SYSTEM_ID` varchar(20) DEFAULT NULL COMMENT '系统ID|系统ID',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='计提合并流水表|记录账户计提合并流水信息';

-- ----------------------------
-- Table structure for rb_acct_appointment
-- ----------------------------
DROP TABLE IF EXISTS `rb_acct_appointment`;
CREATE TABLE `rb_acct_appointment` (
  `APPLY_ID` varchar(50) NOT NULL COMMENT '申请预约编号|预约编号',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `APPLY_DUE_DATE` datetime DEFAULT NULL COMMENT '预约到期日|预约到期日',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_TYPE` char(1) DEFAULT NULL COMMENT '账户类型|账户类型|A-AIO账户,C-结算账户,S-储蓄账户,T-定期账户',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `CATEGORY_TYPE` varchar(3) DEFAULT NULL COMMENT '客户细分类型|客户细类',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `APPOINTMENT_STATUS` varchar(2) DEFAULT NULL COMMENT '预约状态|预约状态|S-预约成功,DS-预约已完成,D-预约已到期',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  PRIMARY KEY (`APPLY_ID`),
  KEY `IDX_RB_ACCT_APPOINTMENT_1M` (`ACCT_TYPE`,`PROD_TYPE`,`BASE_ACCT_NO`,`ACCT_CCY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='账户预约表|记录账户预约信息';

-- ----------------------------
-- Table structure for rb_acct_check_result
-- ----------------------------
DROP TABLE IF EXISTS `rb_acct_check_result`;
CREATE TABLE `rb_acct_check_result` (
  `CHECK_DATE` datetime NOT NULL COMMENT '检查日期|检查日期',
  `INTERNAL_KEY` bigint NOT NULL COMMENT '账户内部键值|账户内部键值',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCOUNTING_STATUS` varchar(3) NOT NULL COMMENT '核算状态|核算状态，为贷款核算状态类型，会计部门根据借款凭证针对借款合同进行审核的贷款核算分级审批制度|ZHC-正常,YUQ-逾期,FYJ-非应计,FY-手工转非应计,DZA-呆账,DZI-呆滞,WRN-核销,TER-终止',
  `GL_CODE` varchar(20) DEFAULT NULL COMMENT '科目代码|科目代码',
  `AMT_TYPE` varchar(10) NOT NULL COMMENT '金额类型|金额类型|BAL-余额,DDA-发放金额,INTP-逾期利息,LIM-额度金额,OD-透支金额,ODIP-逾期复利,ODPP-逾期罚息,OSL-未到期本金,PRD-逾期本金,PRI-本金,PF-净本金,INT-利息,ODI-复利,ODP-罚息,FEE-费用,UNI-非本金,ALL-本加息,DS-前收息金额,PRF-提前结清手续费,ODODP-罚息的复利,ODODI-复利的复利',
  `PREV_AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '上日金额 |上日金额 ',
  `TOTAL_TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT 'T-1发生汇总|T-1发生汇总',
  `CALC_BALANCE` decimal(17,2) DEFAULT NULL COMMENT '计算T-1余额|计算T-1余额',
  `ACCT_BALANCE` decimal(17,2) DEFAULT NULL COMMENT '账户余额|账户余额',
  `DIFF_BALANCE` decimal(17,2) DEFAULT NULL COMMENT '余额的差额|余额的差额',
  `CHECK_RESULT` char(1) DEFAULT NULL COMMENT '结果|结果|S-成功,F-失败',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`CHECK_DATE`,`INTERNAL_KEY`,`ACCOUNTING_STATUS`,`AMT_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='分分核对结果表';

-- ----------------------------
-- Table structure for rb_acct_client_relation
-- ----------------------------
DROP TABLE IF EXISTS `rb_acct_client_relation`;
CREATE TABLE `rb_acct_client_relation` (
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_SEQ_NO` varchar(5) NOT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) NOT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACTUAL_ACCT_NO` varchar(50) NOT NULL COMMENT '实际账号|实际账号',
  `INTERNAL_KEY` bigint NOT NULL COMMENT '账户内部键值|账户内部键值',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `INDIVIDUAL_FLAG` char(1) DEFAULT NULL COMMENT '对公对私标志|对公对私标志|Y-对私,N-对公',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|渠道类型',
  `IS_CARD` char(1) DEFAULT NULL COMMENT '是否卡|是否卡|Y-是,N-否',
  `CARD_NO` varchar(50) DEFAULT NULL COMMENT '卡号|卡号',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `ACCT_CLASS` char(1) DEFAULT NULL COMMENT '账户类别|账户类别，用于区分账户类别（一二三类户），满足人行对于电子账户的管理办法|1-一类账户,2-二类账户,3-三类账户',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `ACCT_NATURE` varchar(10) DEFAULT NULL COMMENT '账户属性|账户属性',
  `ACCT_REAL_FLAG` char(1) DEFAULT NULL COMMENT '账户虚实标志|账户虚实标志，虚账户不产生核算，不进行监管报送|Y-实账户,N-虚账户',
  `DEFAULT_SETTLE_ACCT` char(1) DEFAULT NULL COMMENT '是否默认结算账户|是否默认结算账户|Y-是,N-否',
  `LEAD_ACCT_FLAG` char(1) DEFAULT NULL COMMENT '主账户标志|主账户标志|Y-是 ,N-否',
  `PARENT_INTERNAL_KEY` bigint DEFAULT NULL COMMENT '上级账户标识符|上级账户标识符',
  `REASON_CODE` varchar(10) DEFAULT NULL COMMENT '账户用途|账户用途',
  `APP_FLAG` char(1) DEFAULT NULL COMMENT '附属卡标志|附属卡标志|Y-是,N-否',
  `IS_CORP_SETTLE_CARD` char(1) DEFAULT NULL COMMENT '单位结算卡标志|单位结算卡标志|Y-是,N-否',
  `SHARD_ID` varchar(5) DEFAULT NULL COMMENT '分库标志|分库标志',
  `REASON_CODE_DESC` varchar(100) DEFAULT NULL COMMENT '原因代码描述|原因代码描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `LAST_CHANGE_DATE` datetime DEFAULT NULL COMMENT '最后修改日期|最后修改日期',
  PRIMARY KEY (`CLIENT_NO`,`BASE_ACCT_NO`,`ACCT_SEQ_NO`,`PROD_TYPE`,`ACCT_CCY`,`ACTUAL_ACCT_NO`),
  KEY `FACR_IND5M` (`ACTUAL_ACCT_NO`),
  KEY `FACR_IND4M` (`INTERNAL_KEY`),
  KEY `FACR_IND2M` (`BASE_ACCT_NO`),
  KEY `FACR_IND3M` (`CARD_NO`),
  KEY `FACR_IND1M` (`CLIENT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='账户客户关系表|记录账户客户关系信息';

-- ----------------------------
-- Table structure for rb_acct_financial_check
-- ----------------------------
DROP TABLE IF EXISTS `rb_acct_financial_check`;
CREATE TABLE `rb_acct_financial_check` (
  `BATCH_NO` varchar(50) DEFAULT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='账户级总分核对验证表|分户级总分核对结果表';

-- ----------------------------
-- Table structure for rb_acct_open
-- ----------------------------
DROP TABLE IF EXISTS `rb_acct_open`;
CREATE TABLE `rb_acct_open` (
  `APPLY_NO` varchar(50) DEFAULT NULL COMMENT '申请编号|申请编号',
  `APPLY_DATE` datetime DEFAULT NULL COMMENT '申请日期|申请日期',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `ISS_COUNTRY` varchar(3) DEFAULT NULL COMMENT '发证国家|发证国家',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `CH_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户中文名称|客户中文名称',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `STATUS` char(1) DEFAULT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账,N-新增,U-修改,D-删除,C-非活动状态',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `SPEC_ACCT_FLAG` char(1) DEFAULT NULL COMMENT '定制标识|定义当前账号是否是定制账号|Y-定制账号,N-非定制账号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户预约开户登记簿|客户预约开户登记簿';

-- ----------------------------
-- Table structure for rb_acct_prod_balance
-- ----------------------------
DROP TABLE IF EXISTS `rb_acct_prod_balance`;
CREATE TABLE `rb_acct_prod_balance` (
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `ACCOUNTING_STATUS` varchar(3) NOT NULL COMMENT '核算状态|核算状态，为贷款核算状态类型，会计部门根据借款凭证针对借款合同进行审核的贷款核算分级审批制度|ZHC-正常,YUQ-逾期,FYJ-非应计,FY-手工转非应计,DZA-呆账,DZI-呆滞,WRN-核销,TER-终止',
  `GL_CODE` varchar(20) DEFAULT NULL COMMENT '科目代码|科目代码',
  `AMT_TYPE` varchar(10) NOT NULL COMMENT '金额类型|金额类型|BAL-余额,DDA-发放金额,INTP-逾期利息,LIM-额度金额,OD-透支金额,ODIP-逾期复利,ODPP-逾期罚息,OSL-未到期本金,PRD-逾期本金,PRI-本金,PF-净本金,INT-利息,ODI-复利,ODP-罚息,FEE-费用,UNI-非本金,ALL-本加息,DS-前收息金额,PRF-提前结清手续费,ODODP-罚息的复利,ODODI-复利的复利',
  `BALANCE` decimal(17,2) DEFAULT NULL COMMENT '余额|余额',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `INTERNAL_KEY` bigint NOT NULL COMMENT '账户内部键值|账户内部键值',
  PRIMARY KEY (`INTERNAL_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='账户产品汇总余额表';

-- ----------------------------
-- Table structure for rb_acct_prod_balance_hist
-- ----------------------------
DROP TABLE IF EXISTS `rb_acct_prod_balance_hist`;
CREATE TABLE `rb_acct_prod_balance_hist` (
  `BACKUP_DATE` datetime NOT NULL COMMENT '备份日期 |备份日期 ',
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `ACCOUNTING_STATUS` varchar(3) NOT NULL COMMENT '核算状态|核算状态，为贷款核算状态类型，会计部门根据借款凭证针对借款合同进行审核的贷款核算分级审批制度|ZHC-正常,YUQ-逾期,FYJ-非应计,FY-手工转非应计,DZA-呆账,DZI-呆滞,WRN-核销,TER-终止',
  `GL_CODE` varchar(20) DEFAULT NULL COMMENT '科目代码|科目代码',
  `AMT_TYPE` varchar(10) NOT NULL COMMENT '金额类型|金额类型|BAL-余额,DDA-发放金额,INTP-逾期利息,LIM-额度金额,OD-透支金额,ODIP-逾期复利,ODPP-逾期罚息,OSL-未到期本金,PRD-逾期本金,PRI-本金,PF-净本金,INT-利息,ODI-复利,ODP-罚息,FEE-费用,UNI-非本金,ALL-本加息,DS-前收息金额,PRF-提前结清手续费,ODODP-罚息的复利,ODODI-复利的复利',
  `BALANCE` decimal(17,2) DEFAULT NULL COMMENT '余额|余额',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `INTERNAL_KEY` bigint NOT NULL COMMENT '账户内部键值|账户内部键值',
  PRIMARY KEY (`BACKUP_DATE`,`INTERNAL_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='账户产品汇总余额历史表|产品汇总余额历史表';

-- ----------------------------
-- Table structure for rb_acct_prod_balance_temp
-- ----------------------------
DROP TABLE IF EXISTS `rb_acct_prod_balance_temp`;
CREATE TABLE `rb_acct_prod_balance_temp` (
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `AMT_TYPE` varchar(10) NOT NULL COMMENT '金额类型|金额类型|BAL-余额,DDA-发放金额,INTP-逾期利息,LIM-额度金额,OD-透支金额,ODIP-逾期复利,ODPP-逾期罚息,OSL-未到期本金,PRD-逾期本金,PRI-本金,PF-净本金,INT-利息,ODI-复利,ODP-罚息,FEE-费用,UNI-非本金,ALL-本加息,DS-前收息金额,PRF-提前结清手续费,ODODP-罚息的复利,ODODI-复利的复利',
  `ACCOUNTING_STATUS` varchar(3) NOT NULL COMMENT '核算状态|核算状态，为贷款核算状态类型，会计部门根据借款凭证针对借款合同进行审核的贷款核算分级审批制度|ZHC-正常,YUQ-逾期,FYJ-非应计,FY-手工转非应计,DZA-呆账,DZI-呆滞,WRN-核销,TER-终止',
  `GL_CODE` varchar(20) DEFAULT NULL COMMENT '科目代码|科目代码',
  `BALANCE` decimal(17,2) NOT NULL COMMENT '余额|余额',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `NODE_ID` varchar(50) NOT NULL COMMENT '数据库节点ID|数据库节点ID',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  PRIMARY KEY (`SEQ_NO`),
  KEY `RB_ACCT_PROD_BAL_TMP_INDX1` (`ACCT_BRANCH`),
  KEY `RB_ACCT_PROD_BAL_TMP_INDX` (`TRAN_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品当日发生汇总过渡表|产品发生余额汇总';

-- ----------------------------
-- Table structure for rb_acct_risk_batch
-- ----------------------------
DROP TABLE IF EXISTS `rb_acct_risk_batch`;
CREATE TABLE `rb_acct_risk_batch` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_RISK_LEVEL` char(1) NOT NULL COMMENT '账户风险等级|个人账户风险等级范围：0~4对公账户风险等级范围：0~6|0-零级风险,1-一级风险,2-二级风险,3-三级风险,4-四级风险,5-五级风险,6-六级风险',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `BATCH_CLASS` varchar(10) DEFAULT NULL COMMENT '批次类型|批次类型|BATCH1-批量开立客户,BATCH2-批量开立账户,BATCH3-贷款批量开立/发放,BATCH4-批量司法查询,BATCH5-批量转账,BATCH6-批量开立内部账户（贷款贴现批量开立）,BATCH7-贷款批量核销,BATCH8-批量开立内部账户,BATCH9-批量开立存单,BATCH10-黑白名单批量导入,BATCH11-贷款批量发放核销,BATCH12-批量续开内部账,BATCH13-久悬户批量导入,BATCH14-批量冻结,BATCH15-批量解冻扣款,BATCH16-基金资金清算,BATCH17-社保卡批量开户开卡,BATCH18-CDCT对账,BATCH19-理财平台资金清算,BATCH20-日终签约文件同步,BATCH21-批量收取手续费,BATCH22-IC卡商户批量入账,BATCH23-委托关系验证,BATCH24-ic卡对账文件,BATCH25-IC卡挂失销户到期处理文件请求文件,BATCH26-生成指定表文件,BATCH27-批量冲正',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='账户风险等级批处理信息|账户风险等级批处理信息';

-- ----------------------------
-- Table structure for rb_acct_ticket_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_acct_ticket_detail`;
CREATE TABLE `rb_acct_ticket_detail` (
  `INTERNAL_KEY` bigint NOT NULL COMMENT '账户内部键值|账户内部键值',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `TICKET_TYPE` varchar(10) DEFAULT NULL COMMENT '券类型|券类型|DQQ-定期利率券,HQQ-活期利率券,TBQ-特色券,LXQ-利息券',
  `TICKET_NO` varchar(50) NOT NULL COMMENT '券编号|券编号',
  `EXPIRY_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `MIN_AMT` decimal(17,2) DEFAULT NULL COMMENT '最小金额|最小金额',
  `MAX_AMT` decimal(17,2) DEFAULT NULL COMMENT '最大金额|最大金额',
  `TICKET_AMT` decimal(17,2) DEFAULT NULL COMMENT '利息券金额|利息券金额',
  `ACTUAL_RATE` decimal(15,8) DEFAULT NULL COMMENT '行内利率|在人行基准利率调整后对客发布的行内利率',
  `INT_ACCRUED_CTD` decimal(17,2) DEFAULT NULL COMMENT '计提日计提利息|计提日计提利息',
  `INT_ACCRUED` decimal(17,2) DEFAULT NULL COMMENT '累计计提|累计计提',
  `INT_ACCRUED_CALC_CTD` decimal(25,10) DEFAULT NULL COMMENT '计提日计提实际金额|计提日计提实际金额',
  `INT_POSTED` decimal(17,2) DEFAULT NULL COMMENT '结息金额|结息金额',
  `INT_POSTED_CTD` decimal(17,2) DEFAULT NULL COMMENT '结息日利息金额|结息日利息金额',
  `AGREEMENT_ID` varchar(50) DEFAULT NULL COMMENT '协议编号|协议编号',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`INTERNAL_KEY`,`TICKET_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='加/减息券明细登记薄|加/减息券明细登记薄';

-- ----------------------------
-- Table structure for rb_advance_info
-- ----------------------------
DROP TABLE IF EXISTS `rb_advance_info`;
CREATE TABLE `rb_advance_info` (
  `REFERENCE` varchar(50) NOT NULL COMMENT '交易参考号|交易参考号',
  `ACCEPT_CONTRACT_NO` varchar(50) DEFAULT NULL COMMENT '银承合同编号|银行承兑汇票协议编号',
  `BRANCH_ID` varchar(50) DEFAULT NULL COMMENT '机构号|机构代码',
  `BILL_TYPE` varchar(5) DEFAULT NULL COMMENT '票据类型|票据类型|P-纸质,E-电子',
  `BAB_INTERNAL_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '内部账户账号|内部账户账号',
  `BAB_INTERNAL_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '内部账户序列号|内部账户序列号',
  `BAB_INTERNAL_CCY` varchar(3) DEFAULT NULL COMMENT '内部账户账户币种|内部账户账户币种',
  `BAB_INTERNAL_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '内部账户产品类型|内部账户产品类型',
  `BAB_INTERNAL_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '内部账户账户中文名称|内部账户账户中文名称',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `ADVANCE_FLAG` char(1) DEFAULT NULL COMMENT '垫款标志|垫款标志|Y-是,N-否',
  `LAST_CHANGE_OFFICER` varchar(30) DEFAULT NULL COMMENT '上次修改柜员|上次修改柜员',
  `LAST_UPDATE_DATE` datetime DEFAULT NULL COMMENT '上次更新日期|上次更新日期',
  PRIMARY KEY (`REFERENCE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='垫款信息表';

-- ----------------------------
-- Table structure for rb_agreement_group
-- ----------------------------
DROP TABLE IF EXISTS `rb_agreement_group`;
CREATE TABLE `rb_agreement_group` (
  `AGREEMENT_ID` varchar(50) NOT NULL COMMENT '协议编号|协议编号',
  `INTERNAL_KEY` bigint NOT NULL COMMENT '账户内部键值|账户内部键值',
  `AGREEMENT_STATUS` varchar(2) DEFAULT NULL COMMENT '协议状态|普通协议使用，可应用于大部分场景|A-生效,E-失效',
  `AGREEMENT_TYPE` varchar(10) DEFAULT NULL COMMENT '协议类型|协议类型|CLD-存立得,DC-大额存单,DLS-贷利省,HQB-活期宝,JDL-加多利,KDT-卡贷通,KYD-卡易贷,PCP-资金池,WDL-稳得利,XDB-协定宝,XDCK-协定存款产品,XDL-先得利,YBWL-一本万利,YCD-英才贷,YDT-易贷通,YHT-一户通,ZHY-周享赢,ZXY-坐享其盈,ZZB-至尊宝,LOA-贷款,ODF-法人透支协议,FIN-卡理财协议,SMS-短信,PKG-费用套餐,FEE-暂不收费,PCD-周期性强制扣划,ACC-协定存款协议,SWP-账户清扫协议,ID-智能存款协议,SL-金额补足协议,REC-回单签约,ES-电票签约,YD-约定,NTE-活期智能存款',
  `SIGN_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '签约产品类型|签约产品类型',
  `NEAR_AMT` decimal(17,2) DEFAULT NULL COMMENT '靠档金额|靠档金额',
  `INT_TYPE` varchar(5) DEFAULT NULL COMMENT '利率类型|利率类型',
  `REAL_RATE` decimal(15,8) DEFAULT NULL COMMENT '执行利率|执行利率',
  `SIGN_DATE` datetime DEFAULT NULL COMMENT '签约日期|签约日期',
  `AGREEMENT_START_DATE` datetime DEFAULT NULL COMMENT '协议起始日期|协议起始日期',
  `AGREEMENT_END_DATE` datetime DEFAULT NULL COMMENT '协议结束日期|协议结束日期',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`AGREEMENT_ID`,`INTERNAL_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='存款组靠档签约表';

-- ----------------------------
-- Table structure for rb_auto_create_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_auto_create_detail`;
CREATE TABLE `rb_auto_create_detail` (
  `ACCT_NAME` varchar(200) NOT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|所属机构号',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`ACCT_NAME`,`BRANCH`,`CCY`,`PROD_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='自动批量开立内部户明细表|日终内部户自动批量开户插入明细';

-- ----------------------------
-- Table structure for rb_auto_tran_info
-- ----------------------------
DROP TABLE IF EXISTS `rb_auto_tran_info`;
CREATE TABLE `rb_auto_tran_info` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `INTERNAL_KEY` bigint DEFAULT NULL COMMENT '账户内部键值|账户内部键值',
  `PRINCIPAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易本金|交易本金',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `REGISTER_DATE` datetime DEFAULT NULL COMMENT '登记日期|登记日期',
  `MATURITY_DATE` datetime DEFAULT NULL COMMENT '到期日期|到期日期',
  `FUND_FROM_ACCT_NO` varchar(30) DEFAULT NULL COMMENT '资金来源账号|登记资金来源账号',
  `FUND_FROM_BANK_NO` varchar(50) DEFAULT NULL COMMENT '资金来源支付行号|资金来源支付行号',
  `FUND_FROM_NAME` varchar(200) DEFAULT NULL COMMENT '资金来源户名|登记资金来源户名',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='定期账户到期自动划转登记簿|定期账户到期自动划转登记簿';

-- ----------------------------
-- Table structure for rb_backup_clean_param
-- ----------------------------
DROP TABLE IF EXISTS `rb_backup_clean_param`;
CREATE TABLE `rb_backup_clean_param` (
  `BASE_TABLE_NAME` varchar(100) NOT NULL COMMENT '业务表|业务表',
  `HIST_TABLE_NAME` varchar(100) DEFAULT NULL COMMENT '历史表|历史表',
  `FREQUENCY` varchar(20) NOT NULL COMMENT '对应频率|对应频率',
  `NEXT_CLEAN_DATE` datetime DEFAULT NULL COMMENT '下一清扫日期|下一清扫日期',
  `BACKUP_TYPE` varchar(10) NOT NULL COMMENT '备份类型（01-分区备份、02-条件备份、03-不备份）|备份类型（01-分区备份、02-条件备份、03-不备份）',
  `BACKUP_SEGMENT_FLAG` varchar(10) NOT NULL COMMENT '备份分段(Y-是、N-否)|备份分段(Y-是、N-否)',
  `CLEAN_TYPE` varchar(10) NOT NULL COMMENT '清理类型(01-分区清理、02-条件清理、03-不清理）|清理类型(01-分区清理、02-条件清理、03-不清理）',
  `CLEAN_SEGMENT_FLAG` varchar(10) NOT NULL COMMENT '清理分段(Y-是、N-否)|清理分段(Y-是、N-否)',
  `FIELD_NAME` varchar(50) DEFAULT NULL COMMENT '字段名|字段名',
  `SEGMENT_SIZE` int DEFAULT NULL COMMENT '分段大小|分段大小',
  `BACKUP_SQL_ID` varchar(50) DEFAULT NULL COMMENT '备份SQLID|备份SQLID',
  `CLEAN_SQL_ID` varchar(50) DEFAULT NULL COMMENT '清理SQLID|清理SQLID',
  `BACKUP_STATUS` varchar(10) DEFAULT NULL COMMENT '备份状态(R – 备份中  E –备份结束  N-未开始)|备份状态(R – 备份中  E –备份结束)',
  `CLEAN_STATUS` varchar(2) DEFAULT NULL COMMENT '结清标志|结清标志',
  `BACKUP_START_TIME` datetime DEFAULT NULL COMMENT '备份起始时间|备份起始时间',
  `BACKUP_END_TIME` datetime DEFAULT NULL COMMENT '备份结束时间|备份结束时间',
  `CLEAN_START_TIME` datetime DEFAULT NULL COMMENT '清理起始时间|清理起始时间',
  `CLEAN_END_TIME` datetime DEFAULT NULL COMMENT '清理结束时间|清理结束时间',
  `BACKUP_SEGMENT_ID` varchar(100) DEFAULT NULL COMMENT '备份分段ID|备份分段ID',
  `CLEAN_SEGMENT_ID` varchar(100) DEFAULT NULL COMMENT '清理分段ID|清理分段ID',
  `CLASS_NAME` varchar(200) NOT NULL COMMENT '分段映射实体类名|分段映射实体类名',
  `RETAIN_TIME_LIMIT` varchar(20) DEFAULT NULL COMMENT '保留期限|保留期限',
  PRIMARY KEY (`BASE_TABLE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据备份清理参数表';

-- ----------------------------
-- Table structure for rb_batch_acct_update_details
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_acct_update_details`;
CREATE TABLE `rb_batch_acct_update_details` (
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `ACCT_EXEC_CODE` varchar(20) DEFAULT NULL COMMENT '客户经理代码|客户经理代码',
  `ACCT_EXEC_NAME` varchar(200) DEFAULT NULL COMMENT '客户经理姓名|客户经理姓名',
  `PROMOTER_CODE` varchar(30) DEFAULT NULL COMMENT '推介人代码|推介人代码',
  `PROMOTER_NAME` varchar(30) DEFAULT NULL COMMENT '推介人名称|推介人名称',
  `LINE_OWNER_SHIP` varchar(30) DEFAULT NULL COMMENT '条线归属|条线归属',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量账户信息维护表|批量账户信息维护表';

-- ----------------------------
-- Table structure for rb_batch_async_cache
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_async_cache`;
CREATE TABLE `rb_batch_async_cache` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_CLASS` varchar(10) NOT NULL COMMENT '批次类型|批次类型|BATCH1-批量开立客户,BATCH2-批量开立账户,BATCH3-贷款批量开立/发放,BATCH4-批量司法查询,BATCH5-批量转账,BATCH6-批量开立内部账户（贷款贴现批量开立）,BATCH7-贷款批量核销,BATCH8-批量开立内部账户,BATCH9-批量开立存单,BATCH10-黑白名单批量导入,BATCH11-贷款批量发放核销,BATCH12-批量续开内部账,BATCH13-久悬户批量导入,BATCH14-批量冻结,BATCH15-批量解冻扣款,BATCH16-基金资金清算,BATCH17-社保卡批量开户开卡,BATCH18-CDCT对账,BATCH19-理财平台资金清算,BATCH20-日终签约文件同步,BATCH21-批量收取手续费,BATCH22-IC卡商户批量入账,BATCH23-委托关系验证,BATCH24-ic卡对账文件,BATCH25-IC卡挂失销户到期处理文件请求文件,BATCH26-生成指定表文件,BATCH27-批量冲正,BATCH39-批量开立一户通子账户',
  `CHANNEL_SEQ_NO` varchar(50) NOT NULL COMMENT '渠道流水号|渠道流水号',
  `REFERENCE` varchar(50) NOT NULL COMMENT '交易参考号|交易参考号',
  `FILE_PATH` varchar(200) NOT NULL COMMENT '文件路径|文件路径',
  `SOURCE_TYPE` varchar(10) NOT NULL COMMENT '渠道类型|发起渠道',
  `TOTAL_NUMBER` bigint DEFAULT NULL COMMENT '文件总笔数|总记录数',
  `BRANCH_ID` varchar(50) NOT NULL COMMENT '机构号|发起机构',
  `DEAL_FLAG` char(1) NOT NULL COMMENT '处理标识|处理标识|0-未处理  ,1-已处理 ,2处理中，3无法处理',
  `KEY_FIELD` varchar(30) NOT NULL COMMENT '分段主键|分段关键字',
  `FILE_SEGMENT_SIZE` int NOT NULL COMMENT '文件分段大小|文件分段大小',
  `FILE_FORMAT` varchar(50) NOT NULL COMMENT '文件格式|文件模板',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `USER_ID` varchar(30) NOT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `CREATE_TIME` varchar(26) NOT NULL COMMENT '创建时间|登记时间',
  `FILE_MD5` varchar(200) DEFAULT NULL COMMENT '文件MD5校验值|文件MD5校验值',
  `FILE_TYPE` varchar(50) DEFAULT NULL COMMENT '文件类型|文件类型',
  `STEP_TYPE` varchar(50) DEFAULT NULL COMMENT 'step类型|step类型',
  `ASYNC_REASON` char(1) DEFAULT NULL COMMENT '进入异步处理原因|1-当前并发数过大 ,2-本次文件记录数过大',
  `SEGMENT_SIZE` int DEFAULT NULL COMMENT '分段大小|分段大小',
  `TABLE_NAME` varchar(50) DEFAULT NULL COMMENT '表名|表名',
  `STEP_NAME` varchar(50) DEFAULT NULL COMMENT 'step名称|step名称',
  `RUN_TYPE` char(1) DEFAULT NULL COMMENT '定时任务运行类型|1---普通任务0---定时任务',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='联机批量任务暂存表|存放联机批量未处理的数据';

-- ----------------------------
-- Table structure for rb_batch_bab_internal_result
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_bab_internal_result`;
CREATE TABLE `rb_batch_bab_internal_result` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `HANG_WRITE_OFF_FLAG` char(1) DEFAULT NULL COMMENT '挂销账标志|挂销账标志|H-挂帐,C-销账',
  `HANG_TERM` varchar(5) DEFAULT NULL COMMENT '挂账期限|挂账期限',
  `OD_FACILITY` char(1) DEFAULT NULL COMMENT '是否可透支|是否可透支|Y-是,N-否',
  `COUNTER_DEP_FLAG` char(1) DEFAULT NULL COMMENT '是否允许柜面跨行存入许可标识|是否允许柜面跨行存入许可标识|Y-允许,N-不允许',
  `COUNTER_DEBT_FLAG` char(1) DEFAULT NULL COMMENT '是否允许柜面跨行支取许可标识|是否允许柜面跨行支取许可标识|Y-允许,N-不允许',
  `RET_CODE` varchar(50) DEFAULT NULL COMMENT '状态码|状态码',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `APPROVAL_NO` varchar(50) DEFAULT NULL COMMENT '审批单号|审批单号',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `BRANCH_NAME` varchar(200) DEFAULT NULL COMMENT '机构名称|机构名称',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|所属机构号',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='内部户批量维护登记表|登记内部户批量维护信息';

-- ----------------------------
-- Table structure for rb_batch_balance_query
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_balance_query`;
CREATE TABLE `rb_batch_balance_query` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_SEQ_NO` varchar(50) NOT NULL COMMENT '批次明细序号|批次明细序号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `TOTAL_AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '汇总金额|汇总金额',
  `TOTAL_AMOUNT_PREV` decimal(17,2) DEFAULT NULL COMMENT '上日总金额|上日总金额',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  PRIMARY KEY (`BATCH_NO`,`BATCH_SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量余额查询表|批量余额查询';

-- ----------------------------
-- Table structure for rb_batch_charge_fee
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_charge_fee`;
CREATE TABLE `rb_batch_charge_fee` (
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `FEE_TYPE` varchar(20) NOT NULL COMMENT '费用类型|费率类型',
  `TRAN_AMT` decimal(17,2) NOT NULL COMMENT '交易金额|交易金额',
  `CHARGE_BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '手续费收取账号|手续费收取账号',
  `INC_EXP_TYPE` varchar(10) DEFAULT NULL COMMENT '收支类型|收支类型，通过此字段判断收入方，还是支出方|I-收入,E-支出',
  `CHARGE_METHOD` char(1) NOT NULL COMMENT '手续费类型|手续费类型|A-即收即付,B-预收手续费',
  `CHARGE_MODE` char(1) NOT NULL COMMENT '收取标志|收取标志|C-现金收取,T-转账收取,N-暂不收取,P-套餐内抵用',
  `BATCH_FILE_STATUS` char(1) DEFAULT NULL COMMENT '批处理文件处理状态|批处理文件处理状态|N-新建,S-成功,F-失败',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `AMORT_END` datetime DEFAULT NULL COMMENT '摊销截止日期|摊销截止日期',
  `AMORT_START` datetime DEFAULT NULL COMMENT '摊销起始日期|摊销起始日期',
  `FEE_CHARGE_METHOD` char(1) DEFAULT NULL COMMENT '手续费收取方式|手续费收取方式|0-即收即付手续费 ,1-预收手续费',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `NARRATIVE_CODE` varchar(30) DEFAULT NULL COMMENT '摘要码|摘要码',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `RESERVE_FLAG` char(1) DEFAULT NULL COMMENT '冲正标志|冲正标志|Y-已冲正,N-未冲正',
  `SC_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '收费序号|收费序号',
  `REVERSAL_FLAG` char(1) DEFAULT NULL COMMENT '交易是否已冲正|交易是否已冲正|Y-是,N-否 ',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量手续费收取明细表|批量收取手续费时候插入';

-- ----------------------------
-- Table structure for rb_batch_check_cardinfo
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_check_cardinfo`;
CREATE TABLE `rb_batch_check_cardinfo` (
  `OUT_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '转出账户名称|转出账户名称',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `PHONE_NO` varchar(20) DEFAULT NULL COMMENT '固定电话|固定电话',
  `CARD_NO` varchar(50) DEFAULT NULL COMMENT '卡号|卡号',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `ACCT_TYPE` char(1) DEFAULT NULL COMMENT '账户类型|账户类型|A-AIO账户,C-结算账户,S-储蓄账户,T-定期账户',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `ACCT_CLASS` char(1) DEFAULT NULL COMMENT '账户类别|账户类别，用于区分账户类别（一二三类户），满足人行对于电子账户的管理办法|1-一类账户,2-二类账户,3-三类账户',
  `CREDIT_CARD_NO` varchar(50) DEFAULT NULL COMMENT '信用卡号|信用卡号',
  `DOCUMENT_ID1` varchar(50) DEFAULT NULL COMMENT '证件号1|证件号1',
  `DOCUMENT_TYPE1` varchar(3) DEFAULT NULL COMMENT '证件类型1|证件类型，指的是人或者单位的某种身份证明的类型，比如身份证、经营许可证、军人证等',
  `PHONE_NO1` varchar(20) DEFAULT NULL COMMENT '电话号码 1|电话号码 1',
  `PASS_FLAG` char(1) DEFAULT NULL COMMENT '通过标记|通过标记|Y-是,N-否',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='委托关系验证|委托关系验证时插入';

-- ----------------------------
-- Table structure for rb_batch_cret_res
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_cret_res`;
CREATE TABLE `rb_batch_cret_res` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_SEQ_NO` varchar(50) NOT NULL COMMENT '批次明细序号|批次明细序号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `START_DATE` datetime DEFAULT NULL COMMENT '开始日期|开始日期',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|结束日期',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `OTH_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '对方账号/卡号|对方账号/卡号',
  `OTH_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '对方账户产品类型|对方账户产品类型',
  `OTH_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '对方账户币种|对方账户币种',
  `OTH_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '对方账户序列号|对方账户序列号',
  `RESTRAINT_TYPE` varchar(3) DEFAULT NULL COMMENT '限制类型|限制类型',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `NARRATIVE_CODE` varchar(30) DEFAULT NULL COMMENT '摘要码|摘要码',
  PRIMARY KEY (`BATCH_NO`,`BATCH_SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量上账冻结表';

-- ----------------------------
-- Table structure for rb_batch_deduction_details
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_deduction_details`;
CREATE TABLE `rb_batch_deduction_details` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_SEQ_NO` varchar(50) NOT NULL COMMENT '批次明细序号|批次明细序号',
  `LOAN_NO` varchar(50) DEFAULT NULL COMMENT '贷款号|贷款号',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `SETTLE_METHOD` varchar(3) DEFAULT NULL COMMENT '结算方法|结算方法|R-结算户,I-内部户,N-往账,V-来账',
  `SETTLE_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '结算账号|结算账号',
  `SETTLE_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '结算账户产品类型|结算账户产品类型',
  `SETTLE_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '结算账户币种|结算账户币种',
  `SETTLE_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '结算账户序号|结算账户序号',
  `SETTLE_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '结算账户户名|结算账户户名',
  `SETTLE_CLIENT` varchar(20) DEFAULT NULL COMMENT '结算客户号|结算客户号',
  `SETTLE_AMT` decimal(17,2) DEFAULT NULL COMMENT '结算金额|结算金额',
  `OTH_SETTLE_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '对手结算账号|对手结算账号',
  `OTH_SETTLE_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '对手结算账户产品类型|对手结算账户产品类型',
  `OTH_SETTLE_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '对手结算账户币种|对手结算账户币种',
  `OTH_SETTLE_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '对手结算账户序号|对手结算账户序号',
  `OTH_SETTLE_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '对手结算账户户名|对手结算账户户名',
  `OTH_SETTLE_CLIENT` varchar(20) DEFAULT NULL COMMENT '对手结算客户号|对手结算客户号',
  `OPPO_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '行外对手账号|行外对手账号',
  `OPPO_BANK_CODE` varchar(20) DEFAULT NULL COMMENT '行外对手账户行号|行外对手账户行号',
  `OPPO_BANK_NAME` varchar(50) DEFAULT NULL COMMENT '行外对手账户行名|行外对手账户行名',
  `PRIORITY` varchar(20) DEFAULT NULL COMMENT '优先级|优先级',
  `REC_AMT_CTRL` char(1) DEFAULT NULL COMMENT '还款金额控制标志|扣款方式|A-足额扣除,P-部分扣款',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `SUB_SEQ_NO` varchar(100) DEFAULT NULL COMMENT '子流水号|子流水号',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ACTUAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '实际金额|实际金额',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`,`BATCH_SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='贷款批量扣款表|批量扣款文件明细信息，贷款专用\r\n';

-- ----------------------------
-- Table structure for rb_batch_doss
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_doss`;
CREATE TABLE `rb_batch_doss` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `TOTAL_COUNT` int DEFAULT NULL COMMENT '总笔数|总笔数',
  `TOTAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '总金额|总金额',
  `PER_CNT_DOSS` int DEFAULT NULL COMMENT '对私转久悬笔数|对私转久悬笔数',
  `PER_AMT_DOSS` decimal(17,2) DEFAULT NULL COMMENT '对私转久悬金额|对私转久悬金额',
  `PER_GL_CODE_BAL` decimal(17,2) DEFAULT NULL COMMENT '对私久悬科目昨日余额|对私久悬科目昨日余额',
  `PER_AMT_DOSS_HIS` decimal(17,2) DEFAULT NULL COMMENT '对私转久悬历史批次总金额|对私转久悬历史批次总金额',
  `COM_CNT_DOSS` int DEFAULT NULL COMMENT '对公转久悬笔数|对公转久悬笔数',
  `COM_AMT_DOSS` decimal(17,2) DEFAULT NULL COMMENT '对公转久悬金额|对公转久悬金额',
  `COM_GL_CODE_BAL` decimal(17,2) DEFAULT NULL COMMENT '对公久悬科目昨日余额|对公久悬科目昨日余额',
  `COM_CNT_OUT` int DEFAULT NULL COMMENT '对公转营业外笔数|对公转营业外笔数',
  `COM_AMT_OUT` decimal(17,2) DEFAULT NULL COMMENT '对公转营业外金额|对公转营业外金额',
  `NON_TRANSPLANT_FLAG` char(1) DEFAULT NULL COMMENT '是否未移植数据|是否未移植数据|Y-是,N-否',
  `BATCH_ERROR` varchar(2000) DEFAULT NULL COMMENT '批次错误信息|批次错误信息',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `TRAN_USER` varchar(30) DEFAULT NULL COMMENT '导入柜员|导入柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='久悬户导入批次汇总表';

-- ----------------------------
-- Table structure for rb_batch_doss_acct
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_doss_acct`;
CREATE TABLE `rb_batch_doss_acct` (
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `DOSS_OPERATE_TYPE` varchar(2) DEFAULT NULL COMMENT '转久悬操作类型|转久悬操作类型|DS-不动转久悬,DW-不动户转待转久悬,WS-待转久悬转久悬,SW-久悬转待转营业外,WO-待转营业外转营业外,SA-账户激活,SC-久悬户销户,OC-营业外销户,WSC-待转久悬销户,WOC-待转营业外销户',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `IMPORT_TYPE` varchar(2) DEFAULT NULL COMMENT '导入类型|导入类型|S-久悬户,S-待转营业外',
  `INDIVIDUAL_FLAG` char(1) DEFAULT NULL COMMENT '对公对私标志|对公对私标志|Y-对私,N-对公',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `INTERNAL_KEY` bigint DEFAULT NULL COMMENT '账户内部键值|账户内部键值',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `ACCT_TYPE` char(1) DEFAULT NULL COMMENT '账户类型|账户类型|A-AIO账户,C-结算账户,S-储蓄账户,T-定期账户',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `BALANCE` decimal(17,2) DEFAULT NULL COMMENT '余额|余额',
  `INT_AMT` decimal(17,2) DEFAULT NULL COMMENT '利息金额|利息金额',
  `POR_INT_TOT` decimal(17,2) DEFAULT NULL COMMENT '本息合计|本息合计',
  `DOSS_DATE` datetime DEFAULT NULL COMMENT '转久悬日期|转久悬日期',
  `OUT_DATE` datetime DEFAULT NULL COMMENT '出库日期|出库日期',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='久悬户批量导入明细表|久悬户批量导入';

-- ----------------------------
-- Table structure for rb_batch_doss_indvl
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_doss_indvl`;
CREATE TABLE `rb_batch_doss_indvl` (
  `TRAN_BRANCH` varchar(50) NOT NULL COMMENT '交易机构|交易机构',
  `COM_DOSS_FLAG` char(1) NOT NULL COMMENT '对公转久悬导入标志|通过该标志控制该机构对公久悬户。若已导入完成，则不允许再次导入|Y-已导入,N-未导入',
  `PER_AMT_DOSS` decimal(17,2) DEFAULT NULL COMMENT '对私转久悬金额|对私转久悬金额',
  `PER_AMT_TOT` decimal(17,2) DEFAULT NULL COMMENT '对私转久悬总金额|对私转久悬总金额',
  `PER_AMT_WITHDRAW` decimal(17,2) DEFAULT NULL COMMENT '对私久悬户转出总金额|对私久悬户转出总金额',
  `LAST_CHANGE_DATE` datetime DEFAULT NULL COMMENT '最后修改日期|最后修改日期',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`TRAN_BRANCH`,`COM_DOSS_FLAG`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='久悬户导入信息统计表(只含已成功导入)|久悬户综合处理会做更新';

-- ----------------------------
-- Table structure for rb_batch_file_hist
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_file_hist`;
CREATE TABLE `rb_batch_file_hist` (
  `NAME` varchar(200) NOT NULL COMMENT '名称|名称',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `BATCH_FILE_STATUS` char(1) DEFAULT NULL COMMENT '批处理文件处理状态|批处理文件处理状态|N-新建,S-成功,F-失败',
  `STEP_NAME` varchar(50) DEFAULT NULL COMMENT 'step名称|step名称',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `JOB_ID` varchar(50) DEFAULT NULL COMMENT '批量任务编号 |批量任务编号 ',
  PRIMARY KEY (`NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量文件读取历史表|记录批量处理后的文件';

-- ----------------------------
-- Table structure for rb_batch_financial_details
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_financial_details`;
CREATE TABLE `rb_batch_financial_details` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `PLD_AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '冻结金额|冻结金额',
  `RES_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '限制编号|限制编号',
  `RESTRAINT_TYPE` varchar(3) DEFAULT NULL COMMENT '限制类型|限制类型',
  `START_DATE` datetime DEFAULT NULL COMMENT '开始日期|开始日期',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|结束日期',
  `EVENT_TYPE` varchar(20) DEFAULT NULL COMMENT '事件类型|事件类型',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `STATUS` char(1) DEFAULT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账,N-新增,U-修改,D-删除,C-非活动状态',
  `BATCH_NO` varchar(50) DEFAULT NULL COMMENT '批次号|批次号',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `BUSI_TYPE` varchar(20) DEFAULT NULL COMMENT '业务种类|业务种类',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `SUB_CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '子渠道流水号|子渠道流水号',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量记账交易子表|批量记账交易子表';

-- ----------------------------
-- Table structure for rb_batch_financial_tran_status
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_financial_tran_status`;
CREATE TABLE `rb_batch_financial_tran_status` (
  `REFERENCE` varchar(50) NOT NULL COMMENT '交易参考号|交易参考号',
  `STATUS` char(1) DEFAULT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账,N-新增,U-修改,D-删除,C-非活动状态',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`REFERENCE`,`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量记账交易主表|批量记账交易主表';

-- ----------------------------
-- Table structure for rb_batch_foundation_details
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_foundation_details`;
CREATE TABLE `rb_batch_foundation_details` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `CHANNEL` varchar(10) DEFAULT NULL COMMENT '渠道|渠道细类|JZ-集中作业,MT-柜面,ZD-智能网点自助交易平台,VT-远程智能自助银行,QT-XBankT10,QE-快窗智能高柜,CP-手机银行,EB-个人网上银行,CB-企业网上银行,CM-现金管理,WB-微信银行',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `UNFROZEN_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '解冻流水号|解冻流水号',
  `UNFROZEN_FLAG` char(1) DEFAULT NULL COMMENT '解冻标志|解冻标志|N-否,Y-是',
  `UNFROZEN_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '解冻账户/卡号|解冻账户/卡号',
  `UNFROZEN_PB_FLAG` char(1) DEFAULT NULL COMMENT '解冻账户卡折标志|解冻账户卡折标志|N-否,Y-是',
  `UNFROZEN_CCY` varchar(3) DEFAULT NULL COMMENT '解冻账户币种|解冻账户币种',
  `TRANSFER_FLAG` char(1) DEFAULT NULL COMMENT '转账标志|转账标志|Y-是,N-否',
  `DR_CARD_PB_IND` char(1) DEFAULT NULL COMMENT '借方账户卡折标志|借方账户卡折标志|C-卡,P-折',
  `DR_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '借方账号|借方账号',
  `DR_ACCT_TYPE` char(1) DEFAULT NULL COMMENT '转出账户类型|转出账户类型|A-AIO账户,C-结算账户,S-储蓄账户,T-定期账户',
  `DR_TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '转出交易类型|转出交易类型',
  `TRANSFER_AMT` decimal(17,2) DEFAULT NULL COMMENT '划转金额 |转账金额',
  `CR_CARD_PB_IND` char(1) DEFAULT NULL COMMENT '转入账户卡折标志|转入账户卡折标志|C-卡,P-折',
  `CR_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '贷方账号|贷方账号',
  `CR_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '贷方账户币种|贷方账户币种',
  `CR_ACCT_TYPE` char(1) DEFAULT NULL COMMENT '收款账户类型（行内账户）|收款账户类型（行内账户）|A-AIO账户,C-结算账户,S-储蓄账户,T-定期账户',
  `CR_TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '转入交易类型|转入交易类型',
  `BAL_TYPE` varchar(2) DEFAULT NULL COMMENT '余额类型|余额类型|TT-汇余额,CA-钞余额',
  `FROZEN_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '冻结流水号|冻结流水号',
  `FROZEN_PB_FLAG` char(1) DEFAULT NULL COMMENT '冻结账户卡折标志|冻结账户卡折标志|Y-是,N-否',
  `FROZEN_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '冻结账户|冻结账户',
  `FROZEN_CCY` varchar(3) DEFAULT NULL COMMENT '冻结账户币种|冻结账户币种',
  `FROZEN_ACCT_TYPE` char(1) DEFAULT NULL COMMENT '冻结账户类型|冻结账户类型|A-AIO账户,C-结算账户,S-储蓄账户,T-定期账户',
  `RESTRAINT_TYPE` varchar(3) DEFAULT NULL COMMENT '限制类型|限制类型',
  `START_DATE` datetime DEFAULT NULL COMMENT '开始日期|开始日期',
  `TERM` varchar(5) DEFAULT NULL COMMENT '存期期限|存期',
  `TERM_TYPE` char(1) DEFAULT NULL COMMENT '期限类型|存期类型|Y-年,Q-季,M-月,W-周,D-日',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|结束日期',
  `PLEDGED_AMT` decimal(17,2) DEFAULT NULL COMMENT '限制金额|限制金额',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `RES_FLAG` char(1) DEFAULT NULL COMMENT '冻结标志|冻结标志|Y-是 ,N-不是',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `CREDIT_CARD_NO` varchar(50) DEFAULT NULL COMMENT '信用卡号|信用卡号',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `REMARK2` varchar(200) DEFAULT NULL COMMENT '备注2|备注2',
  `REMARK3` varchar(200) DEFAULT NULL COMMENT '备注3|备注3',
  `RET_CODE` varchar(50) DEFAULT NULL COMMENT '状态码|状态码',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `NARRATIVE_CODE` varchar(30) DEFAULT NULL COMMENT '摘要码|摘要码',
  PRIMARY KEY (`SEQ_NO`,`BATCH_NO`),
  KEY `IDX_RB_BATCH_FOUNDATION_DTL_2M` (`CHANNEL_SEQ_NO`,`TRAN_DATE`),
  KEY `IDX_RB_BATCH_FOUNDATION_DTL_1M` (`TRAN_DATE`,`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='基金批量交易信息明细登记簿|理财平台资金清算（冻结）';

-- ----------------------------
-- Table structure for rb_batch_internal_close_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_internal_close_detail`;
CREATE TABLE `rb_batch_internal_close_detail` (
  `APPROVAL_NO` varchar(50) NOT NULL COMMENT '审批单号|审批单号',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_NO` varchar(50) DEFAULT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) DEFAULT NULL COMMENT '序号|序号',
  `BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_SEQ_NO` varchar(5) NOT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `RET_CODE` varchar(50) DEFAULT NULL COMMENT '状态码|状态码',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `CURRENT_ACCT_OPERATE_TYPE` char(1) DEFAULT NULL COMMENT '活期开户（账务处理为tae方式调用操作类型|活期开户（账务处理为tae方式调用操作类型|C-新增,W-非新增',
  `ACCT_OPERATE_TYPE` char(1) DEFAULT NULL COMMENT '账户操作类型|账户操作类型',
  PRIMARY KEY (`APPROVAL_NO`,`BASE_ACCT_NO`,`ACCT_SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='内部户批量销户重开登记表|内部户批量销户重开登记表';

-- ----------------------------
-- Table structure for rb_batch_law_attach
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_law_attach`;
CREATE TABLE `rb_batch_law_attach` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `MAIN_FILE_NAME` varchar(200) DEFAULT NULL COMMENT '汇总文件名称|汇总文件名称',
  `DETAIL_FILE_NAME` varchar(200) DEFAULT NULL COMMENT '明细文件名称|明细文件名称',
  `LAW_NO` varchar(200) NOT NULL COMMENT '法律文书号|法律文书号',
  `DEDUCTION_JUDICIARY_NAME` varchar(200) NOT NULL COMMENT '有权机关名称|有权机关名称',
  `JUDICIARY_OFFICER_NAME` varchar(200) NOT NULL COMMENT '执法人1姓名|执法人1姓名',
  `JUDICIARY_DOCUMENT_TYPE` varchar(3) NOT NULL COMMENT '执法人1证件类型|执法人1证件类型',
  `JUDICIARY_DOCUMENT_ID` varchar(50) NOT NULL COMMENT '执法人1证件号码|执法人1证件号码',
  `JUDICIARY_OTH_OFFICER_NAME` varchar(200) DEFAULT NULL COMMENT '执法人2姓名|执法人2姓名',
  `JUDICIARY_OTH_DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '执法人2证件类型|执法人2证件类型',
  `JUDICIARY_OTH_DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '执法人2证件号码|执法人2证件号码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `JUDICIARY_DOCUMENT_ID1` varchar(50) DEFAULT NULL COMMENT '执法人1证件号码1|执法人1证件号码1',
  `JUDICIARY_DOCUMENT_TYPE1` varchar(3) DEFAULT NULL COMMENT '执法人1证件类型1|执法人1证件类型1',
  `JUDICIARY_OTH_DOCUMENT_TYPE1` varchar(3) DEFAULT NULL COMMENT '执法人2证件类型1|执法人2证件类型1',
  `JUDICIARY_OTH_DOCUMENT_ID1` varchar(50) DEFAULT NULL COMMENT '执法人2证件号码1|执法人2证件号码1',
  `DEDUCTION_JUDICIARY_PART` varchar(200) DEFAULT NULL COMMENT '执法部门|执法部门',
  `CASE_TYPE` varchar(5) DEFAULT NULL COMMENT '案件类型|案件类型',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量司法查询附加表|批量司法查询联机交易，登记法律文书相关信息';

-- ----------------------------
-- Table structure for rb_batch_law_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_law_detail`;
CREATE TABLE `rb_batch_law_detail` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `BAL_TYPE` varchar(2) DEFAULT NULL COMMENT '余额类型|余额类型|TT-汇余额,CA-钞余额',
  `ACTUAL_BAL` decimal(17,2) DEFAULT NULL COMMENT '实际余额|实际余额',
  `CRET_AMT` decimal(17,2) DEFAULT NULL COMMENT '存入金额|存入金额',
  `DEBT_AMT` decimal(17,2) DEFAULT NULL COMMENT '支取金额|支取金额',
  `CASH_TRAN_FLAG` char(1) DEFAULT NULL COMMENT '现金交易|现金交易|Y-是 ,N-否',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `VOUCHER_NO` varchar(50) DEFAULT NULL COMMENT '凭证号码|凭证号码',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `AUTH_USER_ID` varchar(30) DEFAULT NULL COMMENT '授权柜员|授权柜员',
  `TRAN_HIST_TIME` varchar(26) DEFAULT NULL COMMENT '交易历史时间|交易历史时间',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量司法查询结果明细表|批量司法查询执行类，插入批量司法流水';

-- ----------------------------
-- Table structure for rb_batch_law_main
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_law_main`;
CREATE TABLE `rb_batch_law_main` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `DOCUMENT_ID` varchar(50) NOT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) NOT NULL COMMENT '证件类型|证件类型',
  `ISS_COUNTRY` varchar(3) NOT NULL COMMENT '发证国家|发证国家',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `ACCT_OPEN_DATE` datetime DEFAULT NULL COMMENT '账户开户日期|账户开户日期',
  `ACTUAL_BAL` decimal(17,2) DEFAULT NULL COMMENT '实际余额|实际余额',
  `TOTAL_AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '汇总金额|汇总金额',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量司法查询结果汇总表|批量司法查询执行类，插入批量司法流水证件信息';

-- ----------------------------
-- Table structure for rb_batch_law_query
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_law_query`;
CREATE TABLE `rb_batch_law_query` (
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `DOCUMENT_ID` varchar(50) NOT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) NOT NULL COMMENT '证件类型|证件类型',
  `ISS_COUNTRY` varchar(3) NOT NULL COMMENT '发证国家|发证国家',
  `LAW_QUERY_STATUS` char(1) DEFAULT NULL COMMENT '司法查询状态|司法查询状态|N-未处理,F-失败,S-成功',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `QUERY_TYPE` varchar(10) DEFAULT NULL COMMENT '查询类型|查询类型',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `STATUS` char(1) DEFAULT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账,N-新增,U-修改,D-删除,C-非活动状态',
  `START_DATE` datetime DEFAULT NULL COMMENT '开始日期|开始日期',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|结束日期',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `DETAILS_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '明细序号|明细序号',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量司法查询明细表|批量开立司法查询，批量司法查询插入明细数据';

-- ----------------------------
-- Table structure for rb_batch_online_result
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_online_result`;
CREATE TABLE `rb_batch_online_result` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_CLASS` varchar(10) DEFAULT NULL COMMENT '批次类型|批次类型|BATCH1-批量开立客户,BATCH2-批量开立账户,BATCH3-贷款批量开立/发放,BATCH4-批量司法查询,BATCH5-批量转账,BATCH6-批量开立内部账户（贷款贴现批量开立）,BATCH7-贷款批量核销,BATCH8-批量开立内部账户,BATCH9-批量开立存单,BATCH10-黑白名单批量导入,BATCH11-贷款批量发放核销,BATCH12-批量续开内部账,BATCH13-久悬户批量导入,BATCH14-批量冻结,BATCH15-批量解冻扣款,BATCH16-基金资金清算,BATCH17-社保卡批量开户开卡,BATCH18-CDCT对账,BATCH19-理财平台资金清算,BATCH20-日终签约文件同步,BATCH21-批量收取手续费,BATCH22-IC卡商户批量入账,BATCH23-委托关系验证,BATCH24-ic卡对账文件,BATCH25-IC卡挂失销户到期处理文件请求文件,BATCH26-生成指定表文件,BATCH27-批量冲正',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|渠道类型',
  `FILE_NAME` varchar(200) DEFAULT NULL COMMENT '文件名称|文件名称',
  `FILE_PATH` varchar(200) DEFAULT NULL COMMENT '文件路径|文件路径',
  `STEP_DESC` varchar(50) DEFAULT NULL COMMENT 'STEP描述|STEP描述',
  `TRAN_STATUS` char(1) DEFAULT NULL COMMENT '业务处理状态|业务处理状态|N-正常,X-被冲正,R-冲正',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='联机批量结果文件登记表|联机批量结果文件登记表（批量收取手续费结果文件生成，久悬户批量导入结果文件生成，批量开立账户结果文件生成，批量开立存单结果文件生成，批量内部户续开结果文件生成，批量转账结果文件生成，批量开立结果文件生成）';

-- ----------------------------
-- Table structure for rb_batch_online_status
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_online_status`;
CREATE TABLE `rb_batch_online_status` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `STEP_TYPE` varchar(50) NOT NULL COMMENT 'step类型|step类型',
  `RUN_DATE` datetime DEFAULT NULL COMMENT '运行日期|交易日期',
  `TRAN_STATUS` char(1) DEFAULT NULL COMMENT '业务处理状态|业务处理状态|N-正常,X-被冲正,R-冲正,S-成功,F-失败',
  `BRANCH_ID` varchar(50) DEFAULT NULL COMMENT '机构号|机构代码',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|渠道类型',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `BATCH_CLASS` varchar(10) DEFAULT NULL COMMENT '批次类型|批次类型|BATCH1-批量开立客户,BATCH2-批量开立账户,BATCH3-贷款批量开立/发放,BATCH4-批量司法查询,BATCH5-批量转账,BATCH6-批量开立内部账户（贷款贴现批量开立）,BATCH7-贷款批量核销,BATCH8-批量开立内部账户,BATCH9-批量开立存单,BATCH10-黑白名单批量导入,BATCH11-贷款批量发放核销,BATCH12-批量续开内部账,BATCH13-久悬户批量导入,BATCH14-批量冻结,BATCH15-批量解冻扣款,BATCH16-基金资金清算,BATCH17-社保卡批量开户开卡,BATCH18-CDCT对账,BATCH19-理财平台资金清算,BATCH20-日终签约文件同步,BATCH21-批量收取手续费,BATCH22-IC卡商户批量入账,BATCH23-委托关系验证,BATCH24-ic卡对账文件,BATCH25-IC卡挂失销户到期处理文件请求文件,BATCH26-生成指定表文件,BATCH27-批量冲正',
  PRIMARY KEY (`BATCH_NO`,`STEP_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='文件登记状态表|文件登记状态表';

-- ----------------------------
-- Table structure for rb_batch_open
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_open`;
CREATE TABLE `rb_batch_open` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) DEFAULT NULL COMMENT '序号|序号',
  `BATCH_DESC` varchar(50) DEFAULT NULL COMMENT '批处理描述|批处理描述',
  `TRAN_MODE` char(1) DEFAULT NULL COMMENT '交易模式|交易模式|W-一借多贷 ,M-一贷多借',
  `OPEN_DATE` datetime DEFAULT NULL COMMENT '开立日期|开立日期',
  `OPEN_TYPE` varchar(3) DEFAULT NULL COMMENT '批量开立方式|批量开立方式|EOD-EOD生成,INP-异步在线生成',
  `FILE_NAME` varchar(200) DEFAULT NULL COMMENT '文件名称|文件名称',
  `FILE_PATH` varchar(200) DEFAULT NULL COMMENT '文件路径|文件路径',
  `TOTAL_NUM` int DEFAULT NULL COMMENT '总数量|总数量',
  `TOTAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '总金额|总金额',
  `OPEN_BRANCH` varchar(50) DEFAULT NULL COMMENT '开立机构|开立机构',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `CLIENT_TYPE` varchar(3) NOT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `CATEGORY_TYPE` varchar(3) NOT NULL COMMENT '客户细分类型|客户细分类型',
  `CONTACT_TYPE` varchar(20) NOT NULL COMMENT '联系类型|联系类型',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `CARD_PB_IND` char(1) DEFAULT NULL COMMENT '卡/折标志|卡/折标志|C-卡,P-折',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `GAIN_TYPE` char(1) DEFAULT NULL COMMENT '卡片领取方式|卡片领取方式|O-自取,M-邮寄',
  `OPEN_CCY` varchar(3) DEFAULT NULL COMMENT '批量开户币种|批量开户币种',
  `COPR_NAME` varchar(200) DEFAULT NULL COMMENT '单位名称|单位名称',
  `WITHDRAWAL_TYPE` char(1) DEFAULT NULL COMMENT '支取方式|支取方式|S-凭印鉴支取,P-凭密码支取,W-无密码无印鉴支取,B-凭印鉴和密码支取,O-凭证件支取',
  `FROM_CARD_NO` varchar(50) DEFAULT NULL COMMENT '转出卡号|转出卡号',
  `TO_CARD_NO` varchar(50) DEFAULT NULL COMMENT '终止卡号|终止卡号',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `SUCC_NUM` int DEFAULT NULL COMMENT '成功数量|成功数量',
  `FAILURE_NUMBER` int DEFAULT NULL COMMENT '失败数量|失败数量',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `SERVER_IP` varchar(50) DEFAULT NULL COMMENT '后台服务IP|后台服务IP',
  `SOURCE_BRANCH_NO` varchar(50) DEFAULT NULL COMMENT '源节点编号|源节点编号',
  `DEST_BRANCH_NO` varchar(50) DEFAULT NULL COMMENT '目标机构编号|目标机构编号',
  `THREAD_NO` varchar(50) DEFAULT NULL COMMENT '线程编号|线程编号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `USER_LANG` varchar(30) DEFAULT NULL COMMENT '柜员语言|柜员语言|E-英文,C-中文',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|渠道类型',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `MAC_VALUE` varchar(200) DEFAULT NULL COMMENT '传输密押|传输密押',
  `SYSTEM_ID` varchar(20) DEFAULT NULL COMMENT '系统ID|系统ID',
  `RUN_DATE` datetime DEFAULT NULL COMMENT '运行日期|交易日期',
  `APPR_FLAG` char(1) DEFAULT NULL COMMENT '复核标志|复核标志|Y-已复核,N-未复核',
  `AUTH_FLAG` char(1) DEFAULT NULL COMMENT '授权标志|授权标志|Y-已授权,N-未授权',
  `APPR_USER_ID` varchar(30) DEFAULT NULL COMMENT '复核柜员|复核柜员',
  `AUTH_USER_ID` varchar(30) DEFAULT NULL COMMENT '授权柜员|授权柜员',
  `BEGIN_TIME` varchar(26) DEFAULT NULL COMMENT '开始时间|开始时间',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量开立信息登记簿';

-- ----------------------------
-- Table structure for rb_batch_open_dct_details
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_open_dct_details`;
CREATE TABLE `rb_batch_open_dct_details` (
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `SUB_SEQ_NO` varchar(100) DEFAULT NULL COMMENT '子流水号|子流水号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `OPEN_BRANCH` varchar(50) DEFAULT NULL COMMENT '开立机构|开立机构',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `ISS_COUNTRY` varchar(3) DEFAULT NULL COMMENT '发证国家|发证国家',
  `DOCUMENT_EXPIRY_DATE` datetime DEFAULT NULL COMMENT '证件失效日期|证件的到期日期',
  `BIRTHDAY` datetime DEFAULT NULL COMMENT '生日|出生日期',
  `SEX` char(1) DEFAULT NULL COMMENT '性别|性别|M-男,F-女',
  `CH_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户中文名称|客户中文名称',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `LOCATION` varchar(200) DEFAULT NULL COMMENT '客户地址|客户地址',
  `OCCUPATION_CODE` varchar(10) DEFAULT NULL COMMENT '职业|职业',
  `INLAND_OFFSHORE` char(1) DEFAULT NULL COMMENT '境内境外标志|境内境外标志|I-境内,O-境外',
  `EMAIL` varchar(200) DEFAULT NULL COMMENT '电子邮件|电子邮件',
  `MOBILE_NO` varchar(30) DEFAULT NULL COMMENT '电话号码|电话号码',
  `CONTACT_TYPE` varchar(20) DEFAULT NULL COMMENT '联系类型|联系类型',
  `PHONE_NO` varchar(20) DEFAULT NULL COMMENT '固定电话|固定电话',
  `CONTACT_ADDRESS` varchar(500) DEFAULT NULL COMMENT '公司联系地址|公司联系地址',
  `COPR_NAME` varchar(200) DEFAULT NULL COMMENT '单位名称|单位名称',
  `CARD_PB_IND` char(1) DEFAULT NULL COMMENT '卡/折标志|卡/折标志|C-卡,P-折',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `OPEN_CCY` varchar(3) DEFAULT NULL COMMENT '批量开户币种|批量开户币种',
  `TERM` varchar(5) DEFAULT NULL COMMENT '存期期限|存期',
  `TERM_TYPE` char(1) DEFAULT NULL COMMENT '期限类型|存期类型|Y-年,Q-季,M-月,W-周,D-日',
  `ACCT_NATURE` varchar(10) DEFAULT NULL COMMENT '账户属性|账户属性',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `WITHDRAWAL_TYPE` char(1) DEFAULT NULL COMMENT '支取方式|支取方式|S-凭印鉴支取,P-凭密码支取,W-无密码无印鉴支取,B-凭印鉴和密码支取,O-凭证件支取',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `PREFIX` varchar(10) DEFAULT NULL COMMENT '前缀|前缀',
  `VOUCHER_NO` varchar(50) DEFAULT NULL COMMENT '凭证号码|凭证号码',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `BAL_TYPE` varchar(2) DEFAULT NULL COMMENT '余额类型|余额类型|TT-汇余额,CA-钞余额',
  `AUTO_RENEW_ROLLOVER` char(1) DEFAULT NULL COMMENT '是否自动转存|定期是否自动转存|N-不自动转存,W-本金自动转存,O-本息自动转存',
  `CASH_ITEM` varchar(10) DEFAULT NULL COMMENT '现金项目|现金项目',
  `OTH_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '对方账号|对方账号',
  `OTH_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '对方账户产品类型|对方账户产品类型',
  `OTH_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '对方账户币种|对方账户币种',
  `OTH_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '对方账户序列号|对方账户序列号',
  `OTH_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '对方账户名称|对方账户名称',
  `OTH_BAL_TYPE` varchar(2) DEFAULT NULL COMMENT '对方余额类型|对方余额类型|TT-汇余额,CA-钞余额',
  `OTH_DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '对方凭证类型|对方凭证类型',
  `OTH_PREFIX` varchar(10) DEFAULT NULL COMMENT '对方票据前缀|对方票据前缀',
  `OTH_VOUCHER_NO` varchar(50) DEFAULT NULL COMMENT '对方票据号码|对方票据号码',
  `SIGN_TIME` varchar(26) DEFAULT NULL COMMENT '登记时间|登记时间',
  `PRINT_CNT` int DEFAULT NULL COMMENT '打印次数|银行承兑汇票登记簿凭证打印次数',
  `REVERSAL_FLAG` char(1) DEFAULT NULL COMMENT '交易是否已冲正|交易是否已冲正|Y-是,N-否',
  `BATCH_OPEN_STATUS` char(1) DEFAULT NULL COMMENT '批量开立状态|批量开立状态|P-未处理,F-失败,S-成功',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `ACCT_EXEC` varchar(30) DEFAULT NULL COMMENT '客户经理|客户经理',
  `DEPOSIT_NATURE` varchar(10) DEFAULT NULL COMMENT '存款性质|存款性质|JJSB-基金社保,CZCK-财政性存款,QT-其他,',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`),
  KEY `RB_BATCH_OPEN_DCT_DETAILS_INDEX1M` (`REFERENCE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量开立存单客户登记表|批量开立存单';

-- ----------------------------
-- Table structure for rb_batch_open_deposit_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_open_deposit_detail`;
CREATE TABLE `rb_batch_open_deposit_detail` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `ACCT_OPEN_DATE` datetime DEFAULT NULL COMMENT '账户开户日期|账户开户日期',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `ACCT_TYPE` char(1) DEFAULT NULL COMMENT '账户类型|账户类型|A-AIO账户,C-结算账户,D-垫款,E-委托贷款,L-转让贷款,M-普通贷款,S-储蓄账户,T-定期账户,U-贴现贷款,Y-银团贷款,Z-资产证券化',
  `MAIN_INTERNAL_KEY` bigint DEFAULT NULL COMMENT '主账户内部键值|主账户内部键值',
  `RESTRAINT_TYPE` varchar(3) DEFAULT NULL COMMENT '限制类型|限制类型',
  `RESTRAINT_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '冻结编号|冻结编号',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `TRAN_CCY` varchar(3) DEFAULT NULL COMMENT '交易币种|交易币种',
  `DR_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '借方产品类型|借方产品类型',
  `DR_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '借方账号|借方账号',
  `DR_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '借方账户序号|借方账户序号',
  `DR_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '借方账户币种|借方账户币种',
  `SUB_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账户组子账户账号|账户组子账户账号',
  `SUB_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '子产品类型|子产品类型',
  `SUB_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '子账户序号|子账户序号',
  `SUB_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '子账户中文名|子账户中文名',
  `BATCH_CLASS` varchar(10) DEFAULT NULL COMMENT '批次类型|批次类型|BATCH1-批量开立客户,BATCH2-批量开立账户,BATCH3-贷款批量开立/发放,BATCH4-批量司法查询,BATCH5-批量转账,BATCH6-批量开立内部账户（贷款贴现批量开立）,BATCH7-贷款批量核销,BATCH8-批量开立内部账户,BATCH9-批量开立存单,BATCH10-黑白名单批量导入,BATCH11-贷款批量发放核销,BATCH12-批量续开内部账,BATCH13-久悬户批量导入,BATCH14-批量冻结,BATCH15-批量解冻扣款,BATCH16-基金资金清算,BATCH17-社保卡批量开户开卡,BATCH18-CDCT对账,BATCH19-理财平台资金清算,BATCH20-日终签约文件同步,BATCH21-批量收取手续费,BATCH22-IC卡商户批量入账,BATCH23-委托关系验证,BATCH24-ic卡对账文件,BATCH25-IC卡挂失销户到期处理文件请求文件,BATCH26-生成指定表文件,BATCH27-批量冲正,BATCH39-批量开立一户通子账户',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `SUB_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户组子账户币种|账户组子账户币种',
  `TERM` varchar(5) DEFAULT NULL COMMENT '存期期限|期限',
  `TERM_TYPE` char(1) DEFAULT NULL COMMENT '期限类型|期限类型|Y-年,Q-季,M-月,W-周,D-日',
  `WITHDRAWAL_TYPE` char(1) DEFAULT NULL COMMENT '支取方式|支取方式|S-凭印鉴支取,P-凭密码支取,W-无密码无印鉴支取,B-凭印鉴和密码支取,O-凭证件支取,R-支付密码器和印鉴',
  `FEE_AMT` decimal(17,2) DEFAULT NULL COMMENT '费用金额|费用金额',
  `FEE_TYPE` varchar(20) DEFAULT NULL COMMENT '费用类型|费用类型',
  `FEE_CCY` varchar(3) DEFAULT NULL COMMENT '收费币种|收费币种',
  `AMORT_END` datetime DEFAULT NULL COMMENT '摊销截止日期|摊销截止日期',
  `AMORTIZE_PERIOD_FREQ` varchar(5) DEFAULT NULL COMMENT '摊销频率|摊销频率',
  `AMORTIZE_DAY` varchar(2) DEFAULT NULL COMMENT '摊销日|摊销日',
  `AMORTIZE_MONTH` varchar(2) DEFAULT NULL COMMENT '摊销月|摊销月',
  `AMORT_START` datetime DEFAULT NULL COMMENT '摊销起始日期|摊销起始日期',
  `FEE_CHARGE_METHOD` char(1) DEFAULT NULL COMMENT '手续费收取方式|手续费收取方式|0-即收即付手续费 ,1-预收手续费',
  `AMORTIZE_PERIOD_TYPE` char(1) DEFAULT NULL COMMENT '摊销期限类型|摊销期限类型|Y-年,Q-季,M-月,W-周,D-日',
  `RESERVE_FLAG` char(1) DEFAULT NULL COMMENT '冲正标志|冲正标志|Y-已冲正,N-未冲正',
  `SC_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '收费序号|收费序号',
  `REAL_RATE` decimal(15,8) DEFAULT NULL COMMENT '执行利率|执行利率',
  `MATURITY_DATE` datetime DEFAULT NULL COMMENT '到期日期|到期日期',
  `ACCT_SPREAD_RATE` decimal(15,8) DEFAULT NULL COMMENT '分户级利率浮动百分点|一般与客户协商后的利率上浮百分点，只在客户正常计提和支取的时候生效',
  `ACCT_PERCENT_RATE` decimal(5,2) DEFAULT NULL COMMENT '分户级利率浮动百分比|分户级利率浮动百分比',
  `NARRATIVE_CODE` varchar(30) DEFAULT NULL COMMENT '摘要码|摘要码',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='保证金批量开立详细信息表';

-- ----------------------------
-- Table structure for rb_batch_open_details
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_open_details`;
CREATE TABLE `rb_batch_open_details` (
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `SUB_SEQ_NO` varchar(100) DEFAULT NULL COMMENT '子流水号|子流水号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `CATEGORY_TYPE` varchar(3) DEFAULT NULL COMMENT '客户细分类型|客户细分类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `ISS_DATE` datetime DEFAULT NULL COMMENT '签发日期|签发日期',
  `ISS_COUNTRY` varchar(3) DEFAULT NULL COMMENT '发证国家|发证国家',
  `DOCUMENT_EXPIRY_DATE` datetime DEFAULT NULL COMMENT '证件失效日期|证件的到期日期',
  `BIRTHDAY` datetime DEFAULT NULL COMMENT '生日|出生日期',
  `SEX` char(1) DEFAULT NULL COMMENT '性别|性别|M-男,F-女',
  `RACE` varchar(10) DEFAULT NULL COMMENT '种族|种族',
  `CH_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户中文名称|客户中文名称',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `CONTACT_TYPE` varchar(20) DEFAULT NULL COMMENT '联系类型|联系类型',
  `OCCUPATION_CODE` varchar(10) DEFAULT NULL COMMENT '职业|职业',
  `EDUCATION` varchar(3) DEFAULT NULL COMMENT '教育程度编号|教育程度编号',
  `DIST_CODE` varchar(10) DEFAULT NULL COMMENT '地区代码|地区代码',
  `EMAIL` varchar(200) DEFAULT NULL COMMENT '电子邮件|电子邮件',
  `LOCAL_MESSAGE` varchar(200) DEFAULT NULL COMMENT '地址说明|地址说明',
  `LOCATION` varchar(200) DEFAULT NULL COMMENT '客户地址|客户地址',
  `MOBILE_NO` varchar(30) DEFAULT NULL COMMENT '电话号码|电话号码',
  `PHONE_NO` varchar(20) DEFAULT NULL COMMENT '固定电话|固定电话',
  `POSTAL_CODE` varchar(10) DEFAULT NULL COMMENT '邮政编码|邮政编码',
  `RESIDENT_FLAG` char(1) DEFAULT NULL COMMENT '居民标识|居民标识|Y-是 ,N-不是',
  `SOC_SEC_NO` varchar(50) DEFAULT NULL COMMENT '客户社保参保号码|客户社保参保号码',
  `GUARDIAN` varchar(200) DEFAULT NULL COMMENT '监护人名称|监护人名称',
  `GUARDIAN_PHONE` varchar(20) DEFAULT NULL COMMENT '监护人联系电话|监护人联系电话',
  `INLAND_OFFSHORE` char(1) DEFAULT NULL COMMENT '境内境外标志|境内境外标志|I-境内,O-境外',
  `SELF_STATEMENT` char(1) DEFAULT NULL COMMENT '取得自证声明标志|是否取得自证声明|Y-是 ,N-否',
  `COPR_NAME` varchar(200) DEFAULT NULL COMMENT '单位名称|单位名称',
  `COPR_SOC_SEC_NO` varchar(50) DEFAULT NULL COMMENT '单位社保参保号码|单位社保参保号码',
  `GUARDIAN_DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '监护人身份证号|监护人身份证号',
  `GUARDIAN_SHIP` varchar(20) DEFAULT NULL COMMENT '和监护人关系|和监护人关系',
  `OPEN_DATE` datetime DEFAULT NULL COMMENT '开立日期|开立日期',
  `OPEN_BRANCH` varchar(50) DEFAULT NULL COMMENT '开立机构|开立机构',
  `GAIN_TYPE` char(1) DEFAULT NULL COMMENT '卡片领取方式|卡片领取方式|O-自取,M-邮寄',
  `OPEN_CCY` varchar(3) DEFAULT NULL COMMENT '批量开户币种|批量开户币种',
  `FROM_CARD_NO` varchar(50) DEFAULT NULL COMMENT '转出卡号|转出卡号',
  `TO_CARD_NO` varchar(50) DEFAULT NULL COMMENT '终止卡号|终止卡号',
  `CARD_PB_IND` char(1) DEFAULT NULL COMMENT '卡/折标志|卡/折标志|C-卡,P-折',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CLASS` char(1) DEFAULT NULL COMMENT '账户类别|账户类别，用于区分账户类别（一二三类户），满足人行对于电子账户的管理办法|1-一类账户,2-二类账户,3-三类账户',
  `ACCT_NATURE` varchar(10) DEFAULT NULL COMMENT '账户属性|账户属性',
  `ALL_DEP_IND` char(1) DEFAULT NULL COMMENT '通存标志|通存标志|Y-是,N-否',
  `ALL_DRA_IND` char(1) DEFAULT NULL COMMENT '通兑标志|通兑标志|Y-是,N-否',
  `REASON_CODE` varchar(10) DEFAULT NULL COMMENT '账户用途|账户用途',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `PREFIX` varchar(10) DEFAULT NULL COMMENT '前缀|前缀',
  `VOUCHER_NO` varchar(50) DEFAULT NULL COMMENT '凭证号码|凭证号码',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `WITHDRAWAL_TYPE` char(1) DEFAULT NULL COMMENT '支取方式|支取方式|S-凭印鉴支取,P-凭密码支取,W-无密码无印鉴支取,B-凭印鉴和密码支取,O-凭证件支取',
  `REVERSAL_FLAG` char(1) DEFAULT NULL COMMENT '交易是否已冲正|交易是否已冲正|Y-是,N-否',
  `PRINT_CNT` int DEFAULT NULL COMMENT '打印次数|银行承兑汇票登记簿凭证打印次数',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `ACCT_EXEC` varchar(30) DEFAULT NULL COMMENT '客户经理|客户经理',
  `INT_IND_FLAG` char(1) DEFAULT NULL COMMENT '是否计息|是否计息|Y-是、正利率计息,N-否,F-是、负利率计息',
  `DEPOSIT_NATURE` varchar(10) DEFAULT NULL COMMENT '存款性质|存款性质|JJSB-基金社保,CZCK-财政性存款,QT-其他,',
  `IS_SELL_CHEQUE` char(1) DEFAULT NULL COMMENT '是否允许出售支票标识|是否允许出售支票标识|Y-是,N-否',
  `ALL_DRA_INT_BRANCH` varchar(200) DEFAULT NULL COMMENT '通兑机构|记录可以通兑的机构号',
  `BATCH_OPEN_TYPE` varchar(3) DEFAULT NULL COMMENT '批量开立类型|批量开立类型|BOI-个人开卡业务批量集中处理,BOC-单位批量开卡',
  `NARRATIVE_CODE` varchar(30) DEFAULT NULL COMMENT '摘要码|摘要码',
  `TRAN_NOTE` varchar(2000) DEFAULT NULL COMMENT '交易附言|交易附言',
  `CARD_NO` varchar(50) DEFAULT NULL COMMENT '卡号|卡号',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量开立信息明细登记簿|卡/折批量销户';

-- ----------------------------
-- Table structure for rb_batch_open_sin_card
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_open_sin_card`;
CREATE TABLE `rb_batch_open_sin_card` (
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SOC_SEC_NO` varchar(50) DEFAULT NULL COMMENT '客户社保参保号码|客户社保参保号码',
  `PERSONAL_NO` varchar(50) DEFAULT NULL COMMENT '个人编号|个人编号',
  `COPR_NO` varchar(50) DEFAULT NULL COMMENT '单位编号|单位编号',
  `COUNTRY_LOC` varchar(3) DEFAULT NULL COMMENT '国籍|国籍',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_EXPIRY_DATE` datetime DEFAULT NULL COMMENT '证件失效日期|证件的到期日期',
  `BIRTHDAY` datetime DEFAULT NULL COMMENT '生日|出生日期',
  `BIRTH_PLACE` varchar(500) DEFAULT NULL COMMENT '出生地|出生地',
  `SEX` char(1) DEFAULT NULL COMMENT '性别|性别|M-男,F-女',
  `NATION` varchar(50) DEFAULT NULL COMMENT '民族|民族',
  `PERSONNEL_STATUS` varchar(3) DEFAULT NULL COMMENT '人员状态|人员状态',
  `ACCOUNT_NATURE` varchar(10) DEFAULT NULL COMMENT '户口性质|社保卡开卡信息  户口性质',
  `EMAIL` varchar(200) DEFAULT NULL COMMENT '电子邮件|电子邮件',
  `MOBILE_NO` varchar(30) DEFAULT NULL COMMENT '电话号码|电话号码',
  `POSTAL_CODE` varchar(10) DEFAULT NULL COMMENT '邮政编码|邮政编码',
  `GUARDIAN` varchar(200) DEFAULT NULL COMMENT '监护人名称|监护人名称',
  `GUARDIAN_DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '监护人证件类型|监护人证件类型',
  `GUARDIAN_DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '监护人身份证号|监护人身份证号',
  `CONTACT_TEL` varchar(50) DEFAULT NULL COMMENT '联系电话  |联系电话',
  `COPR_NAME` varchar(200) DEFAULT NULL COMMENT '单位名称|单位名称',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `APPLY_TYPE` char(1) DEFAULT NULL COMMENT '预约申请类型|制卡申请类型|1-非记名卡批量制卡,2-记名卡批量制卡,3-记名卡单张制卡,4-补换卡申请',
  `CARD_NO` varchar(50) DEFAULT NULL COMMENT '卡号|卡号',
  `AGENCY` varchar(50) DEFAULT NULL COMMENT '经办机构|经办机构',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`,`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='社保卡开卡明细表|批量开立插入明细数据';

-- ----------------------------
-- Table structure for rb_batch_open_yht_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_open_yht_detail`;
CREATE TABLE `rb_batch_open_yht_detail` (
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `MAIN_RB_ACCT_NO` varchar(50) NOT NULL COMMENT '客户主账号|客户主账号',
  `MAIN_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '序列号|序列号',
  `MAIN_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '卡产品代码|卡产品代码',
  `MAIN_CCY` varchar(3) DEFAULT NULL COMMENT '主币种|主币种',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `ACCT_LEVEL` varchar(2) DEFAULT NULL COMMENT '当前账户层级|当前账户层级',
  `IS_END_LEVEL` char(1) DEFAULT NULL COMMENT '是否末级子账户|是否末级子账户|Y-是,N-否',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `OD_FACILITY` char(1) DEFAULT NULL COMMENT '是否可透支|是否可透支|Y-是,N-否',
  `BATCH_CLASS` varchar(10) NOT NULL COMMENT '批次类型|批次类型|BATCH1-批量开立客户,BATCH2-批量开立账户,BATCH3-贷款批量开立/发放,BATCH4-批量司法查询,BATCH5-批量转账,BATCH6-批量开立内部账户（贷款贴现批量开立）,BATCH7-贷款批量核销,BATCH8-批量开立内部账户,BATCH9-批量开立存单,BATCH10-黑白名单批量导入,BATCH11-贷款批量发放核销,BATCH12-批量续开内部账,BATCH13-久悬户批量导入,BATCH14-批量冻结,BATCH15-批量解冻扣款,BATCH16-基金资金清算,BATCH17-社保卡批量开户开卡,BATCH18-CDCT对账,BATCH19-理财平台资金清算,BATCH20-日终签约文件同步,BATCH21-批量收取手续费,BATCH22-IC卡商户批量入账,BATCH23-委托关系验证,BATCH24-ic卡对账文件,BATCH25-IC卡挂失销户到期处理文件请求文件,BATCH26-生成指定表文件,BATCH27-批量冲正',
  `BATCH_STATUS` char(1) NOT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量开立一户通子账户明细登记薄|批量开立明细登记薄';

-- ----------------------------
-- Table structure for rb_batch_overdraw_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_overdraw_detail`;
CREATE TABLE `rb_batch_overdraw_detail` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_NO` varchar(50) DEFAULT NULL COMMENT '批次号|批次号',
  `REFERENCE` varchar(50) NOT NULL COMMENT '交易参考号|交易参考号',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `LOAN_NO` varchar(50) NOT NULL COMMENT '贷款号|贷款号',
  `DD_NO` int NOT NULL COMMENT '发放号|贷款发放号，采用顺序数字，表示在同一贷款号、贷款账户类型、币种下的不同借据',
  `CMISLOAN_NO` varchar(50) DEFAULT NULL COMMENT '借据号|借据号',
  `SETTLE_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '结算账号|结算账号',
  `SETTLE_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '结算账户序号|结算账户序号',
  `SETTLE_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '结算账户产品类型|结算账户产品类型',
  `SETTLE_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '结算账户币种|结算账户币种',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `RET_CODE` varchar(50) DEFAULT NULL COMMENT '状态码|状态码',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='法透放款存款入账明细表|法透放款存款入账明细表，日终贷款已放款文件明细，存款进行入账操作';

-- ----------------------------
-- Table structure for rb_batch_reserve_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_reserve_detail`;
CREATE TABLE `rb_batch_reserve_detail` (
  `EXT_REF_NO` varchar(50) NOT NULL COMMENT '来单编号|来单编号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `SUB_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '子账户序号|子账户序号',
  `PAY_OUT_AMT` decimal(17,2) DEFAULT NULL COMMENT '扣划金额|扣划金额',
  `RES_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '限制编号|限制编号',
  `RESTRAINT_TYPE` varchar(3) DEFAULT NULL COMMENT '限制类型|限制类型',
  `PLEDGED_AMT` decimal(17,2) DEFAULT NULL COMMENT '限制金额|限制金额',
  `ACCT_TYPE` char(1) DEFAULT NULL COMMENT '账户类型|账户类型|A-AIO账户,C-结算账户,D-垫款,E-委托贷款,L-转让贷款,M-普通贷款,S-储蓄账户,T-定期账户,U-贴现贷款,Y-银团贷款,Z-资产证券化',
  `AVAILABLE_AMT` decimal(17,2) DEFAULT NULL COMMENT '可用余额|可用余额',
  `ACTUAL_PAY_OUT_AMT` decimal(17,2) DEFAULT NULL COMMENT '实际扣划金额|实际扣划金额',
  `PAY_OUT_STATUS` varchar(3) DEFAULT NULL COMMENT '扣款状态|扣款状态',
  `CLOSE_ACCT_FLAG` char(1) DEFAULT NULL COMMENT '是否可销户|是否可销户|Y-是,N-否',
  PRIMARY KEY (`EXT_REF_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='备款明细信息表|登记自动备款明细信息';

-- ----------------------------
-- Table structure for rb_batch_reserve_info
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_reserve_info`;
CREATE TABLE `rb_batch_reserve_info` (
  `EXT_REF_NO` varchar(50) NOT NULL COMMENT '来单编号|来单编号',
  `REGISTER_DATE` datetime DEFAULT NULL COMMENT '登记日期|登记日期',
  `EXT_TRADE_NO` varchar(50) DEFAULT NULL COMMENT '原业务编号|原业务编号,如承兑汇票号码,信用证编号等',
  `RESERVE_AMT` decimal(17,2) DEFAULT NULL COMMENT '备款金额|备款金额',
  `RESERVE_DATE` datetime DEFAULT NULL COMMENT '备款日期|备款日期',
  `TRADE_TYPE` varchar(30) DEFAULT NULL COMMENT '业务类型|业务类型',
  `RESERVE_STATUS` varchar(3) DEFAULT NULL COMMENT '备款状态|备款状态|0-未备款,1-已备款,2-备款中,F-备款失败',
  `FROM_CHANNEL` varchar(30) DEFAULT NULL COMMENT '记录来源|记录来源',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `CLOSE_ACCT_FLAG` char(1) DEFAULT NULL COMMENT '是否可销户|是否可销户|Y-是,N-否',
  `ADVANCED_REAL_RATE` decimal(15,8) DEFAULT NULL COMMENT '垫款执行利率|垫款的执行利率',
  `ADVANCED_SCHED_MODE` varchar(2) DEFAULT NULL COMMENT '垫款还款方式|垫款的还款方式|1-等额本息,2-等额本金,3-一次性还本付息/前收息,4-按频率付息一次还本,5-按频率付息任意本金,6-气球贷,7-等额累进,8-等比累进,9-等本等息,10-组合贷,11-按比例还本',
  `ADVANCED_CYCLE_FREQ` varchar(5) DEFAULT NULL COMMENT '垫款结息频率|垫款结息频率',
  `ADVANCED_NEXT_CYCLE_DATE` datetime DEFAULT NULL COMMENT '垫款下一结息日|垫款的下一结息日',
  `ADVANCED_BRANCH` varchar(50) DEFAULT NULL COMMENT '垫款发放机构|发放垫款的机构',
  `ADVANCED_CMISLOAN_NO` varchar(50) DEFAULT NULL COMMENT '垫款借据号|垫款借据号',
  `ADVANCED_INT_DAY` varchar(2) DEFAULT NULL COMMENT '垫款结息日|垫款结息日',
  `ADVANCED_AMT` decimal(17,2) DEFAULT NULL COMMENT '垫款金额 |垫款金额 ',
  `CORP_SIZE` varchar(5) DEFAULT NULL COMMENT '企业规模 |企业规模 |9-其他,CS01-大型企业,CS02-中型企业,CS03-小型企业,CS04-微型企业',
  `DEAL_DATE` datetime DEFAULT NULL COMMENT '处理日期|处理日期',
  `ECON_DEPARTMENT_TYPE` varchar(200) DEFAULT NULL COMMENT '国民经济部门类型|国民经济部门类型',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `NEW_SETTLE_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '新利息入账账号|新利息入账账号',
  `RESERVE_ACCT_BRANCH` varchar(20) DEFAULT NULL COMMENT '备款入账机构|备款入账机构',
  PRIMARY KEY (`EXT_REF_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='备款基础信息表|登记自动备款基础信息';

-- ----------------------------
-- Table structure for rb_batch_reserve_loan
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_reserve_loan`;
CREATE TABLE `rb_batch_reserve_loan` (
  `CONTRACT_NO` varchar(50) NOT NULL COMMENT '合同号|合同号',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|所属机构号',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `ORIG_LOAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '合同金额|合同金额',
  `LOAN_NO` varchar(50) DEFAULT NULL COMMENT '贷款号|贷款号',
  `CMISLOAN_NO` varchar(50) DEFAULT NULL COMMENT '借据号|借据号',
  `SUCCESS_FLAG` char(1) DEFAULT NULL COMMENT '成功标志|成功标志|Y-是,N-否',
  `REGISTER_DATE` datetime DEFAULT NULL COMMENT '登记日期|登记日期',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `BILL_VOUCHER_NO` varchar(50) NOT NULL COMMENT '票据凭证号码|票据凭证号码',
  `RESERVE_STATUS` varchar(3) DEFAULT NULL COMMENT '备款状态|备款状态|0-未备款,1-已备款,F-备款失败',
  `ERROR_MESSAGE` varchar(2000) DEFAULT NULL COMMENT '错误信息|发布目标系统的错误信息',
  PRIMARY KEY (`CONTRACT_NO`,`BILL_VOUCHER_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='银行承兑汇票批量贷款发放登记表|银行承兑汇票批量贷款发放登记表';

-- ----------------------------
-- Table structure for rb_batch_settle_card_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_settle_card_detail`;
CREATE TABLE `rb_batch_settle_card_detail` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `ISS_COUNTRY` varchar(3) DEFAULT NULL COMMENT '发证国家|发证国家',
  `DOCUMENT_EXPIRY_DATE` datetime DEFAULT NULL COMMENT '证件失效日期|证件的到期日期',
  `BIRTHDAY` datetime DEFAULT NULL COMMENT '生日|出生日期',
  `CH_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户中文名称|客户中文名称',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `SEX` char(1) DEFAULT NULL COMMENT '性别|性别|M-男,F-女',
  `MOBILE_NO` varchar(30) DEFAULT NULL COMMENT '电话号码|电话号码',
  `CONTACT_TEL` varchar(50) DEFAULT NULL COMMENT '联系电话  |联系电话',
  `LOCATION` varchar(200) DEFAULT NULL COMMENT '客户地址|客户地址',
  `CARD_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '卡产品类型|卡产品类型',
  `CARD_NO` varchar(50) DEFAULT NULL COMMENT '卡号|卡号',
  `MAIN_CARD_FLAG` char(1) DEFAULT NULL COMMENT '主卡标识|主卡标识|Y-是,N-否',
  `MAIN_CARD_NO` varchar(50) DEFAULT NULL COMMENT '主卡卡号|主卡卡号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `PREFIX` varchar(10) DEFAULT NULL COMMENT '前缀|前缀',
  `VOUCHER_NO` varchar(50) DEFAULT NULL COMMENT '凭证号码|凭证号码',
  `OPERATOR_NAME` varchar(200) DEFAULT NULL COMMENT '经办人姓名|经办人姓名',
  `OFF_DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '经办人证件类型|经办人证件类型',
  `OFF_DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '经办人证件号码|经办人证件号码',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`,`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量开卡明细';

-- ----------------------------
-- Table structure for rb_batch_sl_sign_close_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_sl_sign_close_detail`;
CREATE TABLE `rb_batch_sl_sign_close_detail` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `EXE_ID` varchar(50) DEFAULT NULL COMMENT '执行ID|执行ID',
  `UNSIGN_OPERATE_TYPE` varchar(2) DEFAULT NULL COMMENT '解约操作类型|解约操作类型|03-解约   ,04-终止   ,05-解除终止',
  `LOAN_NO` varchar(50) NOT NULL COMMENT '贷款号|贷款号',
  `RUN_DATE` datetime NOT NULL COMMENT '运行日期|交易日期',
  `BATCH_FILE_STATUS` char(1) DEFAULT NULL COMMENT '批处理文件处理状态|批处理文件处理状态|N-新建,S-成功,F-失败',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='特殊贷款产品批处理自动解约和终止登记表|金额补足解约处理(特殊贷款产品-卡易贷、卡贷通、英才贷)';

-- ----------------------------
-- Table structure for rb_batch_sl_sign_close_detail_hist
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_sl_sign_close_detail_hist`;
CREATE TABLE `rb_batch_sl_sign_close_detail_hist` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `EXE_ID` varchar(50) NOT NULL COMMENT '执行ID|执行ID',
  `UNSIGN_OPERATE_TYPE` varchar(2) DEFAULT NULL COMMENT '解约操作类型|解约操作类型|03-解约   ,04-终止   ,05-解除终止',
  `LOAN_NO` varchar(50) NOT NULL COMMENT '贷款号|贷款号',
  `RUN_DATE` datetime NOT NULL COMMENT '运行日期|交易日期',
  `BATCH_FILE_STATUS` char(1) DEFAULT NULL COMMENT '批处理文件处理状态|批处理文件处理状态|N-新建,S-成功,F-失败',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='特殊贷款产品批处理自动解约和终止登记表';

-- ----------------------------
-- Table structure for rb_batch_supplement_dismiss
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_supplement_dismiss`;
CREATE TABLE `rb_batch_supplement_dismiss` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `RUN_DATE` datetime NOT NULL COMMENT '运行日期|交易日期',
  `UNSIGN_OPERATE_TYPE` varchar(2) DEFAULT NULL COMMENT '解约操作类型|解约操作类型|03-解约   ,04-终止   ,05-解除终止',
  `UNSIGN_OPERATE_DATE` datetime DEFAULT NULL COMMENT '解约操作日期|解约操作日期',
  `SL_CONTRACT_NO` varchar(50) NOT NULL COMMENT '贷款合同号|贷款合同号',
  `BATCH_FILE_STATUS` char(1) DEFAULT NULL COMMENT '批处理文件处理状态|批处理文件处理状态|N-新建,S-成功,F-失败',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='金额补足解约表|金额补足解约处理(特殊贷款产品-卡易贷、卡贷通、英才贷)';

-- ----------------------------
-- Table structure for rb_batch_tran
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_tran`;
CREATE TABLE `rb_batch_tran` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) DEFAULT NULL COMMENT '序号|序号',
  `BATCH_CLASS` varchar(10) NOT NULL COMMENT '批次类型|批次类型|BATCH1-批量开立客户,BATCH2-批量开立账户,BATCH3-贷款批量开立/发放,BATCH4-批量司法查询,BATCH5-批量转账,BATCH6-批量开立内部账户（贷款贴现批量开立）,BATCH7-贷款批量核销,BATCH8-批量开立内部账户,BATCH9-批量开立存单,BATCH10-黑白名单批量导入,BATCH11-贷款批量发放核销,BATCH12-批量续开内部账,BATCH13-久悬户批量导入,BATCH14-批量冻结,BATCH15-批量解冻扣款,BATCH16-基金资金清算,BATCH17-社保卡批量开户开卡,BATCH18-CDCT对账,BATCH19-理财平台资金清算,BATCH20-日终签约文件同步,BATCH21-批量收取手续费,BATCH22-IC卡商户批量入账,BATCH23-委托关系验证,BATCH24-ic卡对账文件,BATCH25-IC卡挂失销户到期处理文件请求文件,BATCH26-生成指定表文件,BATCH27-批量冲正',
  `BATCH_DESC` varchar(50) DEFAULT NULL COMMENT '批处理描述|批处理描述',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|渠道类型',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `DEAL_DATE` datetime DEFAULT NULL COMMENT '处理日期|处理日期',
  `RUN_DATE` datetime DEFAULT NULL COMMENT '运行日期|交易日期',
  `FILE_NAME` varchar(200) DEFAULT NULL COMMENT '文件名称|文件名称',
  `FILE_PATH` varchar(200) DEFAULT NULL COMMENT '文件路径|文件路径',
  `TOTAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '总金额|总金额',
  `TOTAL_NUM` int DEFAULT NULL COMMENT '总数量|总数量',
  `SUCC_NUM` int DEFAULT NULL COMMENT '成功数量|成功数量',
  `FAILURE_NUMBER` int DEFAULT NULL COMMENT '失败数量|失败数量',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `THREAD_NO` varchar(50) DEFAULT NULL COMMENT '线程编号|线程编号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `MAC_VALUE` varchar(200) DEFAULT NULL COMMENT '传输密押|传输密押',
  `APPR_FLAG` char(1) DEFAULT NULL COMMENT '复核标志|复核标志|Y-已复核,N-未复核',
  `AUTH_FLAG` char(1) DEFAULT NULL COMMENT '授权标志|授权标志|Y-已授权,N-未授权',
  `TRAN_MODE` char(1) DEFAULT NULL COMMENT '交易模式|交易模式|W-一借多贷 ,M-一贷多借',
  `SYSTEM_ID` varchar(20) DEFAULT NULL COMMENT '系统ID|系统ID',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `DEST_BRANCH_NO` varchar(50) DEFAULT NULL COMMENT '目标机构编号|目标机构编号',
  `SOURCE_BRANCH_NO` varchar(50) DEFAULT NULL COMMENT '源节点编号|源节点编号',
  `PROGRAM_ID` varchar(20) DEFAULT NULL COMMENT '交易代码|交易代码',
  `APPR_USER_ID` varchar(30) DEFAULT NULL COMMENT '复核柜员|复核柜员',
  `AUTH_USER_ID` varchar(30) DEFAULT NULL COMMENT '授权柜员|授权柜员',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `USER_LANG` varchar(30) DEFAULT NULL COMMENT '柜员语言|柜员语言|E-英文,C-中文',
  `BEGIN_TIME` varchar(26) DEFAULT NULL COMMENT '开始时间|开始时间',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `APPROVAL_NO` varchar(50) DEFAULT NULL COMMENT '审批单号|审批单号',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量交易信息登记簿|存款批量文件处理';

-- ----------------------------
-- Table structure for rb_batch_tran_attach
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_tran_attach`;
CREATE TABLE `rb_batch_tran_attach` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `ATTR_KEY` varchar(30) NOT NULL COMMENT '参数KEY值|参数KEY值',
  `ATTR_VALUE` varchar(500) DEFAULT NULL COMMENT '属性值|属性值',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`,`ATTR_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量交易子表|批量转账';

-- ----------------------------
-- Table structure for rb_batch_tran_comm
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_tran_comm`;
CREATE TABLE `rb_batch_tran_comm` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `NEW_BRANCH` varchar(50) DEFAULT NULL COMMENT '变更后机构|变更后机构',
  `OLD_BRANCH` varchar(50) DEFAULT NULL COMMENT '变更前机构|变更前机构',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `BAL_UPD_TYPE` char(1) DEFAULT NULL COMMENT '余额更新类型|余额更新类型|R-实时更新,B-批量更新',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='公共批量登记簿';

-- ----------------------------
-- Table structure for rb_batch_tran_details
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_tran_details`;
CREATE TABLE `rb_batch_tran_details` (
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|渠道类型',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `ACCT_DESC` varchar(200) DEFAULT NULL COMMENT '账户描述|账户描述,目前同账户名称',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `PREFIX` varchar(10) DEFAULT NULL COMMENT '前缀|前缀',
  `VOUCHER_NO` varchar(50) DEFAULT NULL COMMENT '凭证号码|凭证号码',
  `SETTLEMENT_DATE` datetime DEFAULT NULL COMMENT '清算日期|清算日期',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `CHANNEL` varchar(10) DEFAULT NULL COMMENT '渠道|渠道细类|JZ-集中作业,MT-柜面,ZD-智能网点自助交易平台,VT-远程智能自助银行,QT-XBankT10,QE-快窗智能高柜,CP-手机银行,EB-个人网上银行,CB-企业网上银行,CM-现金管理,WB-微信银行',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `SERV_CHARGE` char(1) DEFAULT NULL COMMENT '服务费标识|服务费标识|Y-是, N-否',
  `TRAN_DESC` varchar(200) DEFAULT NULL COMMENT '交易描述|交易描述',
  `TRAN_NOTE` varchar(2000) DEFAULT NULL COMMENT '交易附言|交易附言',
  `GL_CODE` varchar(20) DEFAULT NULL COMMENT '科目代码|科目代码',
  `FH_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '资金冻结流水号|记录冻结编号信息',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `OTH_TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '对方交易类型|对方交易类型',
  `OTH_REFERENCE` varchar(50) DEFAULT NULL COMMENT '对方交易参考号|对方交易参考号',
  `OTH_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '对方交易流水号|对方交易流水号',
  `OTH_GL_CODE` varchar(20) DEFAULT NULL COMMENT '对方科目代码|对方科目代码',
  `OTH_TRAN_NAME` varchar(200) DEFAULT NULL COMMENT '交易对手名称|交易对手名称',
  `OTH_BRANCH` varchar(50) DEFAULT NULL COMMENT '对方账户开户机构|对方账户开户机构',
  `OTH_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '对方账号/卡号|对方账号/卡号',
  `OTH_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '对方账户产品类型|对方账户产品类型',
  `OTH_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '对方账户币种|对方账户币种',
  `OTH_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '对方账户序列号|对方账户序列号',
  `OTH_ACCT_DESC` varchar(200) DEFAULT NULL COMMENT '对方账户描述|对方账户描述',
  `OTH_BANK_CODE` varchar(20) DEFAULT NULL COMMENT '对方银行代码|对方银行代码',
  `OTH_BANK_NAME` varchar(50) DEFAULT NULL COMMENT '对方银行名称|对方银行名称',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `REMARK2` varchar(200) DEFAULT NULL COMMENT '备注2|备注2',
  `REMARK3` varchar(200) DEFAULT NULL COMMENT '备注3|备注3',
  `REMARK4` varchar(200) DEFAULT NULL COMMENT '备注5|备注',
  `REMARK5` varchar(200) DEFAULT NULL COMMENT '备注6|备注',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `NARRATIVE_CODE` varchar(30) DEFAULT NULL COMMENT '摘要码|摘要码',
  `OTH_REAL_BANK_NAME` varchar(50) DEFAULT NULL COMMENT '真实对方金融机构名称|真实对方金融机构名称',
  `OTH_REAL_BANK_CODE` varchar(20) DEFAULT NULL COMMENT '真实对方金融机构代码|真实对方金融机构代码',
  `OTH_REAL_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '真实交易对手账号|真实交易对手账号',
  `OTH_REAL_DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '真实交易对手身份证件/证明文件号码|真实交易对手身份证件/证明文件号码',
  `OTH_REAL_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '真实交易对手账户类型|真实交易对手账户类型',
  `OTH_REAL_DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '真实交易对手身份证件/证明文件类型|真实交易对手身份证件/证明文件类型',
  `OTH_REAL_TRAN_ADDR` varchar(500) DEFAULT NULL COMMENT '真实交易发生地|真实交易发生地',
  `OTH_REAL_TRAN_NAME` varchar(200) DEFAULT NULL COMMENT '真实交易对手名称|真实交易对手名称',
  PRIMARY KEY (`SEQ_NO`,`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量转账信息登记薄|批量转账明细';

-- ----------------------------
-- Table structure for rb_batch_tran_ic_details
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_tran_ic_details`;
CREATE TABLE `rb_batch_tran_ic_details` (
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_NO` varchar(50) DEFAULT NULL COMMENT '批次号|批次号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `CARD_NO` varchar(50) DEFAULT NULL COMMENT '卡号|卡号',
  `CHANNEL_DATE` datetime DEFAULT NULL COMMENT '渠道日期|渠道日期',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='IC卡商户批量入账|IC卡商户批量入账时候';

-- ----------------------------
-- Table structure for rb_batch_transfer_details
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_transfer_details`;
CREATE TABLE `rb_batch_transfer_details` (
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `SUB_SEQ_NO` varchar(100) DEFAULT NULL COMMENT '子流水号|子流水号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `BUSI_TYPE` varchar(20) DEFAULT NULL COMMENT '业务种类|业务种类',
  `OUT_INTERNAL_KEY` bigint DEFAULT NULL COMMENT '转出方internal_Key|转出方internal_Key',
  `OUT_CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '转出方客户号|转出方客户号',
  `OUT_ACCT` varchar(50) DEFAULT NULL COMMENT '账号|账号',
  `OUT_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '转出账户名称|转出账户名称',
  `OUT_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '转出账户产品|转出账户产品',
  `OUT_CCY` varchar(3) DEFAULT NULL COMMENT '转出账户币种|转出账户币种',
  `OUT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '转出账户序号|转出账户序号',
  `WITHDRAWAL_TYPE` char(1) DEFAULT NULL COMMENT '支取方式|支取方式|S-凭印鉴支取,P-凭密码支取,W-无密码无印鉴支取,B-凭印鉴和密码支取,O-凭证件支取',
  `OUT_PURPOSE` varchar(200) DEFAULT NULL COMMENT '转出账户用途|转出账户用途',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `PREFIX` varchar(10) DEFAULT NULL COMMENT '前缀|前缀',
  `VOUCHER_NO` varchar(50) DEFAULT NULL COMMENT '凭证号码|凭证号码',
  `CHEQUE_DATE` datetime DEFAULT NULL COMMENT '支票凭证出票日期|支票凭证出票日期',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `ACT_TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '实际交易金额|实际交易金额',
  `REC_AMT_CTRL` char(1) DEFAULT NULL COMMENT '还款金额控制标志|扣款方式|A-足额扣除,P-部分扣款',
  `IN_INTERNAL_KEY` bigint DEFAULT NULL COMMENT '转入方internal_Key|转入方internal_Key',
  `IN_CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '转入方客户号|转入方客户号',
  `IN_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '移入账号|子账户移入移出时，移入方账号',
  `IN_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '转入账户产品类型|转入账户产品类型',
  `IN_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '转入账户序号|转入账户序号',
  `IN_CCY` varchar(3) DEFAULT NULL COMMENT '转入账户币种|转入账户币种',
  `IN_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '转入账户名称|转入账户名称',
  `IN_PURPOSE` varchar(50) DEFAULT NULL COMMENT '转入账户用途|转入账户用途',
  `REVERSAL_FLAG` char(1) DEFAULT NULL COMMENT '交易是否已冲正|交易是否已冲正|Y-是,N-否',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `TRANSFER_STATUS` char(1) DEFAULT NULL COMMENT '转账状态|转账状态|A-有效,E-失效,F-无效',
  `SIGN_TIME` varchar(26) DEFAULT NULL COMMENT '登记时间|登记时间',
  `CONTRAST_BAT_NO` varchar(50) DEFAULT NULL COMMENT '他行批次号|对方批次号',
  `TRAN_NOTE` varchar(2000) DEFAULT NULL COMMENT '交易附言|交易附言',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `NARRATIVE_CODE` varchar(30) DEFAULT NULL COMMENT '摘要码|摘要码',
  `OTH_REAL_BANK_CODE` varchar(20) DEFAULT NULL COMMENT '真实对方金融机构代码|真实对方金融机构代码',
  `OTH_REAL_BANK_NAME` varchar(50) DEFAULT NULL COMMENT '真实对方金融机构名称|真实对方金融机构名称',
  `OTH_REAL_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '真实交易对手账号|真实交易对手账号',
  `OTH_REAL_DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '真实交易对手身份证件/证明文件号码|真实交易对手身份证件/证明文件号码',
  `OTH_REAL_DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '真实交易对手身份证件/证明文件类型|真实交易对手身份证件/证明文件类型',
  `OTH_REAL_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '真实交易对手账户类型|真实交易对手账户类型',
  `OTH_REAL_TRAN_ADDR` varchar(500) DEFAULT NULL COMMENT '真实交易发生地|真实交易发生地',
  `OTH_REAL_TRAN_NAME` varchar(200) DEFAULT NULL COMMENT '真实交易对手名称|真实交易对手名称',
  PRIMARY KEY (`SEQ_NO`,`BATCH_NO`),
  KEY `IDX_RB_BATCH_TRANSFER_DTL_3M` (`JOB_RUN_ID`),
  KEY `IDX_RB_BATCH_TRANSFER_DTL_2M` (`CHANNEL_SEQ_NO`),
  KEY `IDX_RB_BATCH_TRANSFER_DTL_1M` (`REFERENCE`),
  KEY `RB_BATCH_TRANSFER_DETAILS_INDEX4M` (`CONTRAST_BAT_NO`,`REVERSAL_FLAG`),
  KEY `RB_BATCH_TRANSFER_DETAILS_INDEX5M` (`CONTRAST_BAT_NO`,`TRANSFER_STATUS`,`TRAN_AMT`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量转账明细登记薄|批量转账时候';

-- ----------------------------
-- Table structure for rb_batch_voucher_temp
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_voucher_temp`;
CREATE TABLE `rb_batch_voucher_temp` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `MIN_NO` varchar(50) DEFAULT NULL COMMENT '最小凭证号|最小凭证号',
  `VOUCHER_START_NO` varchar(50) DEFAULT NULL COMMENT '凭证起始号码|凭证起始号码',
  `VOUCHER_END_NO` varchar(50) DEFAULT NULL COMMENT '凭证终止号码|凭证终止号码',
  `VOUCHER_NUM` int DEFAULT NULL COMMENT '凭证数量|凭证数量',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量开卡批次凭证锁定信息|批量开卡时按照批次数量进行锁定凭证信息登记';

-- ----------------------------
-- Table structure for rb_batch_with_hold_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_with_hold_detail`;
CREATE TABLE `rb_batch_with_hold_detail` (
  `BATCH_SEQ_NO` varchar(50) NOT NULL COMMENT '批次明细序号|批次明细序号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `ISSUE_NO` varchar(50) DEFAULT NULL COMMENT '发布编号|发布编号',
  `LOAN_INTERNAL_KEY` bigint DEFAULT NULL COMMENT '贷款账户KEY值|贷款账户KEY值',
  `LOAN_NO` varchar(50) DEFAULT NULL COMMENT '贷款号|贷款号',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `ACTUAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '实际金额|实际金额',
  `TOTAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '总金额|总金额',
  `SETTLE_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '结算账号|结算账号',
  `SETTLE_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '结算账户产品类型|结算账户产品类型',
  `SETTLE_CCY` varchar(3) DEFAULT NULL COMMENT '结算币种|结算币种',
  `SETTLE_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '清算账户序号|清算账户序号',
  `PERIODS` varchar(5) DEFAULT NULL COMMENT '批量扣扣频率|批量扣扣频率',
  `WEIGHT` varchar(20) DEFAULT NULL COMMENT '权重|权重',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量明细表|存贷联合，贷款自动回收';

-- ----------------------------
-- Table structure for rb_batch_with_hold_detail_hist
-- ----------------------------
DROP TABLE IF EXISTS `rb_batch_with_hold_detail_hist`;
CREATE TABLE `rb_batch_with_hold_detail_hist` (
  `BATCH_SEQ_NO` varchar(50) NOT NULL COMMENT '批次明细序号|批次明细序号',
  `REFERENCE` varchar(50) NOT NULL COMMENT '交易参考号|交易参考号',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `LOAN_NO` varchar(50) DEFAULT NULL COMMENT '贷款号|贷款号',
  `LOAN_INTERNAL_KEY` bigint DEFAULT NULL COMMENT '贷款账户KEY值|贷款账户KEY值',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `ISSUE_NO` varchar(50) DEFAULT NULL COMMENT '发布编号|发布编号',
  `ACTUAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '实际金额|实际金额',
  `TOTAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '总金额|总金额',
  `SETTLE_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '结算账号|结算账号',
  `SETTLE_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '结算账户产品类型|结算账户产品类型',
  `SETTLE_CCY` varchar(3) DEFAULT NULL COMMENT '结算币种|结算币种',
  `SETTLE_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '清算账户序号|清算账户序号',
  `PERIODS` varchar(5) DEFAULT NULL COMMENT '批量扣扣频率|批量扣扣频率',
  `WEIGHT` varchar(20) DEFAULT NULL COMMENT '权重|权重',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_SEQ_NO`,`REFERENCE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='贷款批量扣款信息历史表|存贷联合，贷款自动回收';

-- ----------------------------
-- Table structure for rb_bill_tran_def
-- ----------------------------
DROP TABLE IF EXISTS `rb_bill_tran_def`;
CREATE TABLE `rb_bill_tran_def` (
  `OPERATE_TYPE` varchar(2) DEFAULT NULL COMMENT '变更操作方式|变更操作方式|00-录入01-复核,02-兑付,03-退回,04-挂失,05-解挂,06-修改,07-删除',
  `TRAN_TYPE` varchar(10) NOT NULL COMMENT '交易类型|交易类型',
  `REVERSAL_TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '冲正交易类型|冲正交易类型',
  `CASH_FROM_TO` char(1) DEFAULT NULL COMMENT '资金去向来源|资金去向来源，主要用于现金长短款|1-现金,2-转账,3-挂销账',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `BILL_TYPE` varchar(5) DEFAULT NULL COMMENT '票据类型|凭证类型为PN-本票时： 1-现金本票 2-可转让转账本票 3-不可转让转账本票 凭证类型为BE-汇票时 1-现金汇票 2-转账汇票|1-现金本票/现金汇票,2-可转让转账本票/转账汇票,3-不可转让转账本票',
  `OTH_TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '对方交易类型|对方交易类型',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `MEANING` varchar(200) DEFAULT NULL COMMENT '说明|说明',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`TRAN_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='票据交易类型定义表';

-- ----------------------------
-- Table structure for rb_black_name
-- ----------------------------
DROP TABLE IF EXISTS `rb_black_name`;
CREATE TABLE `rb_black_name` (
  `BLACK_NO` varchar(50) DEFAULT NULL COMMENT '黑名单编号|黑名单编号',
  `BLACK_SEQ` varchar(50) NOT NULL COMMENT '黑名单序号|黑名单序号',
  `LIST_SOURCE` char(1) DEFAULT NULL COMMENT '名单来源|名单来源|H-核心,C-信用卡,Z-直销银行',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `CREATE_DATE` datetime DEFAULT NULL COMMENT '创建日期|创建日期',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `OUR_BANK_FLAG` char(1) DEFAULT NULL COMMENT '是否我行黑名单|是否我行黑名单|Y-是,N-否',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `REMARK2` varchar(200) DEFAULT NULL COMMENT '备注2|备注2',
  `REMARK3` varchar(200) DEFAULT NULL COMMENT '备注3|备注3',
  `UNCOUNTER_DESC` varchar(50) DEFAULT NULL COMMENT '入表原因|入表原因',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BLACK_SEQ`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='核心黑名单表|黑名单批量导入';

-- ----------------------------
-- Table structure for rb_branch_rev_file
-- ----------------------------
DROP TABLE IF EXISTS `rb_branch_rev_file`;
CREATE TABLE `rb_branch_rev_file` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `OLD_BRANCH` varchar(50) DEFAULT NULL COMMENT '变更前机构|变更前机构',
  `NEW_BRANCH` varchar(50) DEFAULT NULL COMMENT '变更后机构|变更后机构',
  `TRAN_STATUS` char(1) DEFAULT NULL COMMENT '业务处理状态|业务处理状态|N-正常,X-被冲正,R-冲正',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `LAST_CHANGE_DATE` datetime DEFAULT NULL COMMENT '最后修改日期|最后修改日期',
  `SOURCE_MODULE` varchar(3) DEFAULT NULL COMMENT '源模块|源模块|RB-存款,CL-贷款,GL-总账,ALL-所有',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='存款批量账户机构变更中间表|存款批量账户机构变更中间表';

-- ----------------------------
-- Table structure for rb_branch_rev_meg_register
-- ----------------------------
DROP TABLE IF EXISTS `rb_branch_rev_meg_register`;
CREATE TABLE `rb_branch_rev_meg_register` (
  `APPLY_NO` varchar(50) NOT NULL COMMENT '申请编号|申请编号',
  `REV_MEG_TYPE` char(1) DEFAULT NULL COMMENT '机构拆并类型|机构拆并类型|A-全部转移,P-部分账户',
  `APPLY_DATE` datetime DEFAULT NULL COMMENT '申请日期|申请日期',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `FILE_NAME` varchar(200) DEFAULT NULL COMMENT '文件名称|文件名称',
  `FILE_PATH` varchar(200) DEFAULT NULL COMMENT '文件路径|文件路径',
  `UPDATE_DATE` datetime DEFAULT NULL COMMENT '更新日期|更新日期',
  `IN_BRANCH` varchar(50) DEFAULT NULL COMMENT '拆入机构|拆入机构',
  `OUT_BRANCH` varchar(50) DEFAULT NULL COMMENT '出库机构|出库机构',
  `MERGE_APPLY_STATUS` char(1) DEFAULT NULL COMMENT '机构撤并申请状态|机构撤并申请状态|A-有效,E-失效,A-有效,S-成功,F-失败',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`APPLY_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构撤并申请登记表|全机构撤并';

-- ----------------------------
-- Table structure for rb_cash_ctl_sign
-- ----------------------------
DROP TABLE IF EXISTS `rb_cash_ctl_sign`;
CREATE TABLE `rb_cash_ctl_sign` (
  `BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `SUB_BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '账户组子账户账号|账户组子账户账号',
  `CHANNEL_SEQ_NO` varchar(50) NOT NULL COMMENT '渠道流水号|渠道流水号',
  `SUB_SEQ_NO` varchar(100) NOT NULL COMMENT '子流水号|子流水号',
  `STATUS` char(1) NOT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账,N-新增,U-修改,D-删除,C-非活动状态',
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BASE_ACCT_NO`,`SUB_BASE_ACCT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='现金管理签约登记表|记录现金管理签约主子账户关联关系';

-- ----------------------------
-- Table structure for rb_cl_batch_check_info
-- ----------------------------
DROP TABLE IF EXISTS `rb_cl_batch_check_info`;
CREATE TABLE `rb_cl_batch_check_info` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_NO` varchar(50) DEFAULT NULL COMMENT '批次号|批次号',
  `BATCH_CLASS` varchar(10) DEFAULT NULL COMMENT '批次类型|批次类型|BATCH1-批量开立客户,BATCH2-批量开立账户,BATCH3-贷款批量开立/发放,BATCH4-批量司法查询,BATCH5-批量转账,BATCH6-批量开立内部账户（贷款贴现批量开立）,BATCH7-贷款批量核销,BATCH8-批量开立内部账户,BATCH9-批量开立存单,BATCH10-黑白名单批量导入,BATCH11-贷款批量发放核销,BATCH12-批量续开内部账,BATCH13-久悬户批量导入,BATCH14-批量冻结,BATCH15-批量解冻扣款,BATCH16-基金资金清算,BATCH17-社保卡批量开户开卡,BATCH18-CDCT对账,BATCH19-理财平台资金清算,BATCH20-日终签约文件同步,BATCH21-批量收取手续费,BATCH22-IC卡商户批量入账,BATCH23-委托关系验证,BATCH24-ic卡对账文件,BATCH25-IC卡挂失销户到期处理文件请求文件,BATCH26-生成指定表文件,BATCH27-批量冲正',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `CHECK_RESULT` char(1) DEFAULT NULL COMMENT '结果|结果',
  `DR_SUM_NUM` int DEFAULT NULL COMMENT '借方总笔数|借方总笔数',
  `DR_SUM_AMT` decimal(17,2) DEFAULT NULL COMMENT '借方总金额|借方总金额',
  `CR_TOTAL_NUM` bigint DEFAULT NULL COMMENT '贷方总笔数|贷方总笔数',
  `CR_TOTAL_AMT` decimal(38,2) DEFAULT NULL COMMENT '贷方总金额|贷方总金额',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='存款贷款批量检查信息表|贷款回收检查文件，委托贷款批量转账检查文件处理，易贷通强制扣划检查文件处理';

-- ----------------------------
-- Table structure for rb_cl_batch_tran_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_cl_batch_tran_detail`;
CREATE TABLE `rb_cl_batch_tran_detail` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_NO` varchar(50) DEFAULT NULL COMMENT '批次号|批次号',
  `BATCH_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '批次明细序号|批次明细序号',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `DR_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '借方账号|借方账号',
  `DR_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '借方账户序号|借方账户序号',
  `DR_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '借方产品类型|借方产品类型',
  `DR_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '借方账户币种|借方账户币种',
  `CR_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '贷方账号|贷方账号',
  `CR_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '贷方产品类型|贷方产品类型',
  `CR_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '贷方账户币种|贷方账户币种',
  `CR_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '贷方账户序号|贷方账户序号',
  `RET_STATUS` varchar(2) DEFAULT NULL COMMENT '结果状态|结果状态|CS-贷方成功,CF-贷方失败,DS-借方成功,DF-借方失败,F-失败,S-成功,P-预处理',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `RES_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '限制编号|限制编号',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `WT_TRAN_DEAL_FLOW` char(1) DEFAULT NULL COMMENT '委托转账处理方式|委托转账处理方式',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='存款贷款批量转账明细表|贷款批量转账文件处理';

-- ----------------------------
-- Table structure for rb_cl_batch_tran_detail_hist
-- ----------------------------
DROP TABLE IF EXISTS `rb_cl_batch_tran_detail_hist`;
CREATE TABLE `rb_cl_batch_tran_detail_hist` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '批次明细序号|批次明细序号',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `DR_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '借方账号|借方账号',
  `DR_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '借方账户币种|借方账户币种',
  `DR_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '借方产品类型|借方产品类型',
  `DR_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '借方账户序号|借方账户序号',
  `CR_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '贷方账号|贷方账号',
  `CR_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '贷方产品类型|贷方产品类型',
  `CR_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '贷方账户币种|贷方账户币种',
  `CR_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '贷方账户序号|贷方账户序号',
  `RET_STATUS` varchar(2) DEFAULT NULL COMMENT '结果状态|结果状态|CS-贷方成功,CF-贷方失败,DS-借方成功,DF-借方失败,F-失败,S-成功,P-预处理',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `RES_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '限制编号|限制编号',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `WT_TRAN_DEAL_FLOW` char(1) DEFAULT NULL COMMENT '委托转账处理方式|委托转账处理方式',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='存款贷款批量转账明细历史表|委托贷款批量转账数据备份处理';

-- ----------------------------
-- Table structure for rb_cl_file_register
-- ----------------------------
DROP TABLE IF EXISTS `rb_cl_file_register`;
CREATE TABLE `rb_cl_file_register` (
  `EXE_ID` varchar(50) DEFAULT NULL COMMENT '执行ID|执行ID',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `FILE_CLASS` varchar(5) DEFAULT NULL COMMENT '文件种类|文件种类',
  `FILE_NAME` varchar(200) DEFAULT NULL COMMENT '文件名称|文件名称',
  `FILE_PATH` varchar(200) DEFAULT NULL COMMENT '文件路径|文件路径',
  `FILE_DESC` varchar(50) DEFAULT NULL COMMENT '文件描述|文件描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`),
  KEY `RB_CL_FILE_REGISTER_IND1M` (`TRAN_DATE`,`FILE_CLASS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='存贷文件登记表|贷款批量转账文件处理，贷款回收检查文件处理，委托贷款批量转账检查文件处理，贷款回收文件处理，易贷通强制扣划检查文件处理';

-- ----------------------------
-- Table structure for rb_client_merge
-- ----------------------------
DROP TABLE IF EXISTS `rb_client_merge`;
CREATE TABLE `rb_client_merge` (
  `MERGE_NO` varchar(50) NOT NULL COMMENT '合并编号|合并编号',
  `CLIENT_A` varchar(20) DEFAULT NULL COMMENT '客户A|客户A',
  `DOCUMENT_ID_A` varchar(50) DEFAULT NULL COMMENT '被合并客户证件号码|被合并客户证件号码',
  `DOCUMENT_TYPE_A` varchar(3) DEFAULT NULL COMMENT '被合并客户证件类型|被合并客户证件类型',
  `CH_CLIENT_NAME_A` varchar(200) DEFAULT NULL COMMENT '客户中文名1|客户中文名1',
  `CLIENT_B` varchar(20) DEFAULT NULL COMMENT '客户B|客户B',
  `CH_CLIENT_NAME_B` varchar(200) DEFAULT NULL COMMENT '客户中文名2|客户中文名2',
  `DOCUMENT_TYPE_B` varchar(3) DEFAULT NULL COMMENT '合并目标客户证件类型|合并目标客户证件类型',
  `DOCUMENT_ID_B` varchar(50) DEFAULT NULL COMMENT '合并目标客户证件号码|合并目标客户证件号码',
  `MERGE_FLAG` varchar(2) DEFAULT NULL COMMENT '是否合并标志|合并状态|C-合并 ,R-合并撤销 ,L-水平表目标数据处理完成,D-水平表被合并数据处理完成,U-垂直表目标数据处理完成,O-垂直表被合并数据处理完成',
  `MERGE_DATE` datetime DEFAULT NULL COMMENT '合并日期|合并日期',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  PRIMARY KEY (`MERGE_NO`),
  KEY `RB_CLIENT_MERGE_INDX2M` (`CLIENT_B`),
  KEY `RB_CLIENT_MERGE_INDX1M` (`CLIENT_A`),
  KEY `RB_CLIENT_MERGE_INDX3M` (`MERGE_DATE`,`MERGE_FLAG`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户合并信息表|存款客户合并信息表';

-- ----------------------------
-- Table structure for rb_clrec_deduction_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_clrec_deduction_detail`;
CREATE TABLE `rb_clrec_deduction_detail` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_NO` varchar(50) DEFAULT NULL COMMENT '批次号|批次号',
  `BATCH_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '批次明细序号|批次明细序号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `SETTLE_CLIENT` varchar(20) DEFAULT NULL COMMENT '结算客户号|结算客户号',
  `SETTLE_ACCT_CLASS` varchar(3) DEFAULT NULL COMMENT '结算账户分类|结算账户分类|PAY-付款账户,REC-收款账户,AUT-自动扣款账户,TPP-第三方账户,WTR-委托存款账户,WTS-委托结算账户,INT-利息入账账户,PRI-本金入账账户,TRA-定期自动转活期账户',
  `SETTLE_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '结算账号|结算账号',
  `SETTLE_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '结算账户产品类型|结算账户产品类型',
  `SETTLE_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '结算账户币种|结算账户币种',
  `SETTLE_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '结算账户序号|结算账户序号',
  `SETTLE_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '结算账户户名|结算账户户名',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `SETTLE_AMT` decimal(17,2) DEFAULT NULL COMMENT '结算金额|结算金额',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `AMT_CTL` char(1) DEFAULT NULL COMMENT '金额控制标志|金额控制标志|A-足额扣款,P-部分扣款',
  `AUTO_BLOCKING` char(1) DEFAULT NULL COMMENT '自动锁定标志|自动锁定标志|Y-是 ,N-否',
  `RES_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '限制编号|限制编号',
  `OTH_SETTLE_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '对手结算账户产品类型|对手结算账户产品类型',
  `OTH_SETTLE_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '对手结算账号|对手结算账号',
  `OTH_SETTLE_CLIENT` varchar(20) DEFAULT NULL COMMENT '对手结算客户号|对手结算客户号',
  `OTH_SETTLE_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '对手结算账户币种|对手结算账户币种',
  `OTH_SETTLE_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '对手结算账户序号|对手结算账户序号',
  `OTH_SETTLE_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '对手结算账户户名|对手结算账户户名',
  `PRIORITY` varchar(20) DEFAULT NULL COMMENT '优先级|优先级',
  `OPPO_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '行外对手账号|行外对手账号',
  `OPPO_BANK_CODE` varchar(20) DEFAULT NULL COMMENT '行外对手账户行号|行外对手账户行号',
  `OPPO_BANK_NAME` varchar(50) DEFAULT NULL COMMENT '行外对手账户行名|行外对手账户行名',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `RET_STATUS` varchar(2) DEFAULT NULL COMMENT '结果状态|结果状态|CS-贷方成功,CF-贷方失败,DS-借方成功,DF-借方失败,F-失败,S-成功,P-预处理',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `FIRST_LAST` char(1) DEFAULT NULL COMMENT '期初/期末|期初/期末|F-期初,L-期末,A-实际天数',
  `ORDER_NUM` bigint DEFAULT NULL COMMENT '排序 |排序 ',
  `SETTLE_NO` varchar(50) DEFAULT NULL COMMENT '结算编号|结算编号',
  `GROUP_ID` varchar(30) DEFAULT NULL COMMENT '流程组ID|流程组ID',
  `FIXED_AMT` decimal(17,2) DEFAULT NULL COMMENT '额定监管总金额|额定监管总金额',
  `AMT_TYPE` varchar(10) DEFAULT NULL COMMENT '金额类型|金额类型|BAL-余额,DDA-发放金额,INTP-逾期利息,LIM-额度金额,OD-透支金额,ODIP-逾期复利,ODPP-逾期罚息,OSL-未到期本金,PRD-逾期本金,PRI-本金,PF-净本金,INT-利息,ODI-复利,ODP-罚息,FEE-费用,UNI-非本金,ALL-本加息,DS-前收息金额,PRF-提前结清手续费,ODODP-罚息的复利,ODODI-复利的复利',
  `SCENE_ID` varchar(200) DEFAULT NULL COMMENT '场景ID |场景ID ',
  `ACCT_RES_OPERATE_TYPE` varchar(2) DEFAULT NULL COMMENT '账户限制操作类型|账户限制操作类型|01-新增,02-修改,03-解限',
  `DEAL_STATUS` char(1) DEFAULT NULL COMMENT '处理状态|处理状态|0-未处理,1-已处理',
  PRIMARY KEY (`SEQ_NO`),
  KEY `RB_CLREC_DEDUCTION_DETAIL_IND1M` (`BATCH_SEQ_NO`,`BATCH_NO`),
  KEY `RB_CLREC_DEDUCTION_DETAIL_INDX4M` (`BASE_ACCT_NO`),
  KEY `RB_CLREC_DEDUCTION_DETAIL_IND2M` (`BATCH_SEQ_NO`),
  KEY `RB_CLREC_DEDUCTION_DETAIL_INDX3M` (`SCENE_ID`,`TRAN_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='存款贷款回收扣款明细表|委托贷款批量转账数据备份处理';

-- ----------------------------
-- Table structure for rb_clrec_deduction_detail_hist
-- ----------------------------
DROP TABLE IF EXISTS `rb_clrec_deduction_detail_hist`;
CREATE TABLE `rb_clrec_deduction_detail_hist` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_SEQ_NO` varchar(50) NOT NULL COMMENT '批次明细序号|批次明细序号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) NOT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_SEQ_NO` varchar(5) NOT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `SETTLE_CLIENT` varchar(20) DEFAULT NULL COMMENT '结算客户号|结算客户号',
  `SETTLE_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '结算账号|结算账号',
  `SETTLE_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '结算账户币种|结算账户币种',
  `SETTLE_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '结算账户序号|结算账户序号',
  `SETTLE_ACCT_CLASS` varchar(3) DEFAULT NULL COMMENT '结算账户分类|结算账户分类|PAY-付款账户,REC-收款账户,AUT-自动扣款账户,TPP-第三方账户,WTR-委托存款账户,WTS-委托结算账户,INT-利息入账账户,PRI-本金入账账户,TRA-定期自动转活期账户',
  `SETTLE_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '结算账户户名|结算账户户名',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `SETTLE_AMT` decimal(17,2) DEFAULT NULL COMMENT '结算金额|结算金额',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `AMT_CTL` char(1) DEFAULT NULL COMMENT '金额控制标志|金额控制标志|A-足额扣款,P-部分扣款',
  `RES_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '限制编号|限制编号',
  `OTH_SETTLE_CLIENT` varchar(20) DEFAULT NULL COMMENT '对手结算客户号|对手结算客户号',
  `OTH_SETTLE_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '对手结算账号|对手结算账号',
  `OTH_SETTLE_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '对手结算账户产品类型|对手结算账户产品类型',
  `OTH_SETTLE_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '对手结算账户序号|对手结算账户序号',
  `OTH_SETTLE_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '对手结算账户币种|对手结算账户币种',
  `OTH_SETTLE_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '对手结算账户户名|对手结算账户户名',
  `OPPO_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '行外对手账号|行外对手账号',
  `OPPO_BANK_CODE` varchar(20) DEFAULT NULL COMMENT '行外对手账户行号|行外对手账户行号',
  `OPPO_BANK_NAME` varchar(50) DEFAULT NULL COMMENT '行外对手账户行名|行外对手账户行名',
  `AUTO_BLOCKING` char(1) DEFAULT NULL COMMENT '自动锁定标志|自动锁定标志|Y-是 ,N-否',
  `PRIORITY` varchar(20) DEFAULT NULL COMMENT '优先级|优先级',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `RET_STATUS` varchar(2) DEFAULT NULL COMMENT '结果状态|结果状态|CS-贷方成功,CF-贷方失败,DS-借方成功,DF-借方失败,F-失败,S-成功,P-预处理',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SETTLE_NO` varchar(50) DEFAULT NULL COMMENT '结算编号|结算编号',
  `GROUP_ID` varchar(30) DEFAULT NULL COMMENT '流程组ID|流程组ID',
  `FIXED_AMT` decimal(17,2) DEFAULT NULL COMMENT '额定监管总金额|额定监管总金额',
  `AMT_TYPE` varchar(10) DEFAULT NULL COMMENT '金额类型|金额类型|BAL-余额,DDA-发放金额,INTP-逾期利息,LIM-额度金额,OD-透支金额,ODIP-逾期复利,ODPP-逾期罚息,OSL-未到期本金,PRD-逾期本金,PRI-本金,PF-净本金,INT-利息,ODI-复利,ODP-罚息,FEE-费用,UNI-非本金,ALL-本加息,DS-前收息金额,PRF-提前结清手续费,ODODP-罚息的复利,ODODI-复利的复利',
  `SCENE_ID` varchar(200) DEFAULT NULL COMMENT '场景ID |场景ID ',
  `ACCT_RES_OPERATE_TYPE` varchar(2) DEFAULT NULL COMMENT '账户限制操作类型|账户限制操作类型|01-新增,02-修改,03-解限',
  `DEAL_STATUS` char(1) DEFAULT NULL COMMENT '处理状态|处理状态|0-未处理,1-已处理',
  PRIMARY KEY (`SEQ_NO`,`TRAN_DATE`),
  KEY `RB_CLREC_DEDUCTION_DETAIL_HIST_INDX3M` (`SCENE_ID`,`TRAN_DATE`),
  KEY `RB_CLREC_DEDUCTION_DETAIL_HIST_IND2M` (`BATCH_SEQ_NO`,`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='存款贷款回收扣款明细表|贷款回收存款扣款处理回盘文件生成处理';

-- ----------------------------
-- Table structure for rb_cm_cd_acct
-- ----------------------------
DROP TABLE IF EXISTS `rb_cm_cd_acct`;
CREATE TABLE `rb_cm_cd_acct` (
  `BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `SUB_ACCT_NO` varchar(50) NOT NULL COMMENT '子账户|子账户',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `SUB_CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '子渠道流水号|子渠道流水号',
  `SOURCE_TYPE` varchar(10) NOT NULL COMMENT '渠道类型|渠道类型',
  `SIGN_DATE` datetime DEFAULT NULL COMMENT '签约日期|签约日期',
  `BASE_CLIENT_NO` varchar(20) NOT NULL COMMENT '主账户客户号|主账户客户号',
  `INTERNAL_KEY` bigint NOT NULL COMMENT '账户内部键值|账户内部键值',
  `SUB_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '子账户中文名|子账户中文名',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `CM_CD_FLAG` char(1) DEFAULT NULL COMMENT '存管现管标志|存管现管标志|A-现管,B-存管,C-新现管',
  `CM_CD_OPERATE` char(1) NOT NULL COMMENT '现管存管操作标识|现管存管操作标识|A-新增,B-维护,C-注销',
  `XG_SIGN_STATUS` char(1) NOT NULL COMMENT '现金管理协议状态|现金管理系统专用|A-正常,C-取消',
  `LAST_CHANGE_DATE` datetime DEFAULT NULL COMMENT '最后修改日期|最后修改日期',
  `LAST_CHANGE_USER_ID` varchar(30) DEFAULT NULL COMMENT '最后修改柜员|最后修改柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BASE_ACCT_NO`,`SUB_ACCT_NO`),
  KEY `IDX_RB_CM_CD_ACCT_1M` (`SUB_ACCT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='现管存管子账户同步记录表|现管子账户系统签约';

-- ----------------------------
-- Table structure for rb_cm_cd_rec_acct
-- ----------------------------
DROP TABLE IF EXISTS `rb_cm_cd_rec_acct`;
CREATE TABLE `rb_cm_cd_rec_acct` (
  `BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `SUB_ACCT_NO` varchar(50) NOT NULL COMMENT '子账户|子账号/卡号',
  `REC_BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '回收账户|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `REC_INTERNAL_KEY` bigint DEFAULT NULL COMMENT '白名单账户主键|白名单账户主键，行内不为空，行外为空',
  `REC_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '回收账户名称|回收账号名称',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BASE_ACCT_NO`,`SUB_ACCT_NO`,`REC_BASE_ACCT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='现管存管白名单同步记录表|现管存管子账户白名单登记';

-- ----------------------------
-- Table structure for rb_contrast_batch
-- ----------------------------
DROP TABLE IF EXISTS `rb_contrast_batch`;
CREATE TABLE `rb_contrast_batch` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `START_DATE` datetime DEFAULT NULL COMMENT '开始日期|开始日期',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|结束日期',
  `BATCH_NUM` int DEFAULT NULL COMMENT '总计条数|总计条数',
  `SUCCESS_NUM` int DEFAULT NULL COMMENT '成功笔数|成功笔数',
  `FAILURE_NUMBER` int DEFAULT NULL COMMENT '失败数量|失败数量',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='暂停非柜面导入批次表|手工批量导入登记表';

-- ----------------------------
-- Table structure for rb_dc_branch_amt
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_branch_amt`;
CREATE TABLE `rb_dc_branch_amt` (
  `CHANNEL` varchar(10) NOT NULL COMMENT '渠道|渠道细类|JZ-集中作业,MT-柜面,ZD-智能网点自助交易平台,VT-远程智能自助银行,QT-XBankT10,QE-快窗智能高柜,CP-手机银行,EB-个人网上银行,CB-企业网上银行,CM-现金管理,WB-微信银行',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `STAGE_PROD_CLASS` varchar(5) DEFAULT NULL COMMENT '期次产品分类|期次产品分类|DC-大额存单,ST-结构性存款',
  `ISSUE_YEAR` varchar(5) DEFAULT NULL COMMENT '发行年度|发行年度',
  `TOTAL_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '总额度|总额度',
  `DISTRIBUTE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已分配额度|已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度',
  `HOLDING_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已占用额度|已占用额度',
  `LEAVE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '剩余额度|剩余额度',
  `BACK_STATUS` char(1) DEFAULT NULL COMMENT '额度回收状态|额度回收状态|N-未恢复,S-成功,F-失败',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`CHANNEL`,`BRANCH`,`STAGE_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构配额信息表|大额存单在各个机构配置的额度信息';

-- ----------------------------
-- Table structure for rb_dc_branch_quota
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_branch_quota`;
CREATE TABLE `rb_dc_branch_quota` (
  `PARENT_QUOTA_CLASS` varchar(50) NOT NULL COMMENT '上级额度类型|上级额度类型',
  `INDIVIDUAL_FLAG` char(1) DEFAULT NULL COMMENT '对公对私标志|对公对私标志|Y-对私,N-对公',
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `ISSUE_YEAR` varchar(5) NOT NULL COMMENT '发行年度|发行年度',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `LEAVE_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '剩余额度|剩余额度',
  `DISTRIBUTE_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '已分配额度|已分配额度',
  `HOLDING_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '占用额度|占用额度',
  `TOTAL_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '总额度|总额度',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `SELL_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已出售额度|已出售额度',
  PRIMARY KEY (`STAGE_CODE`,`ISSUE_YEAR`,`BRANCH`),
  KEY `IDX_RB_DC_BRANCH_LIMIT_1M` (`STAGE_CODE`,`ISSUE_YEAR`,`BRANCH`,`CCY`,`PARENT_QUOTA_CLASS`,`INDIVIDUAL_FLAG`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='大额存单机构额度表|记录大额存单机构额度信息';

-- ----------------------------
-- Table structure for rb_dc_channel_amt
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_channel_amt`;
CREATE TABLE `rb_dc_channel_amt` (
  `CHANNEL` varchar(10) NOT NULL COMMENT '渠道|渠道细类|JZ-集中作业,MT-柜面,ZD-智能网点自助交易平台,VT-远程智能自助银行,QT-XBankT10,QE-快窗智能高柜,CP-手机银行,EB-个人网上银行,CB-企业网上银行,CM-现金管理,WB-微信银行',
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `STAGE_PROD_CLASS` varchar(5) DEFAULT NULL COMMENT '期次产品分类|期次产品分类|DC-大额存单,ST-结构性存款',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `ISSUE_YEAR` varchar(5) DEFAULT NULL COMMENT '发行年度|发行年度',
  `TOTAL_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '总额度|总额度',
  `LEAVE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '剩余额度|剩余额度',
  `BACK_STATUS` char(1) DEFAULT NULL COMMENT '额度回收状态|额度回收状态|N-未恢复,S-成功,F-失败',
  `DISTRIBUTE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已分配额度|已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度',
  `HOLDING_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已占用额度|已占用额度',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SELL_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已出售额度|已出售额度',
  PRIMARY KEY (`CHANNEL`,`STAGE_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='渠道配额管理表|各渠道配额使用信息';

-- ----------------------------
-- Table structure for rb_dc_customer_base_class
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_customer_base_class`;
CREATE TABLE `rb_dc_customer_base_class` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `STAGE_CODE` varchar(50) DEFAULT NULL COMMENT '期次代码|期次代码',
  `CUSTOMER_PARENT` varchar(20) DEFAULT NULL COMMENT '客户群体大类|客户群体大类',
  `CUSTOMER_SON` varchar(20) DEFAULT NULL COMMENT '客户群体小类|客户群体小类',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='大额存单客户群体分类表';

-- ----------------------------
-- Table structure for rb_dc_customer_limit
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_customer_limit`;
CREATE TABLE `rb_dc_customer_limit` (
  `CUSTOMER_BASE` varchar(20) DEFAULT NULL COMMENT '客户群体|客户群体',
  `STAGE_CODE` varchar(50) DEFAULT NULL COMMENT '期次代码|期次代码',
  `CUSTOMER_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '大额存单客户限额|大额存单客户限额',
  `MULT_STAGE_FLAG` char(1) DEFAULT NULL COMMENT '是否多期次|是否多期次',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  PRIMARY KEY (`SEQ_NO`),
  UNIQUE KEY `customerbase_stagecode_unique` (`CUSTOMER_BASE`,`STAGE_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='大额存单客户限额表|大额存单客户限额表';

-- ----------------------------
-- Table structure for rb_dc_indvl_copr_quota
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_indvl_copr_quota`;
CREATE TABLE `rb_dc_indvl_copr_quota` (
  `PARENT_QUOTA_CLASS` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '上级额度类型|上级额度类型',
  `INDIVIDUAL_FLAG` char(1) NOT NULL COMMENT '对公对私标志|对公对私标志|Y-对私,N-对公',
  `STAGE_CODE` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '期次代码|期次代码',
  `ISSUE_YEAR` varchar(5) NOT NULL COMMENT '发行年度|发行年度',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `LEAVE_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '剩余额度|剩余额度',
  `DISTRIBUTE_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '已分配额度|已分配额度',
  `HOLDING_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '占用额度|占用额度',
  `TOTAL_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '总额度|总额度',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `STAGE_TYPE` char(1) NOT NULL COMMENT '期次类型|个人C，对公A，机构B|A-个人C,B-对公A,C-机构B,',
  PRIMARY KEY (`INDIVIDUAL_FLAG`,`ISSUE_YEAR`,`CCY`,`TRAN_TIMESTAMP`,`STAGE_TYPE`),
  KEY `IDX_RB_DC_PROD_LIMIT_1M` (`STAGE_CODE`,`ISSUE_YEAR`,`BRANCH`,`CCY`,`PARENT_QUOTA_CLASS`,`INDIVIDUAL_FLAG`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='大额存单对公/对私额度表|大额存单对公/对私额度表';

-- ----------------------------
-- Table structure for rb_dc_info_asyn_hub
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_info_asyn_hub`;
CREATE TABLE `rb_dc_info_asyn_hub` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型|OPEN-申购，TRF-转让，DRAW-支取，STAGE_ADD-期次新增，REVERSAL-冲正',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `DATA_FLAG` varchar(10) DEFAULT NULL COMMENT '数据来源|同步信息的来源|ACCT-账户信息，BAL-余额信息，STAGE-期次信息',
  `STATUS` char(1) DEFAULT NULL COMMENT '状态|同步状态|0-未同步，1-同步且成功，2-同步且失败',
  `ASYN_DATE` datetime DEFAULT NULL COMMENT '同步日期|同步日期',
  `FAILURE_TIMES` int DEFAULT NULL COMMENT '累积失败次数|连续失败次数',
  `JSON_DATA` longtext COMMENT '交易报文信息|交易报文信息',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='核心向HUB同步信息登记表|核心向HUB同步信息登记表';

-- ----------------------------
-- Table structure for rb_dc_quota_apply_info
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_quota_apply_info`;
CREATE TABLE `rb_dc_quota_apply_info` (
  `APPROVAL_NO` varchar(50) NOT NULL COMMENT '审批单号|审批单号',
  `APPLY_BRANCH` varchar(50) DEFAULT NULL COMMENT '申请机构|申请机构',
  `APPLY_USER_ID` varchar(30) DEFAULT NULL COMMENT '申请柜员|申请柜员',
  `APPLY_STATUS` char(1) DEFAULT NULL COMMENT '现金凭证预约状态|现金凭证预约状态|P-待审批,A-申请,D-删除,X-撤销,C-确认,B-退回,E-完成,O-在途,R-拒绝',
  `APPLY_AMT` decimal(17,2) DEFAULT NULL COMMENT '预约总金额|预约总金额',
  `STAGE_CODE` varchar(50) DEFAULT NULL COMMENT '期次代码|期次代码',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`APPROVAL_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='大额存单OA占用额度表';

-- ----------------------------
-- Table structure for rb_dc_rec_result
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_rec_result`;
CREATE TABLE `rb_dc_rec_result` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `AMT_TYPE` varchar(10) NOT NULL COMMENT '金额类型|金额类型',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `DEAL_NO` varchar(50) NOT NULL COMMENT '交易流水号|原处理编号',
  `DEAL_RESULT` varchar(200) NOT NULL COMMENT '处理结果|HUB处理结果',
  `DEAL_REASON` varchar(200) DEFAULT NULL COMMENT '处理说明|HUB处理失败原因',
  `RET_CODE` varchar(50) DEFAULT NULL COMMENT '状态码|状态码',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `ORIG_REFERENCE` varchar(50) NOT NULL COMMENT '源交易参考号|源交易参考号',
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  PRIMARY KEY (`SEQ_NO`,`TRAN_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='大额存单结清入账结果表|大额存单结清入账结果表处理大额存单入账结果文件';

-- ----------------------------
-- Table structure for rb_dc_redemption_info
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_redemption_info`;
CREATE TABLE `rb_dc_redemption_info` (
  `ISSUE_YEAR` varchar(5) NOT NULL COMMENT '发行年度|发行年度',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `TOHONOR_RATE` decimal(15,8) DEFAULT NULL COMMENT '赎回利率|赎回利率',
  `REDEMPTION_STATUS` char(1) DEFAULT NULL COMMENT '赎回状态|赎回状态|O-未处理,P-已处理,C-已撤销',
  `STAGE_PROD_CLASS` varchar(5) DEFAULT NULL COMMENT '期次产品分类|期次产品分类|DC-大额存单,ST-结构性存款',
  `TOHONOR_DATE` datetime DEFAULT NULL COMMENT '赎回日期|赎回日期',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `AUTH_USER_ID` varchar(30) DEFAULT NULL COMMENT '授权柜员|授权柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `APPLY_DATE` datetime DEFAULT NULL COMMENT '申请日期|申请日期',
  `APPLY_CLIENT_NAME` varchar(50) DEFAULT NULL COMMENT '申请人名称|申请人名称',
  `TITLE_INFO` varchar(30) DEFAULT NULL COMMENT '标题|标题',
  `APPROVAL_NO` varchar(50) DEFAULT NULL COMMENT '审批单号|审批单号',
  PRIMARY KEY (`ISSUE_YEAR`,`PROD_TYPE`,`STAGE_CODE`,`CCY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='大额存单赎回申请表|大额存单赎回申请';

-- ----------------------------
-- Table structure for rb_dc_stage_define
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_stage_define`;
CREATE TABLE `rb_dc_stage_define` (
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `STAGE_CODE_DESC` varchar(200) DEFAULT NULL COMMENT '期次描述|中文期次描述',
  `ISSUE_YEAR` varchar(5) DEFAULT NULL COMMENT '发行年度|发行年度',
  `ISSUE_START_DATE` datetime DEFAULT NULL COMMENT '发行起始日期|发行起始日期',
  `ISSUE_END_DATE` datetime DEFAULT NULL COMMENT '发行终止日期|发行终止日期',
  `PRECONTRACT_START_TIME` varchar(26) DEFAULT NULL COMMENT '预约开始时间|预约开始时间',
  `PRECONTRACT_END_TIME` varchar(26) DEFAULT NULL COMMENT '预约结束时间|预约结束时间',
  `SALE_START_TIME` varchar(26) DEFAULT NULL COMMENT '起售时间|起售时间',
  `SALE_END_TIME` varchar(26) DEFAULT NULL COMMENT '止售时间|止售时间',
  `STAGE_PROD_CLASS` varchar(5) DEFAULT NULL COMMENT '期次产品分类|期次产品分类|DC-大额存单,ST-结构性存款',
  `STAGE_LIMIT_CLASS` varchar(10) DEFAULT NULL COMMENT '额度扣减类型|额度扣减类型',
  `STAGE_REMARK` varchar(200) DEFAULT NULL COMMENT '期次详细备注|期次详细备注',
  `STAGE_STATUS` varchar(2) DEFAULT NULL COMMENT '期次状态|期次状态|结构性存款,    N-发行,    R-认购期,    RE-认购结束期,    D-封闭期,    G-结息,    PS-提前终止,    RS-发行失败,    S-终止  ,大额存单,    N-发行,    R-认购期,    G-申购期,    S-终止,    D-删除',
  `SALE_TYPE` char(1) DEFAULT NULL COMMENT '销售方式|销售方式|C-竞售,P-配额',
  `OPERATE_METHOD` varchar(2) DEFAULT NULL COMMENT '配额类型|配额类型|NO-不分配额度,CH-可按渠道分配,BR-可按分行分配,SB-可按支行分配',
  `TOTAL_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '总额度|总额度',
  `LEAVE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '剩余额度|剩余额度',
  `DISTRIBUTE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已分配额度|已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度',
  `HOLDING_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已占用额度|已占用额度',
  `BACK_STATUS` char(1) DEFAULT NULL COMMENT '额度回收状态|额度回收状态|N-未恢复,S-成功,F-失败',
  `RATION_TYPE` char(1) DEFAULT NULL COMMENT '配售方式|配售方式|0-全额配售,1-比例配售',
  `TRANSFER_FLAG` char(1) DEFAULT NULL COMMENT '转账标志|转账标志|Y-是,N-否',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `TERM` varchar(5) DEFAULT NULL COMMENT '存期期限|存期',
  `TERM_TYPE` char(1) DEFAULT NULL COMMENT '期限类型|存期类型|Y-年,Q-季,M-月,W-周,D-日',
  `RESET_INT_FREQ` varchar(5) DEFAULT NULL COMMENT '利率重置频率|期次定义利率重置频率',
  `STAGE_MAX_AMT` decimal(17,2) DEFAULT NULL COMMENT '期次最大购买金额|期次最大购买金额',
  `STAGE_MIN_AMT` decimal(17,2) DEFAULT NULL COMMENT '期次起存金额|期次起存金额',
  `GET_INT_FREQ` varchar(5) DEFAULT NULL COMMENT '取息频率|取息频率',
  `INT_CALC_TYPE` char(1) DEFAULT NULL COMMENT '计息类型|计息方式零息|0-零息,1-浮动计息,4-固定利率',
  `PAY_INT_TYPE` varchar(3) DEFAULT NULL COMMENT '付息方式|付息方式|ARP-一次还本付息和定期付息,NAR-到期还本',
  `PRE_WITHDRAW_FLAG` char(1) DEFAULT NULL COMMENT '是否允许提前支取|是否允许提前支取|Y-是,N-否',
  `PART_WITHDRAW_NUM` int DEFAULT NULL COMMENT '部提次数|部提次数',
  `REDEMPTION_FLAG` char(1) DEFAULT NULL COMMENT '是否可赎回|是否可赎回|Y-是,N-否',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `AUTO_SETTLE_FLAG` char(1) DEFAULT NULL COMMENT '自动结清标志|自动结清标志|Y-是,N-否',
  `WHITE_SELL_FLAG` char(1) DEFAULT NULL COMMENT '是否白名单发售|是否白名单发售Y-是，N-否|Y-是,N-否',
  `CUSTOMER_BASE` varchar(20) DEFAULT NULL COMMENT '客户群体|客户群体',
  `FIXED_RATE` decimal(15,8) DEFAULT NULL COMMENT '固定利率|固定利率',
  `ALLOW_BRANCH` varchar(20) DEFAULT NULL COMMENT '允许购买的机构|允许购买的机构',
  `ALLOW_ACCT_CLASS` char(1) DEFAULT NULL COMMENT '允许购买的账户分类|允许购买的账户分类 | 1-一类户，2-二类户，3-不限制',
  `TRF_THRU_DATE` varchar(5) DEFAULT NULL COMMENT '转让截止日 | 正整数的数字|转让截止日 | 正整数的数字',
  `TRF_ORDER_THRU_DATE` varchar(5) DEFAULT NULL COMMENT '转让挂单截止日|转让挂单截止日 | 正整数的数字',
  `NEW_AND_OLD_FUND_FLAG` char(1) DEFAULT NULL COMMENT '新旧资金标识  | Y-新资金 N-不区分|新旧资金标识  | Y-不区分 N-新资金',
  `STAGE_CODE_DESC_SECOND` varchar(50) DEFAULT NULL COMMENT '第二语言期次描述|第二语言期次描述',
  `STAGE_REMARK_SECOND` varchar(200) DEFAULT NULL COMMENT '第二语言期次详细备注|第二语言期次详细备注',
  `STAGE_CODE_DESC_EN` varchar(200) DEFAULT NULL COMMENT '英文期次描述|英文期次描述',
  `ALLOW_BUY_WAY_CD` char(1) DEFAULT NULL COMMENT '支持组合购买方式|1-单独购买2-组合购买3-单买与组合买',
  `ISSUE_END_TIME` varchar(20) DEFAULT NULL COMMENT '发行终止时间|发行终止时间 yyyyMMdd HH:mm',
  `ISSUE_START_TIME` varchar(20) DEFAULT NULL COMMENT '发行起始时间|发行起始时间 yyyyMMdd HH:mm',
  `STAGE_TYPE` char(1) DEFAULT NULL COMMENT '期次类型|个人C，对公A，机构B|A-个人C,B-对公A,C-机构B,',
  `SELL_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已出售额度|已出售额度',
  PRIMARY KEY (`STAGE_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='期次定义表|结构性存款期次信息定义表';

-- ----------------------------
-- Table structure for rb_dc_stage_define_attach
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_stage_define_attach`;
CREATE TABLE `rb_dc_stage_define_attach` (
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `AVAILABLE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '可用额度|可用额度',
  `KEEP_MIN_BAL` decimal(17,2) DEFAULT NULL COMMENT '最小留存金额|最小留存金额',
  `INT_TYPE` varchar(5) DEFAULT NULL COMMENT '利率类型|利率类型',
  `REAL_RATE` decimal(15,8) DEFAULT NULL COMMENT '执行利率|执行利率',
  `IS_TRF_ENABLED` char(1) DEFAULT NULL COMMENT '是否可转让标志|是否可转让标志',
  `TOHONOR_RATE` decimal(15,8) DEFAULT NULL COMMENT '赎回利率|赎回利率',
  `INT_START_DATE` datetime DEFAULT NULL COMMENT '起息日|起息日',
  `MATURITY_DATE` datetime DEFAULT NULL COMMENT '到期日期|到期日期',
  `INLAND_OFFSHORE` char(1) DEFAULT NULL COMMENT '境内境外标志|境内境外标志|I-境内,O-境外',
  `PROMISSORY_REDEEM_DATE` datetime DEFAULT NULL COMMENT '原约定赎回日期|原约定赎回日期',
  `EMAIL` varchar(200) DEFAULT NULL COMMENT '电子邮件|电子邮件',
  `SPREAD_PERCENT` decimal(15,8) DEFAULT NULL COMMENT '浮动百分比|浮动百分比',
  `CHANGE_MIN_AMT` decimal(17,2) DEFAULT NULL COMMENT '最小变动金额|最小变动金额',
  `DIRECTION_CHARGE_INT_FLAG` char(1) DEFAULT NULL COMMENT '是否指定收息|是否指定收息|Y-是,N-否',
  `INT_START_FLAG` char(1) DEFAULT NULL COMMENT '起息标识|起息标识|0-立即起息,1-募集结束日起息',
  `ON_SALE_CHANNEL` varchar(100) DEFAULT NULL COMMENT '可售渠道|可售渠道',
  `TRF_IN_FEE_AMT` decimal(17,2) DEFAULT NULL COMMENT '转入费用|转入费用',
  `TRF_OUT_FEE_AMT` decimal(17,2) DEFAULT NULL COMMENT '转出费用|转出费用',
  `ALLOW_FUND_SOURCE_INNER_FLAG` char(1) DEFAULT NULL COMMENT '是否允许资金来源为内部户|是否允许资金来源为内部户|Y-是,N-否',
  `PROD_DESC_ADDRESS` varchar(200) DEFAULT NULL COMMENT '产品说明书链接|产品说明书链接',
  `REDEMPTION_INT_TYPE` varchar(5) DEFAULT NULL COMMENT '赎回利率类型|赎回利率类型',
  `REDEMPTION_INT_FLAG` varchar(2) DEFAULT NULL COMMENT '赎回利率标识|赎回利率标识',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SG_MIN_AMT` decimal(17,2) DEFAULT NULL COMMENT '单次最小支取金额|单次最小支取金额',
  `TRF_IN_FEE_TYPE` varchar(20) DEFAULT NULL COMMENT '转入费用类型|转入费用类型',
  `SELL_BRANCH` varchar(500) DEFAULT NULL COMMENT '出售分行或者出售机构|出售分行或者出售机构，多可以是一个或者多个，定义多个时，需要用分隔符 | 进行分开存储',
  `TRF_OUT_FEE_TYPE` varchar(20) DEFAULT NULL COMMENT '转出费用类型|转出费用类型',
  `SG_MAX_AMT` decimal(17,2) DEFAULT NULL COMMENT '单笔认购最大金额|单笔认购最大金额',
  `FLOAT_RATE` decimal(15,8) DEFAULT NULL COMMENT '浮动利率|浮动利率',
  `SETTLE_ACCT_TYPE` char(1) DEFAULT NULL COMMENT '结算账户类型|结算账户类型|R-Retail零售账户,I-Internal内部账户,N-Nostro 往账账户,V-Vostro 往来账账户,C-Card Account 卡号 ',
  `CALM_DAYS` int DEFAULT NULL COMMENT '冷静期|大额存单兑付冷静期',
  `COMB_PROD_FLAG` char(1) DEFAULT NULL COMMENT '是否组合产品|是否组合产品|0-存单买,1-纯组合,2-单买&组合买',
  `TRF_FLAG` char(1) DEFAULT NULL COMMENT '转让标志|转让标志|1-转让,2-回购,3-卖断,4-行内转让,Y-是,N-否',
  PRIMARY KEY (`STAGE_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='期次定义附加表|结构性存款期次定义附加表';

-- ----------------------------
-- Table structure for rb_dc_stage_info
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_stage_info`;
CREATE TABLE `rb_dc_stage_info` (
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `STAGE_PROD_CLASS` varchar(5) DEFAULT NULL COMMENT '期次产品分类|期次产品分类|DC-大额存单,ST-结构性存款',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `TOTAL_LIMIT` decimal(17,2) NOT NULL COMMENT '总额度|总额度',
  `DISTRIBUTE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已分配额度|已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度',
  `HOLDING_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已占用额度|已占用额度',
  `LEAVE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '剩余额度|剩余额度',
  `ISSUE_YEAR` varchar(5) NOT NULL COMMENT '发行年度|发行年度',
  `PREV_HOLDING_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '上次已占用额度|上次已占用额度',
  `PREV_USED_AMT` decimal(17,2) DEFAULT NULL COMMENT '上次已使用额度|上次已使用额度',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `LAST_CHANGE_DATE` datetime DEFAULT NULL COMMENT '最后修改日期|最后修改日期',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`STAGE_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='期次历史信息表|大额存单年度发行计划录入时候插入';

-- ----------------------------
-- Table structure for rb_dc_stage_int
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_stage_int`;
CREATE TABLE `rb_dc_stage_int` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `ACTUAL_RATE` decimal(15,8) DEFAULT NULL COMMENT '行内利率|在人行基准利率调整后对客发布的行内利率',
  `FLOAT_RATE` decimal(15,8) DEFAULT NULL COMMENT '浮动利率|浮动利率',
  `INT_CALC_TYPE` char(1) DEFAULT NULL COMMENT '计息类型|计息方式零息|0-零息,1-浮动计息,4-固定利率',
  `REAL_RATE` decimal(15,8) DEFAULT NULL COMMENT '执行利率|执行利率',
  `EVENT_TYPE` varchar(20) DEFAULT NULL COMMENT '事件类型|事件类型',
  `INT_TYPE` varchar(5) DEFAULT NULL COMMENT '利率类型|利率类型',
  `ISSUE_YEAR` varchar(5) NOT NULL COMMENT '发行年度|发行年度',
  `STAGE_PROD_CLASS` varchar(5) DEFAULT NULL COMMENT '期次产品分类|期次产品分类|DC-大额存单,ST-结构性存款',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`,`STAGE_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='期次管理利率信息表|结构性存款产品期次定义维护';

-- ----------------------------
-- Table structure for rb_dc_stage_int_matrix
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_stage_int_matrix`;
CREATE TABLE `rb_dc_stage_int_matrix` (
  `MATRIX_NO` varchar(50) NOT NULL COMMENT '阶梯序号|阶梯序号',
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `INT_TYPE` varchar(5) DEFAULT NULL COMMENT '利率类型|利率类型',
  `YEAR_BASIS` varchar(3) DEFAULT NULL COMMENT '年基准天数|年基准天数|360-按360天计算日利率,365-按365天计算日利率,366-按366天计算日利率',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `PERIOD_FREQ` varchar(5) DEFAULT NULL COMMENT '频率id|频率id',
  `DAY_NUM` int NOT NULL COMMENT '每期天数|DAY_MTH为D时，取ADD_NO值；为M时，值为7* ADD_NO；为Y时，值为360* ADD_NO',
  `REAL_RATE` decimal(15,8) NOT NULL COMMENT '执行利率|执行利率',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`MATRIX_NO`,`STAGE_CODE`),
  KEY `IDX_INTMATRIX_STAGECODEM` (`STAGE_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='期次产品提前支取利率分段表|期次产品提前支取利率分段表，期限靠档利率维护';

-- ----------------------------
-- Table structure for rb_dc_stage_observe_days
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_stage_observe_days`;
CREATE TABLE `rb_dc_stage_observe_days` (
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `OBSERVE_START_DATE` datetime NOT NULL COMMENT '观察起始日期|观察起始日期',
  `OBSERVE_END_DATE` datetime DEFAULT NULL COMMENT '观察终止日期|观察终止日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`STAGE_CODE`,`PROD_TYPE`,`OBSERVE_START_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='期次观察日表|结构性存款期次新增';

-- ----------------------------
-- Table structure for rb_dc_stage_quota
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_stage_quota`;
CREATE TABLE `rb_dc_stage_quota` (
  `PARENT_QUOTA_CLASS` varchar(50) NOT NULL COMMENT '上级额度类型|上级额度类型',
  `INDIVIDUAL_FLAG` char(1) DEFAULT NULL COMMENT '对公对私标志|对公对私标志|Y-对私,N-对公',
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `ISSUE_YEAR` varchar(5) NOT NULL COMMENT '发行年度|发行年度',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `LEAVE_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '剩余额度|剩余额度',
  `DISTRIBUTE_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '已分配额度|已分配额度',
  `HOLDING_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '占用额度|占用额度',
  `TOTAL_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '总额度|总额度',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `SELL_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已出售额度|已出售额度',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  PRIMARY KEY (`SEQ_NO`),
  KEY `IDX_RB_DC_STAGE_LIMIT_1M` (`INDIVIDUAL_FLAG`,`STAGE_CODE`,`ISSUE_YEAR`,`BRANCH`,`CCY`,`PARENT_QUOTA_CLASS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='大额存单期次额度表|记录大额存单期次额度信息';

-- ----------------------------
-- Table structure for rb_dc_stage_rule_define
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_stage_rule_define`;
CREATE TABLE `rb_dc_stage_rule_define` (
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `MATURITY_DATE` datetime DEFAULT NULL COMMENT '到期日期|到期日期',
  `OBSERVE_END_DATE` datetime DEFAULT NULL COMMENT '观察终止日期|观察终止日期',
  `OBSERVE_START_DATE` datetime DEFAULT NULL COMMENT '观察起始日期|观察起始日期',
  `INIT_AMT` decimal(17,2) DEFAULT NULL COMMENT '认购起存金额|认购起存金额',
  `SG_MAX_AMT` decimal(17,2) DEFAULT NULL COMMENT '单笔认购最大金额|单笔认购最大金额',
  `ACCR_RATE` decimal(15,8) DEFAULT NULL COMMENT '计提利率|触碰规则后 期次对应的计提利率',
  `ACR_RATE_TYPE` char(1) DEFAULT NULL COMMENT '计提利率规则|计提利率规则|L-靠低档收益计息,H-靠高档收益计息,P-业绩参数年化计息,A-按实际收益计息,B-按基准利率收益计息',
  `ACTUAL_RATE` decimal(15,8) DEFAULT NULL COMMENT '行内利率|在人行基准利率调整后对客发布的行内利率',
  `REAL_RATE` decimal(15,8) DEFAULT NULL COMMENT '执行利率|执行利率',
  `OPEN_RATE` decimal(15,8) DEFAULT NULL COMMENT '开户利率|开户利率',
  `FLOAT_RATE` decimal(15,8) DEFAULT NULL COMMENT '浮动利率|浮动利率',
  `HIGH_GRADE_RATE` decimal(15,8) DEFAULT NULL COMMENT '高档利率|高档利率',
  `LOW_END_RATE` decimal(15,8) DEFAULT NULL COMMENT '低档利率|低档利率',
  `PRE_RATE` decimal(15,8) DEFAULT NULL COMMENT '提前支取利率|提前支取利率',
  `STAGE_FIXED_RATE` decimal(15,8) DEFAULT NULL COMMENT '期次级固定利率|期次级固定利率',
  `STAGE_SPREAD_RATE` decimal(15,8) DEFAULT NULL COMMENT '期次级浮动百分点|期次级浮动百分点',
  `STAGE_PERCENT_RATE` decimal(5,2) DEFAULT NULL COMMENT '期次级浮动百分比|期次级浮动百分比',
  `YEARS_RATE` decimal(15,8) DEFAULT NULL COMMENT '年化利率|年化利率',
  `AMT_UNIT` decimal(17,2) DEFAULT NULL COMMENT '金额单位|大额存单购买单位',
  `HIGH_THRESHOLD` decimal(17,2) DEFAULT NULL COMMENT '最高价格|最高价格',
  `RETRY_FLAG` char(1) DEFAULT NULL COMMENT '是否重算|是否重算|Y-是,N-否',
  `TOUCH_FLAG` char(1) DEFAULT NULL COMMENT '是否触碰|是否触碰|Y-是,N-否',
  `LOW_THRESHOLD` decimal(17,2) DEFAULT NULL COMMENT '最低价格|最低价格',
  `OBSERVE_FLAG` char(1) DEFAULT NULL COMMENT '是否设置观察日|是否设置观察日|Y-是,N-否',
  `TOUCH_STOP_FLAG` char(1) DEFAULT NULL COMMENT '是否终止产品|是否终止产品|Y-是,N-否',
  `UNDERLYING_ID` varchar(50) DEFAULT NULL COMMENT '标的物代码|标的物代码',
  `STRUCT_CLASS` char(1) NOT NULL COMMENT '结构性存款结构分类|结构性存款结构分类|T-触碰,E-指数,S-区间累积,F-固定',
  `TOUCH_TYPE` varchar(3) DEFAULT NULL COMMENT '触碰类型|触碰类型|UP-向上触碰,DW-向下触碰,NP-向上不触碰,ND-向下不触碰,NIR-指数看涨（非线性）,NIB-指数看跌（非线性）,LIR-指数看涨（线性）,LIB-指数看跌（线性）',
  `RULE_DESC` varchar(200) DEFAULT NULL COMMENT '规则描述|规则描述',
  `IN_SECTION_DAYS` int DEFAULT NULL COMMENT '区间内天数|区间内天数',
  `OUT_SECTION_DAYS` int DEFAULT NULL COMMENT '区间外天数|区间外天数',
  `STAGE_INIT_PRICE` decimal(17,2) DEFAULT NULL COMMENT '期初价格|期初价格',
  `STAGE_LOW_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '期次成立最低额度|期次成立最低额度',
  `STAGE_RISK_LEVEL` varchar(2) DEFAULT NULL COMMENT '期次风险等级|期次风险等级|R1-低风险,R2-中低风险,R3-中风险,R4-中高风险,R5-高风险',
  `TOUCH_PERCENT` decimal(5,2) DEFAULT NULL COMMENT '触碰百分比|触碰百分比',
  `SETTLE_DAYS` int DEFAULT NULL COMMENT '清算天数|清算天数',
  `SETTLE_DATE` datetime DEFAULT NULL COMMENT '结算日期|结算日期',
  `AUTH_USER_ID` varchar(30) DEFAULT NULL COMMENT '授权柜员|授权柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `FEE_TYPE` varchar(20) DEFAULT NULL COMMENT '费用类型|费率类型',
  PRIMARY KEY (`STAGE_CODE`,`PROD_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='期次规则定义表|结构性存款期次新增';

-- ----------------------------
-- Table structure for rb_dc_white_list
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_white_list`;
CREATE TABLE `rb_dc_white_list` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `STAGE_CODE` varchar(50) DEFAULT NULL COMMENT '期次代码|期次代码',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `CH_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户中文名称|客户中文名称',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `TRAN_FILE_RESULT` char(1) DEFAULT NULL COMMENT '交易返回结果|交易返回结果|N-未生成,Y-已生成',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='预约白名单管理登记簿|批量录入白名单';

-- ----------------------------
-- Table structure for rb_dc_white_quota
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_white_quota`;
CREATE TABLE `rb_dc_white_quota` (
  `PARENT_QUOTA_CLASS` varchar(50) DEFAULT NULL COMMENT '上级额度类型|上级额度类型',
  `STAGE_TYPE` char(1) DEFAULT NULL COMMENT '期次类型|个人C，对公A，机构B|A-个人C,B-对公A,C-机构B,',
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `ISSUE_YEAR` varchar(5) NOT NULL COMMENT '发行年度|发行年度',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `LEAVE_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '剩余额度|剩余额度',
  `DISTRIBUTE_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '已分配额度|已分配额度',
  `HOLDING_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '占用额度|占用额度',
  `TOTAL_QUOTA` decimal(17,2) DEFAULT NULL COMMENT '总额度|总额度',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `SELL_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已出售额度|已出售额度',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  PRIMARY KEY (`STAGE_CODE`,`ISSUE_YEAR`,`CCY`,`CLIENT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='大额存单白名单额度表|大额存单白名单额度表';

-- ----------------------------
-- Table structure for rb_dc_year_plan
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_year_plan`;
CREATE TABLE `rb_dc_year_plan` (
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `ISSUE_YEAR` varchar(5) NOT NULL COMMENT '发行年度|发行年度',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `LEAVE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '剩余额度|剩余额度',
  `ADJUST_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '调整额度|调整额度',
  `DISTRIBUTE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已分配额度|已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度',
  `RECORD_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '备案额度|调整后备案额度',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`CCY`,`ISSUE_YEAR`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='年度发行计划信息表|大额存单年度发行计划录入';

-- ----------------------------
-- Table structure for rb_dc_year_plan_info
-- ----------------------------
DROP TABLE IF EXISTS `rb_dc_year_plan_info`;
CREATE TABLE `rb_dc_year_plan_info` (
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `ISSUE_YEAR` varchar(5) DEFAULT NULL COMMENT '发行年度|发行年度',
  `RECORD_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '备案额度|调整后备案额度',
  `ADJUST_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '调整额度|调整额度',
  `OLD_RECORD_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '调整前备案额度|调整前备案额度',
  `DISTRIBUTE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '已分配额度|已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度',
  `LEAVE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '剩余额度|剩余额度',
  `AUTH_USER_ID` varchar(30) DEFAULT NULL COMMENT '授权柜员|授权柜员',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='年度发行计划信息历史表|大额存单年度发行计划录入时候插入';

-- ----------------------------
-- Table structure for rb_doss_batch
-- ----------------------------
DROP TABLE IF EXISTS `rb_doss_batch`;
CREATE TABLE `rb_doss_batch` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `COM_CNT_DOSS` int DEFAULT NULL COMMENT '对公转久悬笔数|对公转久悬笔数',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `AUDIT_DATE` datetime DEFAULT NULL COMMENT '审计日期|审计日期',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TOTAL_COUNT` int DEFAULT NULL COMMENT '总笔数|总笔数',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='预转久悬清单批次汇总表|预转久悬清单批次汇总';

-- ----------------------------
-- Table structure for rb_eod_file_back
-- ----------------------------
DROP TABLE IF EXISTS `rb_eod_file_back`;
CREATE TABLE `rb_eod_file_back` (
  `COUNT_EOD` int DEFAULT NULL COMMENT '日终step重跑次数|日终step重跑次数',
  `COUNT_INP` int DEFAULT NULL COMMENT '日间step重跑次数|日间step重跑次数',
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`TRAN_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='日终日间重跑次数记录表|记录日终日间step重跑次数';

-- ----------------------------
-- Table structure for rb_ex_tran_summary
-- ----------------------------
DROP TABLE IF EXISTS `rb_ex_tran_summary`;
CREATE TABLE `rb_ex_tran_summary` (
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `BUY_CCY` varchar(3) NOT NULL COMMENT '买入币种|买入币种',
  `SELL_CCY` varchar(3) NOT NULL COMMENT '卖出币种|卖出币种',
  `SELL_BUY_IND` char(1) NOT NULL COMMENT '买卖固定方|买卖固定方|B-结汇,S-售汇,E-外币兑换',
  `REVERSAL` char(1) NOT NULL COMMENT '是否冲正标志|是否冲正标志|Y-是,N-否 ',
  `RATE_TYPE` varchar(10) DEFAULT NULL COMMENT '汇率类型|汇率类型',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `AMT_BUY` decimal(17,2) DEFAULT NULL COMMENT '买入金额|银行买入币种的金额',
  `AMT_SELL` decimal(17,2) DEFAULT NULL COMMENT '卖出金额|银行卖出币种的金额',
  `CTR_TRAN` int DEFAULT NULL COMMENT '交易笔数|交易笔数',
  `UNC_CCY_RATE` decimal(15,8) DEFAULT NULL COMMENT '平盘汇率|平盘汇率',
  `UNC_LCY_AMT` decimal(17,2) DEFAULT NULL COMMENT '平盘本币金额|重算的平盘本币金额',
  `NODE_ID` varchar(50) DEFAULT NULL COMMENT '数据库节点ID|数据库节点ID',
  `STATUS` char(1) DEFAULT NULL COMMENT '状态|状态| F-失败 S - 成功 N-未处理',
  `IBUNC_REFERENCE` varchar(50) DEFAULT NULL COMMENT '系统内平盘流水号|系统内平盘流水号，对应平盘状态uncStatus=P时的主机流水号',
  `OBUNC_REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易市场平盘流水号|交易市场平盘流水号，对应平盘状态=M时的主机流水号，表示总行从系统内平盘到市场平盘平补的流水号',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  PRIMARY KEY (`SEQ_NO`),
  KEY `RB_EX_TRAN_SUMMARY_INX1M` (`TRAN_DATE`,`BRANCH`,`BUY_CCY`,`SELL_CCY`,`SELL_BUY_IND`,`REVERSAL`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='结售汇交易汇总批量表|用于汇总各分片对客结售汇交易，1.系统内平盘汇总模式平盘使用\r\n2.交易市场平盘汇总使用';

-- ----------------------------
-- Table structure for rb_fee_package
-- ----------------------------
DROP TABLE IF EXISTS `rb_fee_package`;
CREATE TABLE `rb_fee_package` (
  `PACKAGE_ID` varchar(50) NOT NULL COMMENT '套餐代码|套餐代码',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `PACKAGE_TYPE` varchar(3) NOT NULL COMMENT '套餐类型|套餐类型|NUM-笔数,AMT-金额,ALL-金额加笔数',
  `PACKAGE_DESC` varchar(50) NOT NULL COMMENT '套餐描述|套餐描述',
  `PACKAGE_MODE` varchar(3) NOT NULL COMMENT '套餐模式|套餐模式|SUB-递减,ADD-递加',
  `PACKAGE_CCY` varchar(3) NOT NULL COMMENT '套餐币种取自|套餐币种取自',
  `PACKAGE_FEE_TYPE` varchar(10) DEFAULT NULL COMMENT '套餐费费用类型|套餐费费用类型取自',
  `PACKAGE_PERIOD_FREQ` varchar(5) DEFAULT NULL COMMENT '套餐频率|套餐频率取自',
  `PACKAGE_NUM` int DEFAULT NULL COMMENT '可抵扣笔数|可抵扣笔数',
  `PACKAGE_AMT` decimal(17,2) DEFAULT NULL COMMENT '可抵扣金额|可抵扣金额',
  `PACKAGE_STATUS` char(1) NOT NULL COMMENT '套餐状态|套餐状态|A-启用,C-停用',
  `SETTLE_AMT` decimal(17,2) DEFAULT NULL COMMENT '结算金额|结算金额',
  `SETTLE_CCY` varchar(3) DEFAULT NULL COMMENT '结算币种|结算币种',
  `PROCESS_MODE` varchar(3) DEFAULT NULL COMMENT '剩余费用处理方式|剩余费用处理方式|CON-自动延续,CLE-清零',
  `PROCESS_ORDER` varchar(3) DEFAULT NULL COMMENT '费用抵扣顺序|抵扣顺序|NTA-先笔数后金额,ATN-先金额后笔数',
  `EFFECT_DATE` datetime NOT NULL COMMENT '生效日期|生效日期',
  `END_DATE` datetime NOT NULL COMMENT '结束日期|结束日期',
  `DEAL_DAY` varchar(2) DEFAULT NULL COMMENT '处理日|处理日',
  `NEXT_DEAL_DATE` datetime DEFAULT NULL COMMENT '下一处理日|下一处理日',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`PACKAGE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='费用套餐表|费用套餐签约的信息，包含套餐代码，套餐金额，日期递增递减标志，是否启用等信息';

-- ----------------------------
-- Table structure for rb_fee_package_map
-- ----------------------------
DROP TABLE IF EXISTS `rb_fee_package_map`;
CREATE TABLE `rb_fee_package_map` (
  `FEE_TYPE` varchar(20) NOT NULL COMMENT '费用类型|费率类型',
  `PACKAGE_ID` varchar(50) NOT NULL COMMENT '套餐代码|套餐代码',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`FEE_TYPE`,`PACKAGE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='费用套餐费用类型关联表|费用套餐信息，包含费用ID和费用类型，关联的表是irl_fee_type表';

-- ----------------------------
-- Table structure for rb_feedback_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_feedback_detail`;
CREATE TABLE `rb_feedback_detail` (
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_EXPIRY_DATE` datetime DEFAULT NULL COMMENT '证件失效日期|证件的到期日期',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `CARD_NO` varchar(50) DEFAULT NULL COMMENT '卡号|卡号',
  `ACCT_TYPE` char(1) DEFAULT NULL COMMENT '账户类型|账户类型|A-AIO账户,C-结算账户,S-储蓄账户,T-定期账户',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `ACCT_MEDIUM` varchar(2) DEFAULT NULL COMMENT '账户介质|账户介质|01-银行卡,02-存折,03-存单,04-手机,05-无介质,06-其他',
  `ACCT_OPEN_DATE` datetime DEFAULT NULL COMMENT '账户开户日期|账户开户日期',
  `CARD_STATUS` char(1) DEFAULT NULL COMMENT '卡状态|卡状态|A-申请,B-待发,C-活动,D-注销,E-CVN锁定,F-单日密码锁定,H-密码错误次数累计达到上限锁定,I-密码失效',
  `FEEDBACK_DATE` datetime DEFAULT NULL COMMENT '人行返回日期|人行返回日期',
  `MATURITY_DATE` datetime DEFAULT NULL COMMENT '到期日期|到期日期',
  `CARD_CLOSE_DATE` datetime DEFAULT NULL COMMENT '销卡日期|销卡日期',
  `CLOSED_DATE` datetime DEFAULT NULL COMMENT '关闭日期|关闭日期',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `BIND_BRANCH` varchar(50) DEFAULT NULL COMMENT '绑定I类账户开户银行金融机构编码|绑定I类账户开户银行金融机构编码',
  `ACCOUNT_OPEN_CHANNEL` varchar(2) DEFAULT NULL COMMENT '开户渠道|账户开户渠道  账户上报信息|JZ-集中作业,MT-柜面,ZD-智能网点自助交易平台,VT-远程智能自助银行,QT-XBankT10,QE-快窗智能高柜,CP-手机银行,EB-个人网上银行,CB-企业网上银行,CM-现金管理,WB-微信银行',
  `INFORMATION_TYPE` char(1) DEFAULT NULL COMMENT '信息类型|信息类型|O-开户,A-变更,C-销户',
  `ERROR_TYPE` varchar(10) DEFAULT NULL COMMENT '错误类型|错误类型',
  `BIND_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '绑定I类账户账号|绑定I类账户账号',
  `FILE_NAME` varchar(200) DEFAULT NULL COMMENT '文件名称|文件名称',
  `DIST_CODE` varchar(10) DEFAULT NULL COMMENT '地区代码|地区代码',
  `MAJOR_CATEGORY` varchar(2) DEFAULT NULL COMMENT '存款人类别|存款人类别|01-中国居民,02-军人,03-武警,04-香港、澳门、台湾地区居民,05-外国公民,06-定居国外的中国公民',
  `COUNTRY_CITIZEN` varchar(3) DEFAULT NULL COMMENT '居住国家|居住国家',
  `SEX` char(1) DEFAULT NULL COMMENT '性别|性别|M-男,F-女',
  `POSTAL_CODE` varchar(10) DEFAULT NULL COMMENT '邮政编码|邮政编码',
  `ADDRESS` varchar(500) DEFAULT NULL COMMENT '地址|地址',
  `CONTACT_TEL` varchar(50) DEFAULT NULL COMMENT '联系电话  |联系电话',
  `COMMISSION_COUNTRY` varchar(3) DEFAULT NULL COMMENT '代办/代理人国籍|代理人国籍',
  `COMMISSION_CLIENT_TEL` varchar(20) DEFAULT NULL COMMENT '代办/代理人电话|代理人电话',
  `MILITARY_GUARANTEE_CARD` char(1) DEFAULT NULL COMMENT '是否为军人保障卡|是否为军人保障卡|Y-是,N-否',
  `SOCIAL_SECURITY_CARD` varchar(2) DEFAULT NULL COMMENT '是否为社会保障卡|是否为社会保障卡|01-是,02-不是',
  `CHECK_RESULT` char(1) DEFAULT NULL COMMENT '结果|结果|S-成功,F-失败',
  `NO_CHECK_REASON` varchar(2) DEFAULT NULL COMMENT '未检查原因|未检查原因|01-无法联系存款人,02-存款人提供证明文件有疑义待进一步核实,03- 存款人在规定时间内无法提供相关证明,04-存款人拒绝提供证明',
  `DISPOSAL_METHOD` varchar(30) DEFAULT NULL COMMENT '处置方法|处置方法|01-未作处理,02-报送当地人民银行分支机构,03-报送反洗钱监测中心,04-报送当地公安机关,05-中止交易,06-关闭网银,07-关闭手机电话银行,08-关闭ATM取现,09-关闭ATM转账,10-其他',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `RESERVE1` varchar(50) DEFAULT NULL COMMENT '预留字段1|预留字段1',
  `RESERVE2` varchar(50) DEFAULT NULL COMMENT '预留字段2|预留字段2',
  `RESERVE3` varchar(50) DEFAULT NULL COMMENT '预留字段3|预留字段3',
  `RESERVE4` varchar(50) DEFAULT NULL COMMENT '预留字段4|预留字段4',
  `RESERVE5` varchar(50) DEFAULT NULL COMMENT '预留字段5|预留字段5',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMMISSION_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '代办人名称|代办人名称',
  `COMMISSION_DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '代办人证件类型|代办人证件类型',
  `COMMISSION_DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '代办人证件号码|代办人证件号码',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  PRIMARY KEY (`SEQ_NO`),
  KEY `IDX_RB_FEEDBACK_DETAIL_1M` (`FEEDBACK_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='账户上报错误信息表|记录账户上报错误信息';

-- ----------------------------
-- Table structure for rb_gl_acct_manual_batch_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_gl_acct_manual_batch_detail`;
CREATE TABLE `rb_gl_acct_manual_batch_detail` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_SEQ_NO` varchar(50) NOT NULL COMMENT '批次明细序号|批次明细序号',
  `BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `MANUAL_ACCOUNT_FLAG` char(1) DEFAULT NULL COMMENT '是否允许手工记账|是否允许手工记账|Y-是 ,N-否',
  `FACILITY_ERROR_CODE` varchar(10) DEFAULT NULL COMMENT '是否允许透支错误码|是否允许透支错误码',
  `OD_FACILITY` char(1) DEFAULT NULL COMMENT '是否可透支|是否可透支|Y-是,N-否',
  `GL_CODE` varchar(20) DEFAULT NULL COMMENT '科目代码|科目代码',
  `FACILITY_ERROR_DESC` varchar(50) DEFAULT NULL COMMENT '是否允许透支错误描述|是否允许透支错误描述',
  `MANUAL_ERROR_CODE` varchar(10) DEFAULT NULL COMMENT '手工记账错误码|是否允许手工记账错误码',
  `MANUAL_ERROR_DESC` varchar(50) DEFAULT NULL COMMENT '手工记账错误描述|是否允许手工记账错误描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`,`BATCH_SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='允许手工记账批处理信息|允许手工记账记账信息登记，包含批次号，交易流水号，科目，是否允许手工记账等';

-- ----------------------------
-- Table structure for rb_gl_batch_tran_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_gl_batch_tran_detail`;
CREATE TABLE `rb_gl_batch_tran_detail` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `PROD_DESC` varchar(200) DEFAULT NULL COMMENT '产品描述|解释产品具体特性',
  `GL_CODE` varchar(20) DEFAULT NULL COMMENT '科目代码|科目代码',
  `SUBJECT_DESC` varchar(200) DEFAULT NULL COMMENT '科目描述|科目描述',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `ACCT_OPEN_DATE` datetime DEFAULT NULL COMMENT '账户开户日期|账户开户日期',
  `BAL_UPD_TYPE` char(1) DEFAULT NULL COMMENT '余额更新类型|余额更新类型|R-实时更新,B-批量更新',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `COUNTER_DEBT_FLAG` char(1) DEFAULT NULL COMMENT '是否允许柜面跨行支取许可标识|是否允许柜面跨行支取许可标识|Y-允许,N-不允许',
  `COUNTER_DEP_FLAG` char(1) DEFAULT NULL COMMENT '是否允许柜面跨行存入许可标识|是否允许柜面跨行存入许可标识|Y-允许,N-不允许',
  `HANG_TERM` varchar(5) DEFAULT NULL COMMENT '挂账期限|挂账期限',
  `HANG_WRITE_OFF_FLAG` char(1) DEFAULT NULL COMMENT '挂销账标志|挂销账标志|H-挂帐,C-销账',
  `LINK_VALUE` bigint DEFAULT NULL COMMENT '关联键值|关联键值',
  `OD_FACILITY` char(1) DEFAULT NULL COMMENT '是否可透支|是否可透支|Y-是,N-否',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='内部账户批量开立详细信息表|批量续开内部户插入明细数据';

-- ----------------------------
-- Table structure for rb_judicature_file_query
-- ----------------------------
DROP TABLE IF EXISTS `rb_judicature_file_query`;
CREATE TABLE `rb_judicature_file_query` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `QUERY_TYPE` varchar(10) DEFAULT NULL COMMENT '查询类型|查询类型',
  `QUERY_CONDITION` varchar(50) DEFAULT NULL COMMENT '查询条件|查询条件',
  `QUERY_OPTION` varchar(10) DEFAULT NULL COMMENT '查询选项|查询选项',
  `START_DATE` datetime DEFAULT NULL COMMENT '开始日期|开始日期',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|结束日期',
  `FILE_NAME` varchar(200) DEFAULT NULL COMMENT '文件名称|文件名称',
  `TRAN_FILE_RESULT` char(1) DEFAULT NULL COMMENT '交易返回结果|交易返回结果|N-未生成,Y-已生成',
  `FILE_ERROR_MSG` varchar(200) DEFAULT NULL COMMENT '文件未生成错误描述|文件未生成错误描述',
  `SUB_SEQ_NO` varchar(100) DEFAULT NULL COMMENT '子流水号|子流水号',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='有权机关查询申请文件表|记录和查询有权机关申请文件';

-- ----------------------------
-- Table structure for rb_judiciary_assistant_info
-- ----------------------------
DROP TABLE IF EXISTS `rb_judiciary_assistant_info`;
CREATE TABLE `rb_judiciary_assistant_info` (
  `AS_OPTION` char(1) DEFAULT NULL COMMENT '司法查询操作标识|司法查询操作标识|A-有权机关查询申请,B-大数据平台查询',
  `LAW_NO` varchar(200) DEFAULT NULL COMMENT '法律文书号|法律文书号',
  `LAW_NO_NAME` varchar(50) DEFAULT NULL COMMENT '法律文书名称|法律文书名称',
  `DEDUCTION_JUDICIARY_NAME` varchar(200) DEFAULT NULL COMMENT '有权机关名称|有权机关名称',
  `ASSISTANT_CONTENT` varchar(50) DEFAULT NULL COMMENT '协助备注|协助备注',
  `ASSISTANT_NARRIVE` varchar(200) DEFAULT NULL COMMENT '协助内容|协助内容',
  `REFERENCE` varchar(50) NOT NULL COMMENT '交易参考号|交易参考号',
  `JUDICIARY_OFFICER_NAME` varchar(200) DEFAULT NULL COMMENT '执法人1姓名|执法人1姓名',
  `JUDICIARY_DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '执法人1证件类型|执法人1证件类型',
  `JUDICIARY_DOCUMENT_TYPE2` varchar(3) DEFAULT NULL COMMENT '执法人1证件类型2|执法人1证件类型2',
  `JUDICIARY_DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '执法人1证件号码|执法人1证件号码',
  `JUDICIARY_DOCUMENT_ID2` varchar(50) DEFAULT NULL COMMENT '执法人1证件号码2|执法人1证件号码2',
  `JUDICIARY_OTH_OFFICER_NAME` varchar(200) DEFAULT NULL COMMENT '执法人2姓名|执法人2姓名',
  `JUDICIARY_OTH_DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '执法人2证件类型|执法人2证件类型',
  `JUDICIARY_OTH_DOCUMENT_TYPE2` varchar(3) DEFAULT NULL COMMENT '执法人2证件类型2|执法人2证件类型2',
  `JUDICIARY_OTH_DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '执法人2证件号码|执法人2证件号码',
  `JUDICIARY_OTH_DOCUMENT_ID2` varchar(50) DEFAULT NULL COMMENT '执法人2证件号码2|执法人2证件号码2',
  `AUTH_USER_ID` varchar(30) DEFAULT NULL COMMENT '授权柜员|授权柜员',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`REFERENCE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='司法协助信息登记簿|记录司法协助登记信息';

-- ----------------------------
-- Table structure for rb_ledge_off_dtl
-- ----------------------------
DROP TABLE IF EXISTS `rb_ledge_off_dtl`;
CREATE TABLE `rb_ledge_off_dtl` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `RES_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '限制编号|限制编号',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `CONTRACT_NO` varchar(50) DEFAULT NULL COMMENT '合同号|合同号',
  `BATCH_FILE_STATUS` char(1) DEFAULT NULL COMMENT '批处理文件处理状态|批处理文件处理状态|N-新建,S-成功,F-失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `CREATE_DATE` datetime DEFAULT NULL COMMENT '创建日期|创建日期',
  `RUN_DATE` datetime DEFAULT NULL COMMENT '运行日期|交易日期',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `LAST_CHANGE_DATE` datetime DEFAULT NULL COMMENT '最后修改日期|最后修改日期',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `CMISLOAN_NO` varchar(50) DEFAULT NULL COMMENT '借据号|借据号',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`),
  KEY `RB_LEDGE_OFF_DTL_INDEX2M` (`CMISLOAN_NO`),
  KEY `IDX_RB_LEDGE_OFF_DTL_1M` (`CONTRACT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='存单质押限制解除登记表|解除存单质押限制';

-- ----------------------------
-- Table structure for rb_notran_list
-- ----------------------------
DROP TABLE IF EXISTS `rb_notran_list`;
CREATE TABLE `rb_notran_list` (
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `EXAMINE_FLAG` char(1) DEFAULT NULL COMMENT '可疑账户核实标志|可疑账户核实标志|1-待核实,2-已核实,3-核实未通过',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `ASYN_DATE` datetime DEFAULT NULL COMMENT '同步日期|同步日期',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `INPUT_BRANCH` varchar(50) DEFAULT NULL COMMENT '录入机构|录入机构',
  `BLACK_NO` varchar(50) DEFAULT NULL COMMENT '黑名单编号|黑名单编号',
  `BLACK_CHECK_TIME` varchar(26) DEFAULT NULL COMMENT '黑名单检查时间|检查时间',
  `ASYN_ID` varchar(50) DEFAULT NULL COMMENT '异步编号|异步编号',
  `OUR_BANK_FLAG` char(1) DEFAULT NULL COMMENT '是否我行黑名单|是否我行黑名单|Y-是,N-否',
  `ADD_FLAG` char(1) DEFAULT NULL COMMENT '添加标识|添加标识 黑名单推送外围标志|1-增加,2-减少',
  `LIST_OPERATE_TYPE` varchar(5) DEFAULT NULL COMMENT '名单操作类型|名单操作类型',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `EXAMINE_TELLER` varchar(10) DEFAULT NULL COMMENT '检查柜面|检查柜面',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `LIST_SOURCE` char(1) DEFAULT NULL COMMENT '名单来源|名单来源|H-核心,C-信用卡,Z-直销银行',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `REMARK3` varchar(200) DEFAULT NULL COMMENT '备注3|备注3',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `UNCOUNTER_DESC` varchar(50) DEFAULT NULL COMMENT '入表原因|入表原因',
  `OPER_USER_ID` varchar(30) DEFAULT NULL COMMENT '操作柜员|操作柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='6个月无交易名单表|查询6个月无交易名单';

-- ----------------------------
-- Table structure for rb_od_branch_info
-- ----------------------------
DROP TABLE IF EXISTS `rb_od_branch_info`;
CREATE TABLE `rb_od_branch_info` (
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `TOTAL_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '总额度|总额度',
  `USED_AMT` decimal(17,2) DEFAULT NULL COMMENT '已使用额度|已使用额度',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构法人透支额度信息表|机构法人透支额度信息表';

-- ----------------------------
-- Table structure for rb_parameter
-- ----------------------------
DROP TABLE IF EXISTS `rb_parameter`;
CREATE TABLE `rb_parameter` (
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `PARA_DESC` varchar(200) DEFAULT NULL COMMENT '参数描述|参数描述',
  `PARA_KEY` varchar(50) NOT NULL COMMENT '参数名|参数名',
  `PARA_VALUE` varchar(200) DEFAULT NULL COMMENT '参数值|参数值',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PARA_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='存款参数表';

-- ----------------------------
-- Table structure for rb_pcp_batch_tran_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_pcp_batch_tran_detail`;
CREATE TABLE `rb_pcp_batch_tran_detail` (
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|所属机构号',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `UP_DOWN_TYPE` varchar(10) DEFAULT NULL COMMENT '账户升降级表示符|账户升降级表示符|UP-升级,DOWN-降级 ',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `SUB_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户组子账户币种|账户组子账户币种',
  `SUB_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '子账户序号|子账户序号',
  `SUB_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账户组子账户账号|账户组子账户账号',
  `SUB_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '子产品类型|子产品类型',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='资金池联机批量交易登记表';

-- ----------------------------
-- Table structure for rb_prod_acct_bal
-- ----------------------------
DROP TABLE IF EXISTS `rb_prod_acct_bal`;
CREATE TABLE `rb_prod_acct_bal` (
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `BALANCE` decimal(17,2) NOT NULL COMMENT '余额|余额',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `LAST_CHANGE_DATE` datetime NOT NULL COMMENT '最后修改日期|最后修改日期',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `AMT_TYPE` varchar(10) NOT NULL COMMENT '金额类型|金额类型|BAL-余额,DDA-发放金额,INTP-逾期利息,LIM-额度金额,OD-透支金额,ODIP-逾期复利,ODPP-逾期罚息,OSL-未到期本金,PRD-逾期本金,PRI-本金',
  `GL_CODE` varchar(20) DEFAULT NULL COMMENT '科目代码|科目代码',
  `NODE_ID` varchar(50) NOT NULL COMMENT '数据库节点ID|数据库节点ID',
  `ACCOUNTING_STATUS` varchar(3) NOT NULL COMMENT '核算状态|核算状态，为贷款核算状态类型，会计部门根据借款凭证针对借款合同进行审核的贷款核算分级审批制度|ZHC-正常,YUQ_逾期,FYJ-非应计,FY-手工转非应计,WRN-核销,TER-终止',
  `SYSTEM_ID` varchar(20) DEFAULT NULL COMMENT '系统ID|系统ID',
  `PROFIT_CENTER` varchar(20) NOT NULL COMMENT '利润中心 |利润中心',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `DEAL_FLAG` char(1) DEFAULT NULL COMMENT '处理标识|处理标识|1-未处理  ,2-已处理 ',
  `TRAN_CATEGORY` varchar(5) DEFAULT NULL COMMENT '交易种类|交易种类|AC-摊余成本计量, FVTPL-公允价值变动计入当期损益, OCI-公允价值变动计入其他综合收益',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `INDUSTRY` varchar(20) DEFAULT NULL COMMENT '通用行业代码|通用行业代码',
  `TERM` varchar(5) DEFAULT NULL COMMENT '存期期限|期限',
  PRIMARY KEY (`PROD_TYPE`,`BRANCH`,`LAST_CHANGE_DATE`,`CCY`,`AMT_TYPE`,`NODE_ID`,`ACCOUNTING_STATUS`,`PROFIT_CENTER`,`COMPANY`),
  KEY `RB_PROD_ACCT_BAL_INDM` (`LAST_CHANGE_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品分户余额表|记录分录四要素及分户余额信息';

-- ----------------------------
-- Table structure for rb_prod_check_result
-- ----------------------------
DROP TABLE IF EXISTS `rb_prod_check_result`;
CREATE TABLE `rb_prod_check_result` (
  `CHECK_DATE` datetime NOT NULL COMMENT '检查日期|检查日期',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `ACCOUNTING_STATUS` varchar(3) NOT NULL COMMENT '核算状态|核算状态，为贷款核算状态类型，会计部门根据借款凭证针对借款合同进行审核的贷款核算分级审批制度|ZHC-正常,YUQ-逾期,FYJ-非应计,FY-手工转非应计,DZA-呆账,DZI-呆滞,WRN-核销,TER-终止',
  `GL_CODE` varchar(20) DEFAULT NULL COMMENT '科目代码|科目代码',
  `AMT_TYPE` varchar(10) NOT NULL COMMENT '金额类型|金额类型|BAL-余额,DDA-发放金额,INTP-逾期利息,LIM-额度金额,OD-透支金额,ODIP-逾期复利,ODPP-逾期罚息,OSL-未到期本金,PRD-逾期本金,PRI-本金,PF-净本金,INT-利息,ODI-复利,ODP-罚息,FEE-费用,UNI-非本金,ALL-本加息,DS-前收息金额,PRF-提前结清手续费,ODODP-罚息的复利,ODODI-复利的复利',
  `PROD_BALANCE` decimal(17,2) DEFAULT NULL COMMENT '产品余额|产品余额',
  `ACCT_BALANCE` decimal(17,2) DEFAULT NULL COMMENT '账户余额|账户余额',
  `DIFF_BALANCE` decimal(17,2) DEFAULT NULL COMMENT '余额的差额|余额的差额',
  `CHECK_RESULT` char(1) DEFAULT NULL COMMENT '结果|结果|S-成功,F-失败',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`CHECK_DATE`,`BRANCH`,`CCY`,`PROD_TYPE`,`ACCOUNTING_STATUS`,`AMT_TYPE`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='总分核对结果表|总分核对结果表';

-- ----------------------------
-- Table structure for rb_res_no_def
-- ----------------------------
DROP TABLE IF EXISTS `rb_res_no_def`;
CREATE TABLE `rb_res_no_def` (
  `RES_LETTER` char(1) DEFAULT NULL COMMENT '限制编号当前字母|限制编号当前字母'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='冻结编号字母定义表';

-- ----------------------------
-- Table structure for rb_riskacct_list
-- ----------------------------
DROP TABLE IF EXISTS `rb_riskacct_list`;
CREATE TABLE `rb_riskacct_list` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BLACK_NO` varchar(50) NOT NULL COMMENT '黑名单编号|黑名单编号',
  `ASYN_ID` varchar(50) DEFAULT NULL COMMENT '异步编号|异步编号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `EXAMINE_FLAG` char(1) DEFAULT NULL COMMENT '可疑账户核实标志|可疑账户核实标志|1-待核实,2-已核实,3-核实未通过',
  `OUR_BANK_FLAG` char(1) DEFAULT NULL COMMENT '是否我行黑名单|是否我行黑名单|Y-是,N-否',
  `LIST_OPERATE_TYPE` varchar(5) DEFAULT NULL COMMENT '名单操作类型|名单操作类型',
  `BLACK_DESC` varchar(50) DEFAULT NULL COMMENT '黑名单描述|黑名单描述',
  `EXAMINE_TELLER` varchar(10) DEFAULT NULL COMMENT '检查柜面|检查柜面',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `UNCOUNTER_DESC` varchar(50) DEFAULT NULL COMMENT '入表原因|入表原因',
  `BLACK_CHECK_TIME` varchar(26) DEFAULT NULL COMMENT '黑名单检查时间|检查时间',
  `LIST_SOURCE` char(1) DEFAULT NULL COMMENT '名单来源|名单来源|H-核心,C-信用卡,Z-直销银行',
  `ASYN_DATE` datetime DEFAULT NULL COMMENT '同步日期|同步日期',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `INPUT_BRANCH` varchar(50) DEFAULT NULL COMMENT '录入机构|录入机构',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `REMARK2` varchar(200) DEFAULT NULL COMMENT '备注2|备注2',
  `REMARK3` varchar(200) DEFAULT NULL COMMENT '备注3|备注3',
  `OPER_USER_ID` varchar(30) DEFAULT NULL COMMENT '操作柜员|操作柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`,`BLACK_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='可疑账户名单表|即可账户可疑风险信息';

-- ----------------------------
-- Table structure for rb_run_date_notice
-- ----------------------------
DROP TABLE IF EXISTS `rb_run_date_notice`;
CREATE TABLE `rb_run_date_notice` (
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `SWITCH_YN` char(1) DEFAULT NULL COMMENT '开关|开关|Y-开,N-关 ',
  `NEXT_RUN_DATE` datetime DEFAULT NULL COMMENT '下一运行日|下一运行日',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `EXTRACT_DATA_STATUS_FLAG` char(1) DEFAULT NULL COMMENT '可抽数状态 Y-可抽数 | N-不可抽数|可抽数状态 Y-可抽数 | N-不可抽数',
  PRIMARY KEY (`TRAN_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='营业日期通知表|用于核心与外部系统间营业日期相关通知记录';

-- ----------------------------
-- Table structure for rb_safe_list
-- ----------------------------
DROP TABLE IF EXISTS `rb_safe_list`;
CREATE TABLE `rb_safe_list` (
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `EXAMINE_FLAG` char(1) DEFAULT NULL COMMENT '可疑账户核实标志|可疑账户核实标志|1-待核实,2-已核实,3-核实未通过',
  `EXAMINE_TELLER` varchar(10) DEFAULT NULL COMMENT '检查柜面|检查柜面',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `ASYN_DATE` datetime DEFAULT NULL COMMENT '同步日期|同步日期',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `BLACK_NO` varchar(50) DEFAULT NULL COMMENT '黑名单编号|黑名单编号',
  `BLACK_CHECK_TIME` varchar(26) DEFAULT NULL COMMENT '黑名单检查时间|检查时间',
  `ASYN_ID` varchar(50) DEFAULT NULL COMMENT '异步编号|异步编号',
  `OUR_BANK_FLAG` char(1) DEFAULT NULL COMMENT '是否我行黑名单|是否我行黑名单|Y-是,N-否',
  `LIST_OPERATE_TYPE` varchar(5) DEFAULT NULL COMMENT '名单操作类型|名单操作类型',
  `BLACK_DESC` varchar(50) DEFAULT NULL COMMENT '黑名单描述|黑名单描述',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `LIST_SOURCE` char(1) DEFAULT NULL COMMENT '名单来源|名单来源|H-核心,C-信用卡,Z-直销银行',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `REMARK2` varchar(200) DEFAULT NULL COMMENT '备注2|备注2',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `UNCOUNTER_DESC` varchar(50) DEFAULT NULL COMMENT '入表原因|入表原因',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='外管局下发境外取现超限额名单|记录外管局下发境外取现超限额名单';

-- ----------------------------
-- Table structure for rb_sameteleno_list
-- ----------------------------
DROP TABLE IF EXISTS `rb_sameteleno_list`;
CREATE TABLE `rb_sameteleno_list` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `PHONE` varchar(20) DEFAULT NULL COMMENT '手机号|手机号',
  `EXAMINE_TELLER` varchar(10) DEFAULT NULL COMMENT '检查柜面|检查柜面',
  `EXAMINE_FLAG` char(1) DEFAULT NULL COMMENT '可疑账户核实标志|可疑账户核实标志|1-待核实,2-已核实,3-核实未通过',
  `UNCOUNTER_DESC` varchar(50) DEFAULT NULL COMMENT '入表原因|入表原因',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `INPUT_BRANCH` varchar(50) DEFAULT NULL COMMENT '录入机构|录入机构',
  `OUR_BANK_FLAG` char(1) DEFAULT NULL COMMENT '是否我行黑名单|是否我行黑名单|Y-是,N-否',
  `LIST_SOURCE` char(1) DEFAULT NULL COMMENT '名单来源|名单来源|H-核心,C-信用卡,Z-直销银行',
  `LIST_OPERATE_TYPE` varchar(5) DEFAULT NULL COMMENT '名单操作类型|名单操作类型',
  `BLACK_CHECK_TIME` varchar(26) DEFAULT NULL COMMENT '黑名单检查时间|检查时间',
  `BLACK_NO` varchar(50) DEFAULT NULL COMMENT '黑名单编号|黑名单编号',
  `BLACK_DESC` varchar(50) DEFAULT NULL COMMENT '黑名单描述|黑名单描述',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `ASYN_DATE` datetime DEFAULT NULL COMMENT '同步日期|同步日期',
  `ASYN_ID` varchar(50) DEFAULT NULL COMMENT '异步编号|异步编号',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `REMARK2` varchar(200) DEFAULT NULL COMMENT '备注2|备注2',
  `OPER_USER_ID` varchar(30) DEFAULT NULL COMMENT '操作柜员|操作柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='外管局下发境外取现超限额名单|记录手机号重复名单';

-- ----------------------------
-- Table structure for rb_self_check
-- ----------------------------
DROP TABLE IF EXISTS `rb_self_check`;
CREATE TABLE `rb_self_check` (
  `CHECK_DATE` datetime NOT NULL COMMENT '检查日期|检查日期',
  `TOTAL_CHECK_RESULT` char(1) NOT NULL COMMENT '总分核对结果|总分核对结果|S-平,F-不平',
  `DETAIL_CHECK_RESULT` char(1) NOT NULL COMMENT '分分核对结果|分分核对结果|S-平,F-不平',
  `DETAIL_CHECK_COUNT` int NOT NULL COMMENT '分分核对次数|分分核对次数',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `TOTAL_CHECK_COUNT` int NOT NULL COMMENT '总分核对次数|总分核对次数',
  PRIMARY KEY (`CHECK_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='核心自检登记簿|核心自检登记簿';

-- ----------------------------
-- Table structure for rb_stage_branch_device_limit
-- ----------------------------
DROP TABLE IF EXISTS `rb_stage_branch_device_limit`;
CREATE TABLE `rb_stage_branch_device_limit` (
  `ISSUE_YEAR` varchar(5) NOT NULL COMMENT '发行年度|发行年度',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `SOURCE_TYPE` varchar(10) NOT NULL COMMENT '渠道类型|渠道类型',
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `TOTAL_LIMIT` decimal(17,2) NOT NULL COMMENT '总额度|总额度',
  `HOLDING_LIMIT` decimal(17,2) NOT NULL COMMENT '已占用额度|已占用额度',
  `DISTRIBUTE_LIMIT` decimal(17,2) NOT NULL COMMENT '已分配额度|已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度',
  `LEAVE_LIMIT` decimal(17,2) NOT NULL COMMENT '剩余额度|剩余额度',
  `BACK_STATUS` char(1) DEFAULT NULL COMMENT '额度回收状态|额度回收状态|N-未恢复,S-成功,F-失败',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `DEVICE` varchar(30) DEFAULT NULL COMMENT '设备|设备',
  PRIMARY KEY (`BRANCH`,`STAGE_CODE`),
  KEY `RB_STAGE_BRANCH_DEVICE_LIMITM` (`DEVICE`,`BRANCH`,`STAGE_CODE`),
  KEY `IDX_RB_STAGE_BRANCH_LIMIT_1M` (`STAGE_CODE`,`BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品期次机构设备额度|记录产品期次机构设备额度';

-- ----------------------------
-- Table structure for rb_stage_branch_limit
-- ----------------------------
DROP TABLE IF EXISTS `rb_stage_branch_limit`;
CREATE TABLE `rb_stage_branch_limit` (
  `ISSUE_YEAR` varchar(5) NOT NULL COMMENT '发行年度|发行年度',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `SALE_TYPE` char(1) DEFAULT NULL COMMENT '销售方式|销售方式|C-竞售,P-配额',
  `ATTACHED_TO` varchar(50) DEFAULT NULL COMMENT '所属上级|用于配置区/县对应上级市',
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `TOTAL_LIMIT` decimal(17,2) NOT NULL COMMENT '总额度|总额度',
  `HOLDING_LIMIT` decimal(17,2) NOT NULL COMMENT '已占用额度|已占用额度',
  `LEAVE_LIMIT` decimal(17,2) NOT NULL COMMENT '剩余额度|剩余额度',
  `DISTRIBUTE_LIMIT` decimal(17,2) NOT NULL COMMENT '已分配额度|已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度',
  `BACK_STATUS` char(1) DEFAULT NULL COMMENT '额度回收状态|额度回收状态|N-未恢复,S-成功,F-失败',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BRANCH`,`STAGE_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='期次机构额度表|记录期次机构额度信息';

-- ----------------------------
-- Table structure for rb_stage_class_limit
-- ----------------------------
DROP TABLE IF EXISTS `rb_stage_class_limit`;
CREATE TABLE `rb_stage_class_limit` (
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `ISSUE_YEAR` varchar(5) NOT NULL COMMENT '发行年度|发行年度',
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `LIMIT_CLASS` varchar(20) NOT NULL COMMENT '额度类别|额度类别|PRE-预约额度,ELE-电子渠道额度',
  `TOTAL_LIMIT` decimal(17,2) NOT NULL COMMENT '总额度|总额度',
  `LEAVE_LIMIT` decimal(17,2) NOT NULL COMMENT '剩余额度|剩余额度',
  `HOLDING_LIMIT` decimal(17,2) NOT NULL COMMENT '已占用额度|已占用额度',
  `DISTRIBUTE_LIMIT` decimal(17,2) NOT NULL COMMENT '已分配额度|已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度',
  `BACK_STATUS` char(1) DEFAULT NULL COMMENT '额度回收状态|额度回收状态|N-未恢复,S-成功,F-失败',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`STAGE_CODE`,`LIMIT_CLASS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='渠道/预约额度表|记录渠道/预约额度信息';

-- ----------------------------
-- Table structure for rb_stage_device_limit
-- ----------------------------
DROP TABLE IF EXISTS `rb_stage_device_limit`;
CREATE TABLE `rb_stage_device_limit` (
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `STAGE_CODE` varchar(50) NOT NULL COMMENT '期次代码|期次代码',
  `ISSUE_YEAR` varchar(5) NOT NULL COMMENT '发行年度|发行年度',
  `SOURCE_TYPE` varchar(10) NOT NULL COMMENT '渠道类型|渠道类型',
  `TOTAL_LIMIT` decimal(17,2) NOT NULL COMMENT '总额度|总额度',
  `HOLDING_LIMIT` decimal(17,2) NOT NULL COMMENT '已占用额度|已占用额度',
  `DISTRIBUTE_LIMIT` decimal(17,2) NOT NULL COMMENT '已分配额度|已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度',
  `LEAVE_LIMIT` decimal(17,2) NOT NULL COMMENT '剩余额度|剩余额度',
  `BACK_STATUS` char(1) DEFAULT NULL COMMENT '额度回收状态|额度回收状态|N-未恢复,S-成功,F-失败',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`STAGE_CODE`,`SOURCE_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='柜面/设备额度表|记录柜面/设备额度信息';

-- ----------------------------
-- Table structure for rb_step_error_log
-- ----------------------------
DROP TABLE IF EXISTS `rb_step_error_log`;
CREATE TABLE `rb_step_error_log` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `INTERNAL_KEY` bigint DEFAULT NULL COMMENT '账户内部键值|账户内部键值',
  `TABLE_ID` varchar(50) DEFAULT NULL COMMENT '表ID|表ID',
  `STEP_ID` varchar(50) DEFAULT NULL COMMENT 'step编号|step编号',
  `EXE_ID` varchar(50) DEFAULT NULL COMMENT '执行ID|执行ID',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_MSG` varchar(2000) DEFAULT NULL COMMENT '错误代码|错误代码',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批处理错误日志表|记录批处理错误日信息';

-- ----------------------------
-- Table structure for rb_step_error_mapping
-- ----------------------------
DROP TABLE IF EXISTS `rb_step_error_mapping`;
CREATE TABLE `rb_step_error_mapping` (
  `STEP_NAME` varchar(50) NOT NULL COMMENT 'step名称|step名称',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `IS_THROW` char(1) DEFAULT NULL COMMENT '是否遗弃|是否遗弃|Y-是,N-否',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`STEP_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='STEP错误映射表|记录STEP错误映射表';

-- ----------------------------
-- Table structure for rb_tae_account_check_detail
-- ----------------------------
DROP TABLE IF EXISTS `rb_tae_account_check_detail`;
CREATE TABLE `rb_tae_account_check_detail` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `TAE_SUB_SEQ_NO` varchar(200) DEFAULT NULL COMMENT 'TAE子流水序号|用于登记tae子流水号的序号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `CARD_NO` varchar(50) DEFAULT NULL COMMENT '卡号|卡号',
  `AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '金额|金额',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `INTERNAL_KEY` bigint DEFAULT NULL COMMENT '账户内部键值|账户内部键值',
  `SESSION_ID` varchar(20) DEFAULT NULL COMMENT '场次|场次',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `MODULE_ID` varchar(2) DEFAULT NULL COMMENT '模块|模块|RB-存款 ,CL-贷款 ,GL-总账',
  `DR_CR_FLAG` char(1) DEFAULT NULL COMMENT '借贷方向|借贷方向|D-借,C-贷',
  `REVERSAL` char(1) DEFAULT NULL COMMENT '是否冲正标志|是否冲正标志|Y-是,N-否 ',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SEQ_NO`),
  KEY `idx_rb_tae_accout_check_detail1` (`CHANNEL_SEQ_NO`,`TAE_SUB_SEQ_NO`,`SEQ_NO`,`SESSION_ID`),
  KEY `idx_rb_tae_accout_check_detail2` (`TRAN_DATE`,`BASE_ACCT_NO`,`INTERNAL_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='日间对账明细结果表|日间对账明细结果表';

-- ----------------------------
-- Table structure for rb_tae_account_check_hist
-- ----------------------------
DROP TABLE IF EXISTS `rb_tae_account_check_hist`;
CREATE TABLE `rb_tae_account_check_hist` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `TAE_SUB_SEQ_NO` varchar(200) DEFAULT NULL COMMENT 'TAE子流水序号|用于登记tae子流水号的序号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '金额|金额',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `INTERNAL_KEY` bigint DEFAULT NULL COMMENT '账户内部键值|账户内部键值',
  `SESSION_ID` varchar(20) DEFAULT NULL COMMENT '场次|场次',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `MODULE_ID` varchar(2) DEFAULT NULL COMMENT '模块|模块|RB-存款 ,CL-贷款 ,GL-总账',
  `DR_CR_FLAG` char(1) DEFAULT NULL COMMENT '借贷方向|借贷方向|D-借,C-贷',
  `REVERSAL` char(1) DEFAULT NULL COMMENT '是否冲正标志|是否冲正标志|Y-是,N-否 ',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='日间TAE对账明细历史表|日间对账明细表及历史表';

-- ----------------------------
-- Table structure for rb_tae_account_check_sum
-- ----------------------------
DROP TABLE IF EXISTS `rb_tae_account_check_sum`;
CREATE TABLE `rb_tae_account_check_sum` (
  `SESSION_ID` varchar(20) NOT NULL COMMENT '场次|场次',
  `CRET_TOTAL_NUM` int DEFAULT NULL COMMENT '贷方总数量|贷方总数量',
  `CRET_TOTAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '贷方总金额|贷方总金额',
  `DEBT_TOTAL_NUM` int DEFAULT NULL COMMENT '借方总数量|借方总数量',
  `DEBT_TOTAL_AMT` decimal(17,2) DEFAULT NULL COMMENT '借方总金额|借方总金额',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SESSION_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='日间对账汇总表|记录日间对账汇总信息';

-- ----------------------------
-- Table structure for rb_telecom_restraints_list
-- ----------------------------
DROP TABLE IF EXISTS `rb_telecom_restraints_list`;
CREATE TABLE `rb_telecom_restraints_list` (
  `BLACK_NO` varchar(50) NOT NULL COMMENT '黑名单编号|黑名单编号',
  `LIST_OPERATE_TYPE` varchar(5) DEFAULT NULL COMMENT '名单操作类型|名单操作类型',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `EXAMINE_FLAG` char(1) DEFAULT NULL COMMENT '可疑账户核实标志|可疑账户核实标志|1-待核实,2-已核实,3-核实未通过',
  `OUR_BANK_FLAG` char(1) DEFAULT NULL COMMENT '是否我行黑名单|是否我行黑名单|Y-是,N-否',
  `BLACK_DESC` varchar(50) DEFAULT NULL COMMENT '黑名单描述|黑名单描述',
  `LIST_SOURCE` char(1) DEFAULT NULL COMMENT '名单来源|名单来源|H-核心,C-信用卡,Z-直销银行',
  `UNCOUNTER_DESC` varchar(50) DEFAULT NULL COMMENT '入表原因|入表原因',
  `BLACK_CHECK_TIME` varchar(26) DEFAULT NULL COMMENT '黑名单检查时间|检查时间',
  `ASYN_ID` varchar(50) DEFAULT NULL COMMENT '异步编号|异步编号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `EXAMINE_TELLER` varchar(10) DEFAULT NULL COMMENT '检查柜面|检查柜面',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `ASYN_DATE` datetime DEFAULT NULL COMMENT '同步日期|同步日期',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `INPUT_BRANCH` varchar(50) DEFAULT NULL COMMENT '录入机构|录入机构',
  `OPER_USER_ID` varchar(30) DEFAULT NULL COMMENT '操作柜员|操作柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `REMARK2` varchar(200) DEFAULT NULL COMMENT '备注2|备注2',
  `REMARK3` varchar(200) DEFAULT NULL COMMENT '备注3|备注3',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `LIST_TYPE` varchar(10) DEFAULT NULL COMMENT '名单类型代码|名单类型代码',
  `ISS_COUNTRY` varchar(3) DEFAULT NULL COMMENT '发证国家|发证国家',
  PRIMARY KEY (`BLACK_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='渠道整合平台黑名单表|记录渠道整合平台信息';

-- ----------------------------
-- Table structure for rb_telecomblack_list
-- ----------------------------
DROP TABLE IF EXISTS `rb_telecomblack_list`;
CREATE TABLE `rb_telecomblack_list` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `ACCT_TYPE` char(1) DEFAULT NULL COMMENT '账户类型|账户类型|A-AIO账户,C-结算账户,S-储蓄账户,T-定期账户',
  `ACCT_BRANCH` varchar(50) DEFAULT NULL COMMENT '账户开户行|账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `EXAMINE_TELLER` varchar(10) DEFAULT NULL COMMENT '检查柜面|检查柜面',
  `EXAMINE_FLAG` char(1) DEFAULT NULL COMMENT '可疑账户核实标志|可疑账户核实标志|1-待核实,2-已核实,3-核实未通过',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `ASYN_DATE` datetime DEFAULT NULL COMMENT '同步日期|同步日期',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `INPUT_BRANCH` varchar(50) DEFAULT NULL COMMENT '录入机构|录入机构',
  `UNCOUNTER_DESC` varchar(50) DEFAULT NULL COMMENT '入表原因|入表原因',
  `BLACK_CHECK_TIME` varchar(26) DEFAULT NULL COMMENT '黑名单检查时间|检查时间',
  `BLACK_NO` varchar(50) DEFAULT NULL COMMENT '黑名单编号|黑名单编号',
  `OUR_BANK_FLAG` char(1) DEFAULT NULL COMMENT '是否我行黑名单|是否我行黑名单|Y-是,N-否',
  `LIST_OPERATE_TYPE` varchar(5) DEFAULT NULL COMMENT '名单操作类型|名单操作类型',
  `BLACK_DESC` varchar(50) DEFAULT NULL COMMENT '黑名单描述|黑名单描述',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `LIST_SOURCE` char(1) DEFAULT NULL COMMENT '名单来源|名单来源|H-核心,C-信用卡,Z-直销银行',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `REMARK2` varchar(200) DEFAULT NULL COMMENT '备注2|备注2',
  `REMARK3` varchar(200) DEFAULT NULL COMMENT '备注3|备注3',
  `ASYN_ID` varchar(50) DEFAULT NULL COMMENT '异步编号|异步编号',
  `OPER_USER_ID` varchar(30) DEFAULT NULL COMMENT '操作柜员|操作柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='电信诈骗黑名单表|记录电信诈骗黑名单';

-- ----------------------------
-- Table structure for rb_terrorism_list
-- ----------------------------
DROP TABLE IF EXISTS `rb_terrorism_list`;
CREATE TABLE `rb_terrorism_list` (
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `EXAMINE_FLAG` char(1) DEFAULT NULL COMMENT '可疑账户核实标志|可疑账户核实标志|1-待核实,2-已核实,3-核实未通过',
  `EXAMINE_TELLER` varchar(10) DEFAULT NULL COMMENT '检查柜面|检查柜面',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `ASYN_ID` varchar(50) DEFAULT NULL COMMENT '异步编号|异步编号',
  `ASYN_DATE` datetime DEFAULT NULL COMMENT '同步日期|同步日期',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `BLACK_NO` varchar(50) DEFAULT NULL COMMENT '黑名单编号|黑名单编号',
  `OUR_BANK_FLAG` char(1) DEFAULT NULL COMMENT '是否我行黑名单|是否我行黑名单|Y-是,N-否',
  `LIST_OPERATE_TYPE` varchar(5) DEFAULT NULL COMMENT '名单操作类型|名单操作类型',
  `BLACK_DESC` varchar(50) DEFAULT NULL COMMENT '黑名单描述|黑名单描述',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,V-已验证,W-待处理(部分成功),S-成功,F-失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `LIST_SOURCE` char(1) DEFAULT NULL COMMENT '名单来源|名单来源|H-核心,C-信用卡,Z-直销银行',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `REMARK2` varchar(200) DEFAULT NULL COMMENT '备注2|备注2',
  `REMARK3` varchar(200) DEFAULT NULL COMMENT '备注3|备注3',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `UNCOUNTER_DESC` varchar(50) DEFAULT NULL COMMENT '入表原因|入表原因',
  `BLACK_CHECK_TIME` varchar(26) DEFAULT NULL COMMENT '黑名单检查时间|检查时间',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  PRIMARY KEY (`BATCH_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='涉案账户名单表|记录涉案账户名单';

-- ----------------------------
-- Table structure for rb_tran_control_hist
-- ----------------------------
DROP TABLE IF EXISTS `rb_tran_control_hist`;
CREATE TABLE `rb_tran_control_hist` (
  `CHANNEL_SEQ_NO` varchar(50) NOT NULL COMMENT '渠道流水号|渠道流水号',
  `CHANNEL_DATE` datetime NOT NULL COMMENT '渠道日期|渠道日期',
  `SOURCE_TYPE` varchar(10) NOT NULL COMMENT '渠道类型|渠道类型',
  `SUB_SEQ_NO` varchar(100) NOT NULL COMMENT '子流水号|子流水号',
  `BUSI_SUB_CLASS` varchar(10) DEFAULT NULL COMMENT '业务细类 |业务细类',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `TRAN_EVENT_TYPE` varchar(20) DEFAULT NULL COMMENT '事件类型 |事件类型',
  `TRAN_DESC` varchar(200) DEFAULT NULL COMMENT '交易描述|交易描述',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `ONLINE_TRAN_STATUS` char(1) DEFAULT NULL COMMENT '联机业务处理状态|联机业务处理状态|S-成功,P-处理中,F-失败 R-失败',
  `MESSAGE_TYPE` varchar(10) DEFAULT NULL COMMENT '接口服务类型|接口服务类型|1000-金融服务 ,1200-非金融 ,1300-冲正 ,1400-查询',
  `MESSAGE_CODE` varchar(10) DEFAULT NULL COMMENT '接口服务代码|接口服务代码',
  `SERVICE_CODE` varchar(20) DEFAULT NULL COMMENT '服务代码|服务代码',
  `SOURCE_MODULE` varchar(3) DEFAULT NULL COMMENT '源模块|源模块|RB-存款,CL-贷款,GL-总账,ALL-所有',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `CUSTOMER_SEQ_NO` varchar(100) DEFAULT NULL COMMENT '系统流水号|调用方流水号',
  PRIMARY KEY (`CHANNEL_SEQ_NO`,`CHANNEL_DATE`,`SOURCE_TYPE`,`SUB_SEQ_NO`,`TRAN_DATE`),
  KEY `IDX_RB_TRAN_CONTROL_HIST_5M` (`SUB_SEQ_NO`),
  KEY `IDX_RB_TRAN_CONTROL_HIST_1M` (`REFERENCE`),
  KEY `IDX_RB_TRAN_CONTROL_HIST_3M` (`CHANNEL_SEQ_NO`,`SUB_SEQ_NO`),
  KEY `IDX_RB_TRAN_CONTROL_HIST_2M` (`CHANNEL_SEQ_NO`),
  KEY `IDX_RB_TRAN_CONTROL_HIST_4M` (`SOURCE_TYPE`,`CHANNEL_SEQ_NO`,`SUB_SEQ_NO`,`CHANNEL_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='交易流程控制表|记录交易流程控制信息';

-- ----------------------------
-- Table structure for rb_tran_sumamt
-- ----------------------------
DROP TABLE IF EXISTS `rb_tran_sumamt`;
CREATE TABLE `rb_tran_sumamt` (
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `SUM_AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '金额总和|总金额',
  `SUM_COUNT` int DEFAULT NULL COMMENT '累计转账次数|累计转账次数',
  `REMARK` varchar(200) NOT NULL COMMENT '备注|备注',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`TRAN_DATE`,`REMARK`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='交易流水金额笔数汇总表|记录交易流水金额笔数汇总信息';

-- ----------------------------
-- Table structure for rb_uncounter_restraints
-- ----------------------------
DROP TABLE IF EXISTS `rb_uncounter_restraints`;
CREATE TABLE `rb_uncounter_restraints` (
  `UNCOUNTER_NO` varchar(50) NOT NULL COMMENT '编号|编号',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `ACCT_OPEN_DATE` datetime DEFAULT NULL COMMENT '账户开户日期|账户开户日期',
  `SETTLE_ACCT_CLASS` varchar(3) DEFAULT NULL COMMENT '结算账户分类|结算账户分类|PAY-付款账户,REC-收款账户,AUT-自动扣款账户,TPP-第三方账户,WTR-委托存款账户,WTS-委托结算账户,INT-利息入账账户,PRI-本金入账账户,TRA-定期自动转活期账户',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `UPDATE_DATE` datetime DEFAULT NULL COMMENT '更新日期|更新日期',
  `INPUT_BRANCH` varchar(50) DEFAULT NULL COMMENT '录入机构|录入机构',
  `ISSCAN_FLAG` char(1) DEFAULT NULL COMMENT '是否已被扫描|是否已被扫描|Y-是,N-否',
  `UNCOUNTER_RESTRAINT_TYPE` varchar(3) NOT NULL COMMENT '暂记非柜面限制类型|暂记非柜面限制类型',
  `UNCOUNTER_DESC` varchar(50) DEFAULT NULL COMMENT '入表原因|入表原因',
  `UNCOUNTER_RESTRAINT_STATUS` char(1) DEFAULT NULL COMMENT '暂停非柜面标记|暂停非柜面标记|1-待核实,2-核实通过,3-核实未通过,4-删除名单',
  `SUCCESS_FLAG` char(1) DEFAULT NULL COMMENT '成功标志|成功标志|Y-是,N-否',
  `BATCH_NO` varchar(50) DEFAULT NULL COMMENT '批次号|批次号',
  `LIST_SOURCE` char(1) DEFAULT NULL COMMENT '名单来源|名单来源|H-核心,C-信用卡,Z-直销银行',
  `UPDATE_BRANCH` varchar(50) DEFAULT NULL COMMENT '最后修改机构|最后修改机构',
  `OPER_USER_ID` varchar(30) DEFAULT NULL COMMENT '操作柜员|操作柜员',
  `UNCOUNTER_TIME` varchar(26) DEFAULT NULL COMMENT '单笔交易录入时间|单笔交易录入时间',
  `UPDATE_USER` varchar(30) DEFAULT NULL COMMENT '更新柜员|更新柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `OPEN_BRANCH` varchar(50) DEFAULT NULL COMMENT '开立机构|开立机构',
  `REMARK2` varchar(200) DEFAULT NULL COMMENT '备注2|备注2',
  `START_EFFECT_DATE` datetime DEFAULT NULL COMMENT '起始生效日期|起始生效日期',
  PRIMARY KEY (`UNCOUNTER_NO`),
  KEY `IDX_RB_UNCOUNTER_RESTRAINTS_1M` (`UNCOUNTER_RESTRAINT_TYPE`),
  KEY `IDX_RB_UNCOUNTER_RESTRAINTS_4M` (`DOCUMENT_ID`),
  KEY `IDX_RB_UNCOUNTER_RESTRAINTS_2M` (`UNCOUNTER_RESTRAINT_STATUS`),
  KEY `IDX_RB_UNCOUNTER_RESTRAINTS_3M` (`BASE_ACCT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='暂停非柜面账户限制表|记录暂停非柜面账户限制信息';

-- ----------------------------
-- Table structure for rb_uncounter_restraints_hist
-- ----------------------------
DROP TABLE IF EXISTS `rb_uncounter_restraints_hist`;
CREATE TABLE `rb_uncounter_restraints_hist` (
  `BATCH_NO` varchar(50) DEFAULT NULL COMMENT '批次号|批次号',
  `LIST_SOURCE` char(1) DEFAULT NULL COMMENT '名单来源|名单来源|H-核心,C-信用卡,Z-直销银行',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `UNCOUNTER_NO` varchar(50) NOT NULL COMMENT '编号|编号',
  `UNCOUNTER_RESTRAINT_TYPE` varchar(3) NOT NULL COMMENT '暂记非柜面限制类型|暂记非柜面限制类型',
  `UNCOUNTER_DESC` varchar(50) DEFAULT NULL COMMENT '入表原因|入表原因',
  `UNCOUNTER_RESTRAINT_STATUS` char(1) DEFAULT NULL COMMENT '暂停非柜面标记|暂停非柜面标记|1-待核实,2-核实通过,3-核实未通过,4-删除名单',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `ACCT_STATUS` char(1) DEFAULT NULL COMMENT '账户状态|描述账户生命周期不同阶段的划分|N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭 ,U-手工解除',
  `ACCT_OPEN_DATE` datetime DEFAULT NULL COMMENT '账户开户日期|账户开户日期',
  `SETTLE_ACCT_CLASS` varchar(3) DEFAULT NULL COMMENT '结算账户分类|结算账户分类|PAY-付款账户,REC-收款账户,AUT-自动扣款账户,TPP-第三方账户,WTR-委托存款账户,WTS-委托结算账户,INT-利息入账账户,PRI-本金入账账户,TRA-定期自动转活期账户',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `UPDATE_DATE` datetime DEFAULT NULL COMMENT '更新日期|更新日期',
  `OPEN_BRANCH` varchar(50) DEFAULT NULL COMMENT '开立机构|开立机构',
  `ISSCAN_FLAG` char(1) DEFAULT NULL COMMENT '是否已被扫描|是否已被扫描|Y-是,N-否',
  `SUCCESS_FLAG` char(1) DEFAULT NULL COMMENT '成功标志|成功标志|Y-是,N-否',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `REMARK2` varchar(200) DEFAULT NULL COMMENT '备注2|备注2',
  `REMARK3` varchar(200) DEFAULT NULL COMMENT '备注3|备注3',
  `UPDATE_BRANCH` varchar(50) DEFAULT NULL COMMENT '最后修改机构|最后修改机构',
  `UNCOUNTER_TIME` varchar(26) DEFAULT NULL COMMENT '单笔交易录入时间|单笔交易录入时间',
  `UPDATE_USER` varchar(30) DEFAULT NULL COMMENT '更新柜员|更新柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  PRIMARY KEY (`SEQ_NO`),
  KEY `IDX_RB_UNCOUNTER_RES_HIST_1M` (`UNCOUNTER_RESTRAINT_TYPE`),
  KEY `IDX_RB_UNCOUNTER_RES_HIST_4M` (`DOCUMENT_ID`),
  KEY `IDX_RB_UNCOUNTER_RES_HIST_3M` (`BASE_ACCT_NO`),
  KEY `IDX_RB_UNCOUNTER_RES_HIST_2M` (`UNCOUNTER_RESTRAINT_STATUS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='暂停非柜面账户限制历史表|记录暂停非柜面账户限制历史信息';

-- ----------------------------
-- Table structure for rb_voucher_sell_info
-- ----------------------------
DROP TABLE IF EXISTS `rb_voucher_sell_info`;
CREATE TABLE `rb_voucher_sell_info` (
  `DOC_TYPE` varchar(10) NOT NULL COMMENT '凭证类型|凭证类型',
  `PREFIX` varchar(10) DEFAULT NULL COMMENT '前缀|前缀',
  `VOUCHER_END_NO` varchar(50) NOT NULL COMMENT '凭证终止号码|凭证终止号码',
  `VOUCHER_START_NO` varchar(50) NOT NULL COMMENT '凭证起始号码|凭证起始号码',
  `VOUCHER_STATUS` varchar(3) DEFAULT NULL COMMENT '凭证状态|凭证状态|NUS-未使用,WAY-未使用在途,WDE-待销毁,WWA-待销毁在途,DES-销毁,LCB-丢失,CAN-已作废,LOC-已锁定,SOL-已签发,ACP-已承兑,VER-口头挂失,LCC-正式挂失,USE-已收回,POB-已兑付,SET-已结清,PBK-已退回',
  `VOUCHER_STATUS_PRE` varchar(3) DEFAULT NULL COMMENT '凭证交易前状态|凭证交易前状态',
  `SUB_SEQ_NO` varchar(100) DEFAULT NULL COMMENT '子流水号|子流水号',
  `CHANNEL_DATE` datetime DEFAULT NULL COMMENT '渠道日期|渠道日期',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_BRANCH` varchar(50) NOT NULL COMMENT '交易机构|交易机构',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='凭证状态变化登记簿（外围专用）|记录凭证状态变化登记(外围)信息';

-- ----------------------------
-- Table structure for rb_wechat_amt_info
-- ----------------------------
DROP TABLE IF EXISTS `rb_wechat_amt_info`;
CREATE TABLE `rb_wechat_amt_info` (
  `BATCH_SEQ_NO` varchar(50) NOT NULL COMMENT '批次明细序号|批次明细序号',
  `CHANNEL` varchar(10) NOT NULL COMMENT '渠道|渠道',
  `MIN_BAL` decimal(17,2) NOT NULL COMMENT '最小余额|最小余额',
  `EFFECT_DATE` datetime NOT NULL COMMENT '生效日期|生效日期',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_SEQ_NO`),
  KEY `RB_WECHAT_AMT_INFO_INDM` (`CHANNEL`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='微信银行动作阈值表|记录微信银行阈值信息';

-- ----------------------------
-- Table structure for rb_white_list
-- ----------------------------
DROP TABLE IF EXISTS `rb_white_list`;
CREATE TABLE `rb_white_list` (
  `EFFECT_FLAG` char(1) DEFAULT NULL COMMENT '是否生效标志|是否生效标志|Y-是,N-否',
  `SOURCE_TYPE` varchar(10) NOT NULL COMMENT '渠道类型|渠道类型',
  `ACTUAL_ACCT_NO` varchar(50) NOT NULL COMMENT '实际账号|实际账号',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  PRIMARY KEY (`SOURCE_TYPE`,`ACTUAL_ACCT_NO`),
  KEY `RB_WHITE_LIST_INDXM` (`ACTUAL_ACCT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='白名单|记录白名单';

-- ----------------------------
-- Table structure for rc_all_list
-- ----------------------------
DROP TABLE IF EXISTS `rc_all_list`;
CREATE TABLE `rc_all_list` (
  `DATA_TYPE` varchar(20) NOT NULL COMMENT '数据类型|黑名单数据类型|AccountNumber-卡/折号,IDType_IDNumber-证件号,ClientNo-客户号,CellPhoneNumber-手机号,IPAddress-IP地址',
  `DATA_VALUE` varchar(50) NOT NULL COMMENT '数据值|数据值',
  `LIST_TYPE` varchar(10) NOT NULL COMMENT '名单类型代码|名单类型代码|0001-电信诈骗已冻结对私卡号需暂停所有业务(不收不付，禁止换卡，限制关联账号),0002-电信诈骗已冻结存折号需暂停所有业务(不收不付，禁止换卡，限制关联账号),0003-电信诈骗已冻结对公账号需暂停所有业务(不收不付，禁止换卡，限制关联账号),0004-电信诈骗已冻结支付账号需暂停所有业务,0011-公安机关认定为违法犯罪活动，5年内暂停非柜面业务，3年内不得为其新开立账户,0012-买卖银行账户、冒名开户、为违法犯罪活动，5年内暂停非柜面业务，3年内不得为其新开立账户,0021-被全国企业信用信息公示系统列入“严重违法失信企业名单”,0022-经银行核实单位注册地址不存在或者虚构的单位证件号，银行不得为其开户,0031-经过工信部认定无法证明与银行账号绑定合理性的手机号，应当对相关银行账户暂停非柜面业务,0041-中国支付清算协会、银行卡清算机构移交”金融信用信息基础数据库,1001-案件侦办过程中可疑对私卡号需暂停非柜面业务（86号文规定）,1002-案件侦办过程中可疑存折号需暂停非柜面业务（86号文规定）,1003-案件侦办过程中可疑对公账号需暂停非柜面业务（86号文规定）,1004-案件侦办过程中可疑支付账号需风险提示（86号文规定）,2001-行内风险客户名单(限制所有交易),8003-6个月无交易记录',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `ISS_COUNTRY` varchar(3) DEFAULT NULL COMMENT '发证国家|发证国家',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `OUR_BANK_FLAG` char(1) DEFAULT NULL COMMENT '是否我行黑名单|是否我行黑名单|Y-是,N-否',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `MATURITY_DATE` datetime DEFAULT NULL COMMENT '到期日期|到期日期',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `INPUT_BRANCH` varchar(50) DEFAULT NULL COMMENT '录入机构|录入机构',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|渠道类型',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `REMARK1` varchar(200) DEFAULT NULL COMMENT '备注1|备注1',
  `REMARK2` varchar(200) DEFAULT NULL COMMENT '备注2|备注2',
  `REMARK3` varchar(200) DEFAULT NULL COMMENT '备注3|备注3',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `LIST_CATEGORY` varchar(2) DEFAULT NULL COMMENT '名单种类代码|名单种类代码|01-人行黑名单  ,02-人行灰名单  ,11-行内黑名单  ,12-行内白名单 ',
  `LIST_ORG` varchar(50) DEFAULT NULL COMMENT '名单发送/审核机构|名单发送/审核机构|000000-本行   ,200501-公安,300501-工商总局,400501-高法(暂缓实现),500501-工信部(暂缓实现),600501-中国支付清算协会(暂缓实现),700501-银行卡清算(暂缓实现)',
  PRIMARY KEY (`DATA_TYPE`,`DATA_VALUE`,`LIST_TYPE`),
  KEY `INDx_RC_ALL_LIST` (`ACCT_NAME`),
  KEY `IDX_RC_ALL_LIST_2M` (`LIST_TYPE`),
  KEY `IDX_RC_ALL_LIST_1M` (`DATA_TYPE`),
  KEY `IDX_RC_ALL_LIST_3M` (`DATA_VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='黑灰名单信息表|记录黑灰名单';

-- ----------------------------
-- Table structure for rc_file_notice
-- ----------------------------
DROP TABLE IF EXISTS `rc_file_notice`;
CREATE TABLE `rc_file_notice` (
  `SYSTEM_ID` varchar(20) NOT NULL COMMENT '系统ID|系统ID',
  `FILE_TYPE` varchar(50) NOT NULL COMMENT '文件类型|文件类型',
  `REMOTE_FILE_NAME` varchar(200) NOT NULL COMMENT '远端文件名|远端文件名',
  `FILE_NAME` varchar(200) DEFAULT NULL COMMENT '文件名称|文件名称',
  `FILE_PATH` varchar(200) DEFAULT NULL COMMENT '文件路径|文件路径',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `START_DATE` datetime DEFAULT NULL COMMENT '开始日期|开始日期',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|结束日期',
  `END_FLAG` char(1) DEFAULT NULL COMMENT '完成状态|完成状态|Y-是,N-否',
  `OK_FLAG` char(1) DEFAULT NULL COMMENT '是否已完成|是否已完成|Y-是,N-否',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SYSTEM_ID`,`FILE_TYPE`,`REMOTE_FILE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='黑灰名单文件登记表|记录文件登记信息';

-- ----------------------------
-- Table structure for rc_inner_list
-- ----------------------------
DROP TABLE IF EXISTS `rc_inner_list`;
CREATE TABLE `rc_inner_list` (
  `LIST_TYPE` varchar(10) NOT NULL COMMENT '名单类型代码|名单类型代码',
  `LIST_CATEGORY` varchar(2) NOT NULL COMMENT '名单种类代码|名单种类代码|01-人行黑名单  ,02-人行灰名单  ,11-行内黑名单  ,12-行内白名单 ',
  `DATA_TYPE` varchar(20) NOT NULL COMMENT '数据类型|黑名单数据类型|AccountNumber-卡/折号,IDType_IDNumber-证件号,ClientNo-客户号,CellPhoneNumber-手机号,IPAddress-IP地址 ',
  `DATA_VALUE` varchar(50) NOT NULL COMMENT '数据值|数据值',
  `CREATE_DATE` datetime DEFAULT NULL COMMENT '创建日期|创建日期',
  `ISS_COUNTRY` varchar(3) DEFAULT NULL COMMENT '发证国家|发证国家',
  `CANCEL_DATE` datetime DEFAULT NULL COMMENT '取消日期|指不同场景协议到期处理日期',
  `LIST_ORG` varchar(50) DEFAULT NULL COMMENT '名单发送/审核机构|名单发送/审核机构|000000-本行   ,200501-公安,300501-工商总局,400501-高法(暂缓实现),500501-工信部(暂缓实现),600501-中国支付清算协会(暂缓实现),700501-银行卡清算(暂缓实现)',
  `NAME` varchar(200) DEFAULT NULL COMMENT '名称|名称',
  `VALID_FROM` datetime DEFAULT NULL COMMENT '生效起始时间|生效起始时间,格式 YYYYMMDDHHMMSS',
  `VALID_THRU` datetime DEFAULT NULL COMMENT '生效截止时间|生效截止时间,格式 YYYYMMDDHHMMSS',
  `RECORD_STATUS` char(1) DEFAULT NULL COMMENT '记录状态|记录状态|A-新增,C-取消',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `AUDIT_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '审计时间|审计时间',
  `CANCEL_USER_ID` varchar(30) DEFAULT NULL COMMENT '取消柜员|取消柜员',
  `CREATE_USER_ID` varchar(30) DEFAULT NULL COMMENT '录入柜员|录入柜员',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`LIST_TYPE`,`LIST_CATEGORY`,`DATA_TYPE`,`DATA_VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='行内名单表|行内名单表';

-- ----------------------------
-- Table structure for sonic_running_step_lock
-- ----------------------------
DROP TABLE IF EXISTS `sonic_running_step_lock`;
CREATE TABLE `sonic_running_step_lock` (
  `step_run_id` varchar(32) NOT NULL COMMENT 'Step运行时唯一ID',
  `executor_id` varchar(32) DEFAULT NULL COMMENT '执行者ID',
  `lock_id` varchar(32) DEFAULT NULL COMMENT '锁ID',
  `lock_time` timestamp NULL DEFAULT NULL COMMENT '锁时间戳',
  PRIMARY KEY (`step_run_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='Step执行锁';

-- ----------------------------
-- Table structure for sonic_step_run_result
-- ----------------------------
DROP TABLE IF EXISTS `sonic_step_run_result`;
CREATE TABLE `sonic_step_run_result` (
  `step_run_id` varchar(32) NOT NULL COMMENT 'Step运行时唯一ID',
  `complete_status` varchar(10) DEFAULT NULL COMMENT 'Step完成状态',
  `complete_time` timestamp NULL DEFAULT NULL COMMENT 'Step完成时间',
  `executor_id` varchar(32) DEFAULT NULL COMMENT '执行者ID',
  PRIMARY KEY (`step_run_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='Step执行结果';

-- ----------------------------
-- Table structure for tran_sql_log
-- ----------------------------
DROP TABLE IF EXISTS `tran_sql_log`;
CREATE TABLE `tran_sql_log` (
  `SERVICE_ID` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务ID',
  `SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务唯一识别号',
  `TRAN_DATE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易日期',
  `TRAN_TIME` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易时间',
  `SOURCE_TYPE` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '渠道类型',
  `SEQ_NO` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '渠道流水号',
  `PROGRAM_ID` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易屏幕标识',
  `REFERENCE` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '业务参考号',
  `USER_ID` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作柜员',
  `BRANCH_ID` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '网点',
  `TABLE_NAME` varchar(50) DEFAULT NULL COMMENT '表名',
  `SQL_TYPE` varchar(10) DEFAULT NULL COMMENT '操作类型（UPDATE/DELETE/INSERT)',
  `BEFORE_EXE_DATA` varchar(4000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '执行前数据',
  `AFTER_EXE_DATA` varchar(4000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '执行后数据',
  `CREATE_DATE` varchar(26) DEFAULT NULL COMMENT '创建时间',
  KEY `TRAN_SQL_LOG_IDX1` (`TRAN_DATE`) USING BTREE,
  KEY `TRAN_SQL_LOG_IDX2` (`REFERENCE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='sql数据登记表';

SET FOREIGN_KEY_CHECKS = 1;
