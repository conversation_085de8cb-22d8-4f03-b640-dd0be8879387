# CIF模块接口详细清单

## CIF81-客户信息管理 (22个接口)

| 序号 | 接口名称 | 接口定义 | URL | 功能描述 |
|------|----------|----------|-----|----------|
| 9 | 对公客户信息维护(修改版) | MbsdCore-1200-9114 | /cif/nfin/client/public/maint/new | 对公客户信息新增修改，包括客户全量基本信息，证件信息，联系信息等 |
| 10 | FATCA信息维护 | MbsdCore-1200-9981 | /cif/nfin/client/fatca/maint | 根据上送信息FATCA信息维护 |
| 11 | CRS信息维护 | MbsdCore-1200-9971 | /cif/nfin/client/crs/maint | 根据上送信息维护CRS信息 |
| 12 | 客户变更 | MbsdCore-1200-9174 | /cif/nfin/client/change | 客户机构变更 |
| 13 | 客户信息注销与激活 | MbsdCore-1200-9120 | /cif/nfin/client/cancel | 客户信息注销，注销后激活 |
| 16 | 联名客户关系建立 | MbsdCore-1200-9112 | /cif/nfin/client/joint/maint | 联名客户关系建立。自动生成联名客户号（相当于虚拟客户），并建立联名关系 |
| 17 | 快速建立客户信息 | MbsdCore-1200-9100 | /cif/nfin/client/create | 使用客户信息中少量关键要素，快速建立客户信息 |
| 18 | 对私客户信息维护简化版 | MbsdCore-1200-9103 | /cif/nfin/client/private/jsmaint | 对客户信息中的少量常用信息进行维护 |
| 19 | 对私客户信息维护 | MbsdCore-1200-9106 | /cif/nfin/client/private/maint | 个人客户信息新增修改，包括客户全量基本信息，证件信息，联系信息等 |
| 20 | 对公客户信息维护 | MbsdCore-1200-9107 | /cif/nfin/client/public/maint | 对公客户信息新增修改，包括客户全量基本信息，证件信息，联系信息等 |
| 21 | 客户状态维护 | MbsdCore-1200-9109 | /cif/nfin/client/private/main/status | 根据客户号修改客户状态(印尼项目) |
| 22 | 客户经理维护 | MbsdCore-1200-9110 | /cif/nfin/client/acctexec/maint | 对客户经理信息进行新增修改删除 |
| 23 | 客户关系维护 | MbsdCore-1200-9111 | /cif/nfin/client/relation/maint | 对客户关系进行新增修改删除，客户关系包括关系人或反向关系 |
| 24 | 客户密码维护 | MbsdCore-1200-9108 | /cif/nfin/client/password/maint | 对客户密码进行新增,更新,删除,校验,重置 |

## CIF82-客户信息查询 (19个接口)

| 序号 | 接口名称 | 接口定义 | URL | 功能描述 |
|------|----------|----------|-----|----------|
| 31 | 批量查询客户和账户信息 | MbsdCore-1220-9111 | /cif/file/client/batchquery | 批量查询客户和账户信息 |
| 32 | 客户基本信息查询 | MbsdCore-1400-9100 | /cif/inq/client/basic | 根据客户号或证件信息查询单笔客户详细信息 |
| 33 | 查询客户基本信息(多笔) | MbsdCore-1400-9102 | /cif/inq/client/batch | 根据条件查询，返回多笔客户详细信息 |
| 34 | 客户详细信息查询 | MbsdCore-1400-9105 | /cif/inq/client/detail | 根据客户号或者证件信息查询客户详细信息 |
| 35 | 客户首选信息查询 | MbsdCore-1400-9101 | /cif/inq/client/preferred | 根据客户号或证件信息查询单笔客户详细信息 |
| 36 | 客户证件信息查询 | MbsdCore-1400-0006 | /cif/inq/client/certificate | 根据证件信息查询客户简要信息和证件信息 |
| 37 | 客户证件批量查询 | MbsdCore-1400-9113 | /cif/inq/client/certificate/batch | 根据证件类型证件号码批量查询客户证件信息 |
| 38 | 客户证件到期信息查询 | MbsdCore-1400-0017 | /cif/inq/client/certificate/expire | 根据客户类型，证件类型、起始日期、结束日期进行客户证件到期信息查询 |
| 40 | 客户关系查询 | MbsdCore-1400-9104 | /cif/inq/client/relation | 根据客户号，查询该客户之前在系统中维护的客户关系信息 |
| 41 | 相似客户查询 | MbsdCore-1400-9107 | /cif/inq/client/resemble | 根据相似规则，查询规则匹配的所有相似的客户信息列表 |
| 42 | 客户相似信息查询 | MbsdCore-1400-9114 | /cif/inq/client/similarity/query | 通过客户相关信息查询相似信息 |
| 43 | 客户组成员信息查询 | MbsdCore-1400-9108 | /cif/inq/client/group | 查询上送客户号所在客户组的所有客户信息数组 |
| 44 | 按客户经理查询客户信息 | MbsdCore-1400-9109 | /cif/inq/client/byacctexec | 按客户经理查询客户号名称证件等简要信息 |
| 45 | 客户经理信息查询 | MbsdCore-1400-9111 | /cif/inq/client/acctexec | 根据客户经理编号，查询客户经理的详细信息列表 |
| 46 | 业务信息修改记录查询 | MbsdCore-1400-9112 | /cif/inq/client/amend | 查询客户信息的维护前后变更信息 |
| 48 | 客户批量开立查询 | MbsdCore-1400-9118 | /cif/inq/client/batchcreate | 查询批量开立客户信息，并支持结果文件下载 |
| 49 | 根据客户号查询客户联系人信息 | MbsdCore-1400-9182 | /cif/inq/client/contact | 根据客户号查询客户联系人信息 |

## CIF83-客户冻结限制 (6个接口)

| 序号 | 接口名称 | 接口定义 | URL | 功能描述 |
|------|----------|----------|-----|----------|
| 50 | 客户级限制 | MbsdCore-1200-0122 | /cif/nfin/client/restraint/maint | 对客户进行限制信息维护，包含新增限制或解除限制 |
| 51 | 根据客户号查询客户级限制信息 | MbsdCore-1400-0148 | /cif/inq/client/restraint | 根据客户号查询客户级限制信息 |
| 52 | 客户渠道限制 | MbsdCore-1200-9165 | /cif/nfin/channel/control | 对客户进行限制维护,包含新增或解除限制,或者修改限制 |
| 53 | 客户渠道限制查询 | MbsdCore-1400-9165 | /cif/inq/channel/control | 客户渠道限制查询 |
| 54 | 客户冻结维护 | MbsdCore-1200-9116 | /cif/nfin/client/block/maint | 对客户进行冻结，支持冻结或解冻 |
| 55 | 根据客户号查询客户冻结信息 | MbsdCore-1400-9116 | /cif/inq/client/block | 根据客户号或证件号查询客户冻结信息 |

## CIF85-客户合并 (3个接口)

| 序号 | 接口名称 | 接口定义 | URL | 功能描述 |
|------|----------|----------|-----|----------|
| 58 | 客户合并 | MbsdCore-1200-9121 | /cif/nfin/client/merge | 将被合并客户废弃，保留新客户信息，并广播通知其他系统 |
| 59 | 客户合并查询 | MbsdCore-1400-9121 | /cif/inq/client/mergeinfo | 查询客户合并登记簿信息 |
| 60 | 客户合并处理状态修改 | MbsdCore-1200-9122 | /cif/nfin/client/merge/statusupdate | 客户合并处理状态修改接口 |

## CIF84-客户身份核查 (2个接口)

| 序号 | 接口名称 | 接口定义 | URL | 功能描述 |
|------|----------|----------|-----|----------|
| 56 | 批量核查结果登记 | MbsdCore-1220-9118 | /cif/file/verify/batchregister | 以txt文件的形式批量导入客户核查结果 |
| 57 | 批量核查结果文件下载 | MbsdCore-1220-9119 | /cif/file/verify/downloadresfile | 根据上送的批次号，在个人客户身份核实情况表中查询信息 |

## 其他功能接口

### 系统管理类
| 序号 | 接口名称 | 接口定义 | URL | 功能描述 |
|------|----------|----------|-----|----------|
| 1 | 柜员权限查询 | Mbsdcore-1400-3002 | /cif/inq/user/access | 柜员权限查询 |
| 2 | CIF接收OB外部营业日期 | MbsdCore-1200-8902 | /cif/nfin/rundate/change | ob系统调用此接口,向cif推送新的营业日期 |
| 4 | 查询用户自定义参数 | MbsdCore-1400-9997 | /cif/inq/user/defined | 查询用户自定义数据表 |
| 7 | 参数统一查询 | MbsdCore-1400-9130 | /cif/inq/param/queryparam | 参数统一查询接口 |
| 62 | 柜员权限维护 | Mbsdcore-1200-3001 | /cif/nfin/user/access | 用于维护柜员权限表 |

### 特殊业务类
| 序号 | 接口名称 | 接口定义 | URL | 功能描述 |
|------|----------|----------|-----|----------|
| 3 | 根据客户类型查询产品类型 | MbsdCore-1400-4005 | /pf/inq/param/query/prodtype | 根据客户类型查询产品类型 |
| 8 | 机构撤并客户校验项查询 | MbsdCore-1400-9120 | /cif/inq/branch/change/checkinfo | 机构撤并前检查 |
| 61 | 批量导入查询 | MbsdCore-1400-7003 | /cif/inq/list/importInfo | 查询批量导入的人行原始黑灰名单表信息 |

## 接口命名规范

- **查询接口**: 以 `/cif/inq/` 开头，使用 1400 系列交易码
- **维护接口**: 以 `/cif/nfin/` 开头，使用 1200 系列交易码  
- **文件接口**: 以 `/cif/file/` 开头，使用 1220 系列交易码
- **产品接口**: 以 `/pf/` 开头，属于产品模块

## 接口类型统计

- **查询类接口**: 30个 (47.6%)
- **维护类接口**: 26个 (41.3%) 
- **文件类接口**: 6个 (9.5%)
- **其他接口**: 1个 (1.6%)
