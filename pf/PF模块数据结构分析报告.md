# PF模块数据结构分析报告

## 概述

PF模块是银行核心系统中的参数工厂(Parameter Factory)模块，主要负责系统基础参数管理、产品定义、利率管理、汇率管理、费用管理、事件管理等核心功能。本报告基于`pf.sql`文件，对PF模块的数据结构进行全面分析。

## 核心业务领域

### 1. 系统基础管理 (FM_*)
- **系统参数**: `fm_system` - 系统业务参数表
- **机构管理**: `fm_branch` - 机构信息表
- **用户管理**: `fm_user` - 核心柜员信息表
- **币种管理**: `fm_currency` - 币种基本信息表
- **交易流水**: `fm_tran_info` - 公共统一流水信息表

### 2. 产品管理 (MB_PROD_*)
- **产品类型**: `mb_prod_type` - 产品类型定义表
- **产品分类**: `mb_prod_class` - 产品分类定义表
- **产品目录**: `mb_prod_catalog` - 产品目录表
- **产品定义**: `mb_prod_define` - 产品定义表
- **产品余额**: `mb_prod_bal_default` - 产品起存金额定义表
- **产品利率**: `mb_prod_int` - 产品利率信息表

### 3. 利率管理 (MB_INT_*, MB_BASIS_*)
- **利率类型**: `mb_int_type` - 利率税率类型表
- **基准利率**: `mb_int_basis` - 基准利率类型表
- **基准利率信息**: `mb_basis_rate` - 基准利率信息表
- **利率矩阵**: `mb_int_matrix` - 利率税率阶梯表

### 4. 汇率管理 (MB_CCY_*, MB_EXCHANGE_*)
- **汇率类型**: `mb_exchange_type` - 汇率类型表
- **汇率牌价**: `mb_ccy_rate` - 汇率牌价表
- **汇率历史**: `mb_ccy_rate_hist` - 汇率牌价历史表
- **货币对汇率**: `mb_duad_ccy_rate` - 货币对汇率牌价表

### 5. 费用管理 (MB_FEE_*)
- **费用类型**: `mb_fee_type` - 费用类型表
- **费用费率**: `mb_fee_rate` - 费率信息表
- **费用矩阵**: `mb_fee_matrix` - 费率矩阵信息表
- **费用映射**: `mb_fee_mapping` - 费用启用规则表
- **费用目录**: `mb_fee_catalog` - 费用目录

### 6. 事件管理 (MB_EVENT_*)
- **事件类型**: `mb_event_type` - 事件类型定义表
- **事件分类**: `mb_event_class` - 事件分类定义表
- **事件属性**: `mb_event_attr` - 事件参数定义表

### 7. 参数管理 (MB_ATTR_*)
- **参数类型**: `mb_attr_type` - 参数定义表
- **参数值**: `mb_attr_value` - 参数值定义表
- **参数分类**: `mb_attr_class` - 参数分类定义表

## 主要数据表分析

### 核心系统表

#### 1. fm_system (系统业务参数表)
- **主键**: COMPANY
- **功能**: 记录系统运行的核心参数
- **关键字段**: 
  - `RUN_DATE`: 系统当前运行会计日期
  - `SYSTEM_PHASE`: 系统所处的阶段(INP-日间,EOD-日终,SOD-日始)
  - `LOCAL_CCY`: 当地币种
  - `BASE_CCY`: 基础币种
  - `MULTI_CORPORATION_FLAG`: 是否多法人系统

#### 2. fm_branch (机构信息表)
- **主键**: BRANCH
- **功能**: 记录银行机构层级结构
- **关键字段**:
  - `BRANCH_NAME`: 机构名称
  - `BRANCH_TYPE`: 机构类型
  - `HIERARCHY_CODE`: 层级代码
  - `PARENT_BRANCH`: 上级机构

#### 3. fm_user (核心柜员信息表)
- **主键**: USER_ID + BRANCH
- **功能**: 记录柜员基本信息
- **关键字段**:
  - `USER_TYPE`: 柜员类别(DUMMY_TELLER-虚拟柜员,TELLER_USER-普通柜员)
  - `USER_SUB_TYPE`: 柜员细类(A-ATM柜员,I-ITM柜员,Y-实体柜员)
  - `ACCOUNT_STATUS`: 柜员状态(A-有效,D-删除)

### 产品管理核心表

#### 1. mb_prod_type (产品类型定义表)
- **主键**: PROD_TYPE + COMPANY
- **功能**: 定义银行所有产品类型
- **关键字段**:
  - `PROD_CLASS`: 产品分类
  - `BASE_PROD_TYPE`: 基础产品
  - `PROD_GROUP_FLAG`: 是否产品组(Y-组合产品,N-单一产品)
  - `PROD_RANGE`: 产品作用范围(B-基础产品,S-可售产品)

#### 2. mb_prod_int (产品利率信息表)
- **主键**: PROD_TYPE + EVENT_TYPE + INT_CLASS + INT_TYPE + COMPANY
- **功能**: 定义产品的利率计算规则
- **关键字段**:
  - `INT_CLASS`: 利息分类(INT-正常利息,ODI-复利,PDUE-超期利息)
  - `INT_CALC_METHOD`: 利息计算方法(AB-积数计息,EB-分段计息)
  - `RATE_LAYER_RULE`: 利率分层规则(A-按金额分层,P-按周期分层,N-不分层)

### 利率管理核心表

#### 1. mb_int_matrix (利率税率阶梯表)
- **主键**: MATRIX_NO
- **功能**: 定义利率的阶梯计算规则
- **关键字段**:
  - `MATRIX_AMT`: 阶梯金额
  - `ACTUAL_RATE`: 行内利率
  - `SPREAD_PERCENT`: 浮动百分比
  - `SPREAD_RATE`: 浮动点数
  - `MAX_RATE`: 最大利率
  - `MIN_RATE`: 最小利率

#### 2. mb_basis_rate (基准利率信息表)
- **主键**: INT_BASIS + CCY + EFFECT_DATE
- **功能**: 记录基准利率的历史变化
- **关键字段**:
  - `INT_BASIS_RATE`: 基准利率
  - `EFFECT_DATE`: 生效日期
  - `UPDATE_DATE`: 更新日期

### 汇率管理核心表

#### 1. mb_ccy_rate (汇率牌价表)
- **主键**: RATE_TYPE + CCY + BRANCH + EFFECT_DATE + EFFECT_TIME
- **功能**: 记录汇率牌价信息
- **关键字段**:
  - `QUOTE_TYPE`: 牌价类型(D-直接,I-间接)
  - `EXCH_BUY_RATE`: 汇买价
  - `EXCH_SELL_RATE`: 汇卖价
  - `MIDDLE_RATE`: 中间价
  - `NOTES_BUY_RATE`: 钞买价
  - `NOTES_SELL_RATE`: 钞卖价

### 费用管理核心表

#### 1. mb_fee_type (费用类型表)
- **主键**: FEE_TYPE + MB_CCY_TYPE
- **功能**: 定义费用类型和计算方式
- **关键字段**:
  - `FEE_MODE`: 费率计算方式(F-固定金额,R-固定比例,B-固定金额+比例,S-差额累进,T-全额累进)
  - `BO_IND`: 日终/联机标志(O-联机,B-日终批量)
  - `CCY_FLAG`: 收费币种标识(T-交易币种收费,S-指定币种收费)

#### 2. mb_fee_mapping (费用启用规则表)
- **主键**: IRL_SEQ_NO
- **功能**: 定义费用的启用规则和匹配条件
- **关键字段**:
  - `PROD_TYPE_RULE`: 费用启用规则产品类型
  - `BRANCH_RULE`: 机构匹配规则
  - `CLIENT_TYPE_RULE`: 费用启用规则客户类型
  - `TRAN_TYPE_RULE`: 费用启用规则交易类型
  - `EVENT_TYPE_RULE`: 费用启用规则事件类型

## 数据关系分析

### 1. 系统基础关系
```
FM_SYSTEM (系统参数)
├── FM_BRANCH (机构信息) - 通过COMPANY关联
├── FM_USER (柜员信息) - 通过COMPANY关联
└── FM_CURRENCY (币种信息) - 通过COMPANY关联
```

### 2. 产品管理关系
```
MB_PROD_CLASS (产品分类)
└── MB_PROD_TYPE (产品类型) - 通过PROD_CLASS关联
    ├── MB_PROD_CATALOG (产品目录) - 通过PROD_TYPE关联
    ├── MB_PROD_DEFINE (产品定义) - 通过PROD_TYPE关联
    ├── MB_PROD_BAL_DEFAULT (产品余额) - 通过PROD_TYPE关联
    └── MB_PROD_INT (产品利率) - 通过PROD_TYPE关联
```

### 3. 利率管理关系
```
MB_INT_BASIS (基准利率类型)
├── MB_BASIS_RATE (基准利率信息) - 通过INT_BASIS关联
└── MB_INT_MATRIX (利率矩阵) - 通过INT_BASIS关联

MB_INT_TYPE (利率类型)
└── MB_INT_MATRIX (利率矩阵) - 通过INT_TYPE关联
```

### 4. 汇率管理关系
```
MB_EXCHANGE_TYPE (汇率类型)
└── MB_CCY_RATE (汇率牌价) - 通过RATE_TYPE关联
    └── MB_CCY_RATE_HIST (汇率历史) - 历史数据
```

### 5. 费用管理关系
```
MB_FEE_TYPE (费用类型)
└── MB_FEE_RATE (费用费率) - 通过FEE_TYPE关联
    ├── MB_FEE_MATRIX (费用矩阵) - 通过IRL_SEQ_NO关联
    └── MB_FEE_MAPPING (费用映射) - 通过IRL_SEQ_NO关联
```

## 业务规则与约束

### 1. 产品状态管理
- **产品状态**: A-有效, F-无效, O-未过账, P-已过账
- **产品范围**: B-基础产品, S-可售产品

### 2. 利率计算规则
- **计算方法**: AB-积数计息, EB-分段计息
- **分层规则**: A-按金额分层, P-按周期分层, N-不分层
- **靠档方向**: F-靠下档, C-靠上档

### 3. 费用计算方式
- **计算模式**: F-固定金额, R-固定比例, B-固定金额+比例, S-差额累进, T-全额累进
- **执行时机**: O-联机, B-日终批量

### 4. 汇率类型
- **牌价类型**: D-直接, I-间接
- **浮动方式**: 02-取最大, 03-取最小, 04-取平均值, 05-取叠加, 06-取权重

## 技术特点

### 1. 多法人支持
- 所有核心表都包含COMPANY字段
- 支持多法人系统的数据隔离

### 2. 历史数据管理
- 利率、汇率等关键数据都有历史表
- 支持按时间查询历史数据

### 3. 灵活的参数配置
- 基于事件驱动的参数管理
- 支持表达式和规则引擎

### 4. 分区表设计
- `fw_tran_info`和`mq_consumer_msg`按日期分区
- 提高大数据量下的查询性能

## 性能优化建议

### 1. 索引优化
- 在高频查询字段上建立合适索引
- 复合索引优化多条件查询

### 2. 分区策略
- 对大表考虑按时间分区
- 定期清理历史数据

### 3. 缓存策略
- 基础参数数据适合缓存
- 汇率、利率等实时数据需要及时更新

## 总结

PF模块作为银行核心系统的参数工厂，承担着系统基础参数管理的重要职责。其数据结构设计体现了银行业务的复杂性和严谨性，通过分层的产品管理、灵活的利率汇率机制、完善的费用计算规则，为银行各业务模块提供了强有力的参数支撑。模块设计具有良好的扩展性和维护性，能够适应银行业务的快速发展需求。
