```mermaid
erDiagram
    CIF_CLIENT {
        varchar CLIENT_NO PK "客户号"
        varchar CLIENT_SHORT "客户简称"
        varchar CLIENT_NAME "客户名称"
        varchar CH_CLIENT_NAME "客户中文名称"
        varchar EN_CLIENT_NAME "客户英文名称"
        varchar EN_CLIENT_SHORT "客户英文简称"
        varchar CLIENT_TYPE "客户类型"
        varchar CATEGORY_TYPE "客户细分类型"
        char CLIENT_TRAN_STATUS "客户交易状态"
        varchar CTRL_BRANCH "控制分行"
        varchar ACCT_EXEC "客户经理"
        varchar DEPARTMENT "部门"
        varchar PROFIT_CENTER "利润中心"
        varchar SOURCE_TYPE "渠道类型"
        datetime CREATE_DATE "创建日期"
        varchar COUNTRY_LOC "国籍"
        varchar STATE "省别代码"
        varchar CLIENT_CITY "城市代码"
        varchar DISTRICT "区号"
        varchar POSTAL_CODE "邮政编码"
        varchar ADDRESS "地址"
        char INFO_LACK "资料不全标志"
        char CLIENT_INDICATOR "客户标识"
        char INTERNAL_IND_FLAG "内部客户标志"
        char TEMP_CLIENT "临时客户标志"
        char INLAND_OFFSHORE "境内境外标志"
        char BLACKLIST_IND_FLAG "是否黑名单客户"
        char CIF_VER_FLAG "客户级核实标志"
        char UNPROVIDE_ID_REASON "未提供识别号原因"
        varchar NAME_SUFFIX "客户名后缀"
        varchar CLIENT_MNEMONIC "助记名称"
        varchar CLIENT_ALIAS "别名"
        varchar CLIENT_GRP "客户组"
        varchar COUNTRY_CITIZEN "居住国家"
        varchar COUNTRY_RISK "风险控制国家"
        char IS_FORMAL "弱实名客户标志"
        varchar CLIENT_STATUS "客户状态"
        varchar CLIENT_STATUS_DESC "客户状态描述"
        datetime CLASS_LEVEL_DATE "评级日期"
        varchar TAX_COUNTRY "税收居民国"
        char TAX_FLAG "是否税信息"
        char TAXABLE_IND "收税标志"
        varchar TAX_REMARK "税收备注"
        varchar TAXPAYER_ID "纳税人识别号"
        char INVOICE_TYPE "发票类型"
        char TAX_ORG_TYPE "税收机构类型"
        char TAXPAYER_TYPE "纳税人类型"
        decimal CLIENT_SPREAD "客户浮动"
        char PAY_AGENT_FLAG "代付标志"
        char REC_AGENT_FLAG "代收标志"
        char WRN_FLAG "贷款核销标志"
        char YDT_FLAG "客户级易贷通标志"
        char PEP_IND_FLAG "PEP客户标志"
        varchar INDUSTRY "通用行业代码"
        varchar BUSINESS "行业代码"
        varchar CLASS1 "分类1"
        varchar CLASS2 "分类2"
        varchar CLASS3 "分类3"
        varchar CLASS4 "分类4"
        varchar CLASS5 "分类5"
        varchar CLASS_LEVEL "综合评级"
        varchar CR_RATING "客户信用等级"
        int RISK_WEIGHT "风险权重"
        varchar NARRATIVE "摘要"
        char PRINT_LANGUAGE "打印语言"
        char SPOKEN_LANGUAGE "交流语言"
        char WRITTEN_LANGUAGE "书写语言"
        datetime CLOSED_DATE "关闭日期"
        varchar CLOSE_REASON "注销原因"
        varchar OLD_CLIENT_NO "原客户号"
        varchar CREATION_USER_ID "创建柜员"
        varchar TRAN_TIMESTAMP "交易时间戳"
        datetime LAST_CHANGE_DATE "最后修改日期"
        varchar LAST_CHANGE_TIME "上次修改时间"
        varchar LAST_CHANGE_USER_ID "最后修改柜员"
        varchar COMPANY "法人"
        bigint CLIENT_POINT "客户积分"
        int CONTRIBUTE_DEGREE "贡献度"
        varchar IS_JOINT_CUSTOMER "是否联名客户"
        char TAX_RESIDENT_FLAG "税收居民标识"
        varchar ACCT_VERIFY_STATE "客户核实状态"
        varchar PASSWORD "密码"
        char PASSWORD_STATUS "密码状态"
        int FAILURE_TIMES "累积失败次数"
        char CORP_FLAG "是否小微企业"
        varchar CLIENT_GROUP "客户群组"
        char BRANCH_INNER_FLAG "是否机构内客户标志"
        varchar ANNUAL_STATUS "年检状态"
        datetime NEXT_ANNUAL_DATE "下一年检日期"
        datetime LAST_ANNUAL_DATE "上一年检日期"
        varchar FIRST_NAME "名"
        varchar MID_NAME "中间名"
        varchar LAST_NAME "姓"
    }

    CIF_CLIENT_INDVL {
        varchar CLIENT_NO PK "客户号"
        varchar CH_GIVEN_NAME "中文名"
        varchar CH_SURNAME "中文姓"
        varchar GIVEN_NAME "英文名"
        varchar SURNAME "英文姓"
        varchar NATION "民族"
        varchar RACE "种族"
        char SURNAME_FIRST "是否姓在前"
        varchar MARITAL_STATUS "婚姻状况"
        char SEX "性别"
        varchar MAIDEN_NAME "婚前名"
        varchar OCCUPATION_CODE "职业"
        varchar MAX_DEGREE "最高学位"
        varchar EDUCATION "教育程度编号"
        varchar SALUTATION "称呼"
        varchar PLACE_OF_BIRTH "出生国"
        varchar RESIDENT_TYPE "居住类型"
        char RESIDENT_STATUS "居住状态"
        varchar HOBBY "兴趣爱好"
        varchar POST "职务"
        char QUALIFICATION "专业职称"
        varchar MOTHERS_MAIDEN_NAME "母亲婚前名"
        varchar SALARY_ACCT_BRANCH "工资账户开户行"
        varchar SALARY_ACCT_NO "工资账号"
        varchar INC_PROOF_USER_ID "收入验证人"
        varchar REDCROSS_NO "红十字会员编号"
        char INC_PROOF_IND "收入验证标识"
        datetime INC_PROOF_DATE "收入验证日期"
        varchar SALARY_CCY "薪资币种"
        decimal MON_SALARY "月薪"
        decimal YEARLY_INCOME "年收入"
        varchar MORTGAGE_CCY "抵押币种"
        varchar RENTAL_CCY "租金币种"
        decimal MON_MORTGAGE "月抵押付给金额"
        decimal MON_RENTAL "月租金"
        datetime RESIDENT_DATE "入住日期"
        int CHILD_NUM "孩子人数"
        int DEPENDENT_NUM "供养人数"
        varchar EMPLOYER_INDUSTRY "雇主所在行业"
        varchar EMPLOYER_NAME "工作单位"
        datetime EMPLOYMENT_START_DATE "雇佣开始日期"
        varchar SOCIAL_INSU_NO "社会保险号"
        varchar TRAN_TIMESTAMP "交易时间戳"
        datetime LAST_CHANGE_DATE "最后修改日期"
        varchar LAST_CHANGE_USER_ID "最后修改柜员"
        varchar COMPANY "法人"
        datetime BIRTH_DATE "出生日期"
    }

    CIF_CLIENT_CORP {
        varchar CLIENT_NO PK "客户号"
        varchar CLIENT_NAME "客户名称"
        varchar ORGAN "组织机构代码"
        varchar FIN_APP_CODE "金融机构许可证号"
        varchar INP_EXP_NO "进出口业务经营资格编号"
        char BUSILICENCE_STATUS "营业执照状态"
        varchar CORP_SIZE "企业规模"
        int EMP_NUM "员工数"
        varchar HIGHER_ORGAN "主管单位"
        varchar ORIGIN_COUNTRY "注册国家"
        datetime INCOR_DATE "公司成立日期"
        varchar BASIC_ACCT_OPENAT "基本账户开户行"
        varchar BASE_ACCT_NO "账号/卡号"
        varchar REGISTER_NO_TYPE "登记注册号类型"
        datetime REGISTER_DATE "登记日期"
        datetime CESSATION_DATE "终止日期"
        datetime REP_EXPIRY_DATE "法人代表证件到期日"
        datetime REP_ISS_DATE "法人证件签发日期"
        varchar ECON_TYPE "经济类型"
        varchar LEGAL_REP "法定代表人名称"
        varchar REP_DOCUMENT_TYPE "法定代表人身份证件类型"
        varchar REP_DOCUMENT_ID "法定代表人身份证件号码"
        varchar REP_PHONE "法人代表手机号"
        varchar BUSINESS_SCOPE "经营范围"
        varchar SPECIAL_APP_NO "特殊行业许可证书号"
        char TAX_FLAG "是否税信息"
        varchar TAXPAYER_ADDRESS "纳税人地址"
        char UNPROVIDE_ID_REASON "未提供识别号原因"
        varchar TAXPAYER_NAME "纳税人名称"
        varchar TAXPAYER_ID "纳税人识别号"
        varchar TAX_FILE_NO "国税登记号"
        varchar LOCAL_TAX_FILE_NO "地方税务证号"
        datetime TAX_CER_AVAI "税务证有效期"
        varchar CESSATION "终止类型"
        varchar BANK_ID "银行ID"
        varchar CENTRAL_BANK_REF "中央银行"
        decimal PAID_UP_CAPITAL "实收资本"
        varchar PAID_CAPITAL_CCY "实收资本币种"
        decimal AUTH_CAPITAL "注册资本"
        varchar CAPITAL_CCY "注册资本币种"
        decimal MOODY_RATE "外部浮动利率"
        char PHONE_FAX_FLAG "电话/传真指令指定客户标志"
        char SUC_FLAG "社会统一信用代码标志"
        char COMPANY_SECRETARY_FLAG "是否指定公司秘书"
        char NON_RESIDENT_CTRL_FLAG "运营国外控制标志"
        char REF_INTERMEDIARY_FLAG "中介推崇标志"
        char MINORITY_INTEREST_FLAG "最小控股标志"
        varchar TRAN_EMAIL "交易用EMAIL"
        datetime FORE_REMIT_CER_AVAI "外汇证有效期"
        varchar FITCH "Fitch等级"
        varchar SP_RATE "SP等级"
        varchar LOAN_GRADE "贷方级别"
        varchar REGISTER_NO "登记注册号"
        char EXPOSURE_CAP "风险控制标志"
        varchar LOAN_CARD_ID "该企业贷款使用的贷款卡编码"
        varchar CHECK_YEAR "工商执照年检年份"
        varchar CORP_PLAN "公司计划"
        varchar OFF_WEBSITE "官方网站"
        varchar COUNTRY "国家"
        varchar BORROWER_GRADE "借款人等级"
        varchar ECON_DIST "经济特区"
        varchar MARKET_PARTICIPANT "市场参与者"
        char LENDING_OFFICER_IND "是否指定贷款副负责人"
        char DIRECTOR_IND "是否指定银行负责人"
        varchar OWNERSHIP "所有权"
        varchar INVESTOR "投资人"
        varchar FX_REGISTER_ID "外汇登记证"
        varchar FX_ISS_PLACE "外汇登记证签发地"
        varchar FX_ISS_ORGAN "外汇等级证号"
        varchar FOREIGN_APP_NO "外商投资批准证书号"
        decimal PD "违约机率"
        varchar BANK_CODE "银行代码"
        varchar SWIFT_ID "银行国际代码"
        char SUB_DIRECTOR_IND "指定贷款负责人标志"
        char SUB_LENDING_OFFICER_IND "指定银行副负责人标志"
        char PHONE_FAX_ACCT_FLAG "电话/传真指令指定账户客户标志"
        varchar REMARK "备注"
        varchar LAST_CHANGE_USER_ID "最后修改柜员"
        datetime LAST_CHANGE_DATE "最后修改日期"
        varchar TRAN_TIMESTAMP "交易时间戳"
        varchar COMPANY "法人"
        datetime BIRTH_DATE "出生日期"
    }

    CIF_CLIENT_DOCUMENT {
        varchar CLIENT_NO PK "客户号"
        varchar DOCUMENT_TYPE PK "证件类型"
        varchar DOCUMENT_ID PK "证件号码"
        varchar ISS_COUNTRY PK "发证国家"
        varchar ISS_STATE "签发省、州"
        varchar ISSUE_BRANCH "签发机构"
        varchar ISS_PLACE "签发地"
        varchar ISS_CITY "签发城市"
        datetime ISS_DATE "签发日期"
        datetime MATURITY_DATE "到期日期"
        char PREF_FLAG "首选标志"
        varchar DIST_CODE "地区代码"
        varchar DIST_CODE_DESC "地区代码描述"
        char PASSPORT_TYPE "护照类型"
        varchar NEW_DOCUMENT_ID "变更后证件号码"
        varchar OTHER_DOCUMENT "其他证件名称"
        datetime INSPECT_DATE "上次核查日期"
        datetime LAST_CHANGE_DATE "最后修改日期"
        varchar LAST_CHANGE_USER_ID "最后修改柜员"
        varchar TRAN_TIMESTAMP "交易时间戳"
        varchar COMPANY "法人"
        varchar visa_type "签证类型"
    }

    CIF_CLIENT_CONTACT_TBL {
        varchar CLIENT_NO PK "客户号"
        varchar CONTACT_TYPE PK "联系类型"
        varchar CITY_TEL "电话区号"
        varchar CONTACT_TEL "联系电话"
        varchar MOBILE_PHONE "移动电话"
        varchar COUNTRY "国家"
        varchar COUNTRY_TEL "国家电话区号"
        varchar STATE "省别代码"
        varchar CITY "城市"
        varchar CITY_DIST "区/县"
        varchar POSTAL_CODE "邮政编码"
        char PREF_FLAG "首选标志"
        varchar ADDRESS_ID "地址编号"
        varchar ADDRESS "地址"
        varchar ADDRESS1 "地址1"
        varchar ADDRESS2 "地址2"
        varchar ADDRESS3 "地址3"
        varchar ADDRESS4 "地址4"
        varchar ROUTE "联系方式类型"
        char PHONE_REPEAT_DEAL "客户移动电话手机号重复处理标识"
        datetime CONTACT_TEL_UPDATE_DATE "联系电话更新日期"
        varchar LINKMAN "对账联系人"
        varchar SALUTATION "称呼"
        varchar BIC_CODE "BIC代码"
        datetime LAST_CHANGE_DATE "最后修改日期"
        varchar LAST_CHANGE_USER_ID "最后修改柜员"
        varchar TRAN_TIMESTAMP "交易时间戳"
        varchar COMPANY "法人"
        varchar EMAIL "电子邮件"
        varchar RT "地区编号"
        varchar RW "地区编码"
        varchar SUB_DISTRICT "子区"
        varchar DESPATCH_CODE "发送代码"
        varchar MOBILE_PHONE2 "移动电话2"
    }

    CIF_CLIENT_VERIFICATION {
        varchar CLIENT_NO PK "客户号"
        varchar JOB_RUN_ID "批处理任务ID"
        varchar BATCH_NO "批次号"
        varchar SEQ_NO "序号"
        datetime VERIFICATION_DATE "核实日期"
        varchar VERIFICATION_BRANCH "核查机构"
        varchar VERIFICATION_SOURCE_TYPE "核查渠道"
        varchar VERIFICATION_RESULT "核查结果"
        varchar VERIFICATION_USER_ID "核实柜员"
        char VERIFY_STATUS "核查状态"
        varchar UNVERIFICATION_REASON "无法核实原因"
        char IS_SAVE "留存标志"
        varchar TREATMENT "处置方式"
        varchar REMARK "备注"
        varchar RET_CODE "状态码"
        varchar RET_MSG "服务状态描述"
        varchar TRAN_TIMESTAMP "交易时间戳"
        varchar COMPANY "法人"
    }

    CIF_CLIENT_NRA {
        varchar CLIENT_NO PK "客户号"
        char SELF_STATEMENT "取得自证声明标志"
        char NEG_NON_FIN_FLAG "消极非金融标识"
        char IDENTIFY_FLAG "是否免于识别"
        varchar NRA_ID_TAX "非居民纳税人识别号"
        char NRA_ID_TAX_RES "非居民未提供纳税人识别号原因"
        varchar CON_ID_TAX "控制人纳税人识别号"
        varchar CON_ID_TAX_RES "控制人纳税人识别原因"
        char CON_RESIDENT_FLAG "控制人居民标识"
        varchar CON_NAME_CH "控制人姓名（中文）"
        varchar CON_NAME_EN "控制人姓名（英文）"
        datetime CON_BITRHDAY "控制人出生日期"
        varchar CON_BIRTH_CH "控制人出生地（中文）"
        varchar CON_BIRTH_EN "控制人出生地（英文）"
        varchar CON_COUNTRY "控制人居民国（地区）"
        varchar CON_NOW_ADDRESS_CH "控制人现居地址（中文）"
        varchar CON_NOW_ADDRESS_EN "控制人现居地址（英文）"
        char RESIDENT_FLAG "居民标识"
        varchar NRA_BIRTH_CH "非居民出生地（中文）"
        varchar NRA_BIRTH_EN "非居民出生地（英文）"
        varchar NRA_COUNTRY "非居民居民国（地区）"
        varchar NRA_NOW_ADDRESS_CH "非居民现居地址（中文）"
        varchar NRA_NOW_ADDRESS_EN "非居民现居地址（英文）"
        datetime NRA_BITRHDAY "非居民出生日期"
        varchar TRAN_TIMESTAMP "交易时间戳"
        varchar COMPANY "法人"
    }

    CIF_CLIENT_NRA_DETAIL {
        varchar CLIENT_NO PK "客户号"
        varchar NRA_COUNTRY PK "非居民居民国（地区）"
        varchar NRA_ID_TAX "非居民纳税人识别号"
        varchar NRA_ID_TAX_DETRES "未取得纳税人识别号具体原因"
        char NRA_ID_TAX_RES "非居民未提供纳税人识别号原因"
        varchar TRAN_TIMESTAMP "交易时间戳"
        varchar COMPANY "法人"
    }

    CIF_CLIENT_ATTACH {
        varchar CLIENT_NO PK "客户号"
        varchar MOTHERS_NAME "母亲姓名"
        varchar KYC_FLAG "kyc标志"
        char NPWP_FLAG "税务标志"
        varchar NPWP_NUMBER "税务编号"
        varchar SOURCE_OF_INCOME "收入来源"
        varchar SPOUSE_NAME "配偶姓名"
        varchar SPOUSE_DOCUMENT_ID "配偶证件号码"
        varchar SPOUSE_DATE_OF_BIRTH "配偶生日"
        char SPOUSE_REAL_ESTATE_CONTRACT "配偶房产合同"
        varchar ACCOUNT_OPEN_PURPOSE "开户原因"
        varchar RELATIONSHIP_TO_BANK "是否关联"
        varchar DEBTOR_CATEGORY "借款人类别"
        char PRENUPIAL_AGREEMENT "婚前协议"
        varchar AML_LAST_REVIEW_DATE "AML最后核查时间"
        varchar AML_NEXT_REVIEW_DATE "AML下次核查时间"
        varchar RISK_SCORE "AML风险评分"
        varchar PEP "AML政要级别"
        varchar RISK_CLASSIFICATIONS "风险分类"
        datetime RISK_RATING_REVIEW_DATE "风险评级审查日期"
        datetime RISK_RATING_EXPIRY_DATE "风险评级有效日期"
        varchar TRAN_TIMESTAMP "交易时间戳"
        varchar COMPANY "法人"
    }

    CIF_CONTACT_LIST {
        varchar CLIENT_NO PK "客户号"
        varchar LINKMAN_TYPE PK "联系人类型"
        varchar LINKMAN_NAME "联系人名称"
        varchar DOCUMENT_ID "证件号码"
        varchar DOCUMENT_TYPE "证件类型"
        varchar PHONE_NO1 "电话号码1"
        varchar PHONE_NO2 "联系人电话2"
        datetime LAST_CHANGE_DATE "最后修改日期"
        varchar LAST_CHANGE_USER_ID "最后修改柜员"
        varchar TRAN_TIMESTAMP "交易时间戳"
        varchar COMPANY "法人"
    }

    CIF_CROSS_RELATIONS {
        varchar CLIENT_A PK "客户A"
        varchar CLIENT_B PK "客户B"
        varchar RELATION_TYPE PK "关系类型"
        varchar CLIENT_NO PK "客户号"
        varchar INVERSE_RELA_TYPE "反向关系类型"
        varchar INVERSE_RELA_DESC "反向关系描述"
        datetime CREATE_DATE "创建日期"
        char RELATION_STATUS "关系状态"
        bigint CLIENT_KEY "关系客户内部键"
        varchar CLIENT_SHORT "客户简称"
        datetime APPROVE_DATE "批准日期"
        datetime DECLARE_DATE "宣告日期"
        char RELA_TYPE "关联方类型"
        varchar RELA_DOCUMENT_TYPE "关联方证件类型"
        varchar RELA_DOCUMENT_ID "关联方证件号"
        datetime RELA_BIRTH_DATE "关联方出生日期"
        varchar RE_CLIENT_NAME "关联方中文名称"
        varchar RE_EN_CLIENT_NAME "关联方英文名称"
        char RELA_SEX "关联方性别"
        varchar RELA_COUNTRY "关联方国籍"
        varchar RELA_EDUCATION "关联方学历"
        varchar RELA_TEL "关联方联系电话"
        varchar RELA_EMPLOYER_ADR "关联方单位地址"
        varchar RELA_EMPLOYER_NM "关联方单位名称"
        varchar RELA_EMPLOYER_TEL "关联方单位电话"
        varchar RELA_ORG_CODE "关联方组织机构代码"
        varchar RELA_REG_CODE "关联方登记注册代码"
        varchar RELA_CREDIT_CODE "关联方信用代码"
        varchar RELA_LOAN_CARD "关联方贷款编号"
        varchar ASSESS_NETVAL_CCY "评定净值币种"
        decimal EVAL_NETVAL "估价净值"
        varchar EVAL_NETVAL_CCY "估价净值币种"
        decimal NOTICE_NETVAL "公告净值"
        varchar NOTICE_NETVAL_CCY "公告净值币种"
        decimal ASSESS_NETVAL "评定净值"
        decimal EQUITY_PERCENT "企业占股比例"
        datetime CLOSED_DATE "关闭日期"
        varchar CREATION_USER_ID "创建柜员"
        varchar TRAN_TIMESTAMP "交易时间戳"
        datetime LAST_CHANGE_DATE "最后修改日期"
        varchar LAST_CHANGE_USER_ID "最后修改柜员"
        varchar COMPANY "法人"
    }

    CIF_CONTROL_CONTACT {
        varchar CLIENT_NO PK "客户号"
        char CONTACT_ADDRESS_TYPE PK "联系地址类型"
        varchar CONTACT_TEL "联系电话"
        varchar COUNTRY "国家"
        varchar STATE "省别代码"
        varchar CITY "城市"
        varchar TRAN_TIMESTAMP "交易时间戳"
        varchar COMPANY "法人"
    }

    CIF_CROP_COMPNAY_INFO {
        varchar CLIENT_NO PK "客户号"
        varchar COMPNAY_GOVERNOR PK "法人/财务主管"
        varchar COMPANY_GOVERNOR_NAME "法人/财务主管名称"
        varchar DOCUMENT_ID "证件号码"
        varchar DOCUMENT_TYPE "证件类型"
        datetime MATURITY_DATE "到期日期"
        varchar TRAN_TIMESTAMP "交易时间戳"
        varchar COMPANY "法人"
    }

    CIF_CLIENT_INFORMATION {
        varchar CLIENT_NO PK "客户号"
        varchar CLIENT_FULL_NAME "客户全名称"
        varchar SWIFT_NAME "贸易金融名称"
        decimal A_BASE_RATIO "借贷双方异常基准"
        char INVEST_SERVICE_FLAG "投资服务同意书标志"
        varchar PCP_GROUP_ID "资金池账户组ID"
        varchar GROUP_NAME "账户组名称"
        varchar ACCT_EXEC_CODE "客户经理代码"
        varchar RAINTG "评级结果"
        varchar MANAGE_CONTENT "监管内容"
        varchar FTF_FLAG "个人非居民标志"
        char INTRA_GROUP_FLAG "跨群组标志"
        char SPECIAL_RATE_FLAG "特殊利率"
        varchar AREA_NAME "区域名称"
        varchar MODE_OF_INFORMATION "经营模式"
        varchar FORM_OF_COMPANY "公司形式"
        varchar CON_OF_SUPPLE "供应商集中度"
        varchar CON_OF_CUST "客户集中度"
        datetime START_DATE "开始日期"
        char ONLINE_SETTLE_FLAG "线上清算标志"
        char LIA_INDICATOR_FLAG "诉讼标志"
        char TEL_CHECKER_FLAG "恐怖组织标志"
        char CORE_MARKET_FLAG "核心市场参与者"
        varchar CONTRA_TYPE "对手方类型"
        varchar NAME_OF_EXCHANGE "交易所名称"
        varchar NUMBER_OF_BRANCH "分行数量"
        varchar EXT_RATING_NORMAL "外部评级标普"
        varchar EXT_RATING_MOD "外部评级穆迪"
        varchar INNER_RAINTG "内部评级"
        varchar SOC_TYPE "主权类型"
        char GOVERNMENT_RE "政府关联标志"
        decimal ORIGINAL_START "初始违约概率"
        decimal ORIGINAL "违约概率"
        varchar PLACE_OF_OPERATION "业务开展地"
        varchar PLACE_WHERE_BUSI "业务建立地"
        varchar BUSINESS_PARTNER "业务合作伙伴"
        varchar COUNTRY_CODE "国家代码"
        char BLANK_SHARE "不记名股票"
        char THREE_OR_LAYER "三层或三层以上股权结构"
        char COMPANY_OWN_INDIVIDUAL "由个人全资拥有的公司"
        char CORPORATE_SHARE "拥有海外个人公司股东的本地公司"
        char OWNERSHIP_STRUCTURE "涉及三个或三个以上注册地的所有权结构"
        char TRUST_IN_UNCHAIN "所有权链中的信托"
        char FOR_IN_UNCHAIN "所有权链中的基金会"
        char UNRES_INVEST_FUND "不受监管的投资基金"
        char SHARED_UNCHAIN "所有权链中的提名股东"
        char DIRECT_UNCHAIN "所有权链中提名董事"
        char CLIENT "客户"
        char NO_INTERFACE_CLIENT "非面对面客户"
        char CUSTOMER_DUE "经批准的中介机构介绍"
        char CON_LENDING_PARTY "关联贷款方"
        varchar ORIGINAL_GRADE "撒"
        varchar GUARANTY_ASSETS_CLASS "担保人资产等级"
        varchar TRAN_TIMESTAMP "交易时间戳"
        varchar PEP_CLOSE "政治公众人物亲密合作方"
        varchar PEP_COUNTRY "政治公众人物所在国家"
        varchar LAST_NAME "姓"
        varchar FIRST_NAME "名"
        varchar PEP_RISK "政治任务风险"
        char EMPLOYER_FLAG "员工标志"
        varchar MID_NAME "中间名"
        varchar PEP_INTER "政治公众人物所在国际组织"
        varchar PEP_CATEGORY "政治公众人物细类"
    }

    CIF_DEFINE_COLUMN_INFORMATION {
        varchar CLIENT_NO PK "客户号"
        varchar COLUMN_CODE PK "字段编码"
        varchar COLUMN_NAME "列名"
        varchar COLUMN_VALUE "取值范围"
        varchar COLUMN_TYPE "字段类型"
        varchar COLUMN_LENGTH "字段长度"
        varchar APPLICATION_FLAG "应用标志"
        varchar TRAN_TIMESTAMP "交易时间戳"
    }

    %% 关系定义
    CIF_CLIENT ||--o{ CIF_CLIENT_INDVL : "个人客户信息"
    CIF_CLIENT ||--o{ CIF_CLIENT_CORP : "法人客户信息"
    CIF_CLIENT ||--o{ CIF_CLIENT_DOCUMENT : "客户证件信息"
    CIF_CLIENT ||--o{ CIF_CLIENT_CONTACT_TBL : "客户联系信息"
    CIF_CLIENT ||--o{ CIF_CLIENT_VERIFICATION : "客户核实信息"
    CIF_CLIENT ||--o{ CIF_CLIENT_NRA : "非居民涉税信息"
    CIF_CLIENT ||--o{ CIF_CLIENT_NRA_DETAIL : "非居民涉税详细信息"
    CIF_CLIENT ||--o{ CIF_CLIENT_ATTACH : "客户附属信息"
    CIF_CLIENT ||--o{ CIF_CONTACT_LIST : "客户联系人"
    CIF_CLIENT ||--o{ CIF_CROSS_RELATIONS : "客户关系"
    CIF_CLIENT ||--o{ CIF_CONTROL_CONTACT : "控制人联系信息"
    CIF_CLIENT ||--o{ CIF_CROP_COMPNAY_INFO : "法人/财务主管信息"
    CIF_CLIENT ||--o{ CIF_CLIENT_INFORMATION : "客户扩展信息"
    CIF_CLIENT ||--o{ CIF_DEFINE_COLUMN_INFORMATION : "自定义客户信息"
    CIF_CLIENT_NRA ||--o{ CIF_CLIENT_NRA_DETAIL : "非居民涉税详细信息"
```