
--
-- Table structure for table `batch_online_check`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_online_check` (
  `JOB_ID` varchar(50) NOT NULL COMMENT 'JOB_ID',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'JOB_RUN_ID',
  `CHANNEL_SEQ_NO` varchar(50) NOT NULL COMMENT '渠道流水号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号',
  `OPERATION_ID` varchar(50) DEFAULT NULL COMMENT 'sonic执行序号',
  `STEP_TYPE` varchar(50) NOT NULL COMMENT '业务类型',
  `STEP_DESC` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `FILE_TYPE` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `PROCESS_TYPE` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `TRAN_DATE` date NOT NULL COMMENT '交易日期',
  `RUN_DATE` date DEFAULT NULL COMMENT '交易日期',
  `FILE_NAME` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_PATH` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_MD5` varchar(255) DEFAULT NULL COMMENT '文件MD5校验值',
  `START_TIME` datetime DEFAULT NULL COMMENT '流程开始时间',
  `END_TIME` datetime DEFAULT NULL COMMENT '流程结束时间',
  `TOTAL_NUMBER` bigint DEFAULT NULL COMMENT '文件总笔数',
  `SUCCESS_NUMBER` bigint DEFAULT NULL COMMENT '成功笔数',
  `FAILURE_NUMBER` bigint DEFAULT NULL COMMENT '失败笔数',
  `TRAN_STATUS` varchar(16) DEFAULT NULL COMMENT '业务处理状态 S成功 F失败 P处理中',
  `ERROR_CODE` varchar(6) DEFAULT NULL COMMENT '交易状态码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '交易状态描述信息',
  `HOST_IP` varchar(16) DEFAULT NULL COMMENT 'IP',
  `BATCH_CLASS` varchar(10) DEFAULT NULL COMMENT '批量交易类型',
  `BRANCH_ID` varchar(20) DEFAULT NULL COMMENT '机构代码',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道',
  `SYS_HEAD` blob COMMENT '联机调批量请求头信息',
  `APP_HEAD` blob COMMENT '联机调批量应用头信息',
  `ATTR_JSON` blob COMMENT '联机请求报文',
  PRIMARY KEY (`BATCH_NO`,`STEP_TYPE`,`CHANNEL_SEQ_NO`,`JOB_ID`,`TRAN_DATE`) USING BTREE,
  KEY `idx1_check` (`BATCH_NO`,`STEP_TYPE`,`PROCESS_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='文件登记表(UPRGHT)';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `batch_online_check`
--

LOCK TABLES `batch_online_check` WRITE;
/*!40000 ALTER TABLE `batch_online_check` DISABLE KEYS */;
/*!40000 ALTER TABLE `batch_online_check` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `batch_online_check_details`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_online_check_details` (
  `JOB_ID` varchar(50) NOT NULL COMMENT 'JOB_ID',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'JOB_RUN_ID',
  `STEP_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'STEP_RUN_ID',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号',
  `OPERATION_ID` varchar(50) DEFAULT NULL COMMENT 'sonic执行序号',
  `STEP_TYPE` varchar(50) NOT NULL COMMENT '业务类型',
  `FILE_TYPE` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `PROCESS_TYPE` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `TRAN_DATE` date NOT NULL COMMENT '交易日期',
  `RUN_DATE` date DEFAULT NULL COMMENT '交易日期',
  `FILE_NAME` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_PATH` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_MD5` varchar(255) DEFAULT NULL COMMENT '文件MD5校验值',
  `START_TIME` datetime DEFAULT NULL COMMENT '流程开始时间',
  `END_TIME` datetime DEFAULT NULL COMMENT '流程结束时间',
  `TOTAL_NUMBER` bigint DEFAULT NULL COMMENT '文件总笔数',
  `SUCCESS_NUMBER` bigint DEFAULT NULL COMMENT '成功笔数',
  `FAILURE_NUMBER` bigint DEFAULT NULL COMMENT '失败笔数',
  `TRAN_STATUS` varchar(16) DEFAULT NULL COMMENT '业务处理状态 S:成功 F:失败 P 处理中',
  `ERROR_CODE` varchar(6) DEFAULT NULL COMMENT '交易状态码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '交易状态描述信息',
  `HOST_IP` varchar(16) DEFAULT NULL COMMENT 'IP',
  PRIMARY KEY (`JOB_ID`,`BATCH_NO`,`STEP_TYPE`,`TRAN_DATE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='文件登记表明细(UPRGHT)';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `batch_online_check_details`
--

LOCK TABLES `batch_online_check_details` WRITE;
/*!40000 ALTER TABLE `batch_online_check_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `batch_online_check_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `batch_online_upload`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_online_upload` (
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'JOB_RUN_ID',
  `STEP_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'STEP_RUN_ID',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号',
  `STEP_TYPE` varchar(50) NOT NULL COMMENT '业务类型',
  `FILE_NAME` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_PATH` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `UPLOAD_PATH` varchar(255) DEFAULT NULL COMMENT '上传文件路径',
  `BATCH_CLASS` varchar(10) DEFAULT NULL COMMENT '批量交易类型',
  `BRANCH_ID` varchar(20) DEFAULT NULL COMMENT '机构代码',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道',
  PRIMARY KEY (`BATCH_NO`) USING BTREE,
  KEY `idx1_upload` (`BATCH_NO`),
  KEY `idx2_upload` (`BATCH_NO`,`STEP_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量文件上传登记表(UPRGHT)';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `batch_online_upload`
--

LOCK TABLES `batch_online_upload` WRITE;
/*!40000 ALTER TABLE `batch_online_upload` DISABLE KEYS */;
/*!40000 ALTER TABLE `batch_online_upload` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `flyway_schema_history`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `flyway_schema_history` (
  `installed_rank` int NOT NULL,
  `version` varchar(50) DEFAULT NULL,
  `description` varchar(200) NOT NULL,
  `type` varchar(20) NOT NULL,
  `script` varchar(1000) NOT NULL,
  `checksum` int DEFAULT NULL,
  `installed_by` varchar(100) NOT NULL,
  `installed_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `execution_time` int NOT NULL,
  `success` tinyint(1) NOT NULL,
  PRIMARY KEY (`installed_rank`),
  KEY `flyway_schema_history_s_idx` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `flyway_schema_history`
--


--
-- Table structure for table `fm_acct_exec`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_acct_exec` (
  `ACCT_EXEC` varchar(30) NOT NULL COMMENT '客户经理|客户经理',
  `ACCT_EXEC_NAME` varchar(200) NOT NULL COMMENT '客户经理姓名|客户经理姓名',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `ISS_COUNTRY` varchar(3) DEFAULT NULL COMMENT '发证国家|发证国家',
  `ACCT_EXEC_TYPE` varchar(3) DEFAULT NULL COMMENT '客户经理类型|客户经理类型',
  `ACCT_EXEC_STATUS` char(1) DEFAULT NULL COMMENT '客户经理状态|客户经理状态|A-活动,C-失效',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `COLLAT_MGR_IND` char(1) DEFAULT NULL COMMENT '是否担保经理|是否担保经理|Y-是,N-否',
  `CONTACT_ID` varchar(100) DEFAULT NULL COMMENT '联系类型ID|联系类型',
  `CONTACT_TEL` varchar(50) DEFAULT NULL COMMENT '联系电话  |联系电话  ',
  `MANAGER` varchar(30) DEFAULT NULL COMMENT '主管经理|主管经理',
  `PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '利润中心 |利润中心 ',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`ACCT_EXEC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户经理表|客户经理表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_acct_exec`
--

LOCK TABLES `fm_acct_exec` WRITE;
/*!40000 ALTER TABLE `fm_acct_exec` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_acct_exec` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_backup_clean_param`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_backup_clean_param` (
  `BASE_TABLE_NAME` varchar(100) NOT NULL COMMENT '业务表|业务表',
  `HIST_TABLE_NAME` varchar(100) DEFAULT NULL COMMENT '历史表|历史表',
  `FREQUENCY` varchar(20) DEFAULT NULL COMMENT '对应频率|对应频率',
  `NEXT_DATE` datetime DEFAULT NULL COMMENT '下一日期|下一处理日期',
  `BACKUP_TYPE` varchar(10) DEFAULT NULL COMMENT '备份类型（01-分区备份、02-条件备份、03-不备份）|备份类型（01-分区备份、02-条件备份、03-不备份）',
  `BACKUP_SEGMENT_FLAG` varchar(10) DEFAULT NULL COMMENT '备份分段(Y-是、N-否)|备份分段(Y-是、N-否)',
  `CLEAN_TYPE` varchar(10) DEFAULT NULL COMMENT '清理类型(01-分区清理、02-条件清理、03-不清理）|清理类型(01-分区清理、02-条件清理、03-不清理）',
  `CLEAN_SEGMENT_FLAG` varchar(10) DEFAULT NULL COMMENT '清理分段(Y-是、N-否)|清理分段(Y-是、N-否)',
  `FIELD_NAME` varchar(50) DEFAULT NULL COMMENT '字段名|字段名',
  `SEGMENT_SIZE` int DEFAULT NULL COMMENT '分段大小|分段大小',
  `BACKUP_SQL_ID` varchar(50) DEFAULT NULL COMMENT '备份SQLID|备份SQLID',
  `CLEAN_SQL_ID` varchar(50) DEFAULT NULL COMMENT '清理SQLID|清理SQLID',
  `BACKUP_STATUS` varchar(10) DEFAULT NULL COMMENT '备份状态(R – 备份中  E –备份结束  N-未开始)|备份状态(R – 备份中  E –备份结束)',
  `CLEAN_STATUS` varchar(2) DEFAULT NULL COMMENT '结清标志|结清标志',
  `BACKUP_START_TIME` datetime DEFAULT NULL COMMENT '备份起始时间|备份起始时间',
  `BACKUP_END_TIME` datetime DEFAULT NULL COMMENT '备份结束时间|备份结束时间',
  `CLEAN_START_TIME` datetime DEFAULT NULL COMMENT '清理起始时间|清理起始时间',
  `CLEAN_END_TIME` datetime DEFAULT NULL COMMENT '清理结束时间|清理结束时间',
  `BACKUP_SEGMENT_ID` varchar(100) DEFAULT NULL COMMENT '备份分段ID|备份分段ID',
  `CLEAN_SEGMENT_ID` varchar(100) DEFAULT NULL COMMENT '清理分段ID|清理分段ID',
  `CLASS_NAME` varchar(200) DEFAULT NULL COMMENT '分段映射实体类名|分段映射实体类名',
  `RETAIN_TIME_LIMIT` varchar(20) DEFAULT NULL COMMENT '保留期限|保留期限',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BASE_TABLE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='备份清理参数配置表|配置核心数据库大表备份和清理的周期，处理方式，留存期限等参数';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_backup_clean_param`
--

LOCK TABLES `fm_backup_clean_param` WRITE;
/*!40000 ALTER TABLE `fm_backup_clean_param` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_backup_clean_param` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_bank`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_bank` (
  `BANK_CODE` varchar(20) NOT NULL COMMENT '银行代码|在同一个表中两个不同字段的中文注释都为银行代码',
  `BANK_TYPE` char(1) NOT NULL COMMENT '银行类别|银行类别|O-我行,B-他行,S-建房互助协会,T-第三方银行',
  `BANK_NAME` varchar(50) NOT NULL COMMENT '银行名称|银行名称',
  `STATUS` char(1) NOT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BANK_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='外部金融机构定义表|外部金融机构定义表，核心暂未使用';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_bank`
--

LOCK TABLES `fm_bank` WRITE;
/*!40000 ALTER TABLE `fm_bank` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_bank` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_bank_hierarchy`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_bank_hierarchy` (
  `HIERARCHY_CODE` varchar(50) NOT NULL COMMENT '层级代码|层级代码',
  `HIERARCHY_NAME` varchar(50) DEFAULT NULL COMMENT '层级说明|层级说明',
  `HIERARCHY_LEVEL` varchar(10) DEFAULT NULL COMMENT '分行级别|分行级别',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`HIERARCHY_CODE`),
  KEY `IDX_FM_BANK_HIERARCHY_2` (`HIERARCHY_LEVEL`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构层次表|机构层次表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_bank_hierarchy`
--

LOCK TABLES `fm_bank_hierarchy` WRITE;
/*!40000 ALTER TABLE `fm_bank_hierarchy` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_bank_hierarchy` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_branch`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_branch` (
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `BRANCH_NAME` varchar(200) NOT NULL COMMENT '机构名称|机构名称',
  `BRANCH_SHORT` varchar(30) NOT NULL COMMENT '机构简称|机构简称',
  `BRANCH_TYPE` char(1) NOT NULL COMMENT '机构类型|区分机构是行内还是行外，目前默认为I-本行机构|I-本行,O-他行',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_BR_IND` char(1) DEFAULT NULL COMMENT '是否交易机构|只有交易行才能开门办理业务，接收上送报文请求，否则只能在核算和报表场景中使用；已被撤并的机构此字段也登记为N|Y-交易行,N-非交易行',
  `INTERNAL_CLIENT` varchar(20) DEFAULT NULL COMMENT '内部客户号|默认按机构编号在cif应用注册一套客户信息，用户核心系统内部业务场景使用，ecif无需管控，有OM初始化导入，后续机构新增时也会自动创建一套客户信息',
  `HIERARCHY_CODE` varchar(50) DEFAULT NULL COMMENT '层级代码|机构的总分支级别',
  `ATTACHED_TO` varchar(50) DEFAULT NULL COMMENT '所属上级|机构的上级管理机构',
  `SUB_BRANCH_CODE` varchar(50) DEFAULT NULL COMMENT '分行代码|默认配置机构编号前三位，业务系统实际暂未使用',
  `BRANCH_STATUS` char(1) DEFAULT NULL COMMENT '机构开关门状态|机构开关门状态|I-开门,O-关门,X-已撤并',
  `FTA_FLAG` char(1) DEFAULT NULL COMMENT '是否自贸区机构|是否自贸区机构|Y-是,N-否',
  `FTA_CODE` varchar(10) DEFAULT NULL COMMENT '自贸区代码|自贸区代码',
  `LOCAL_CCY` varchar(3) DEFAULT NULL COMMENT '当地币种|机构当地默认的币种，一般为人民币',
  `BASE_CCY` varchar(3) DEFAULT NULL COMMENT '基础币种|机构在系统中的默认基础币种，一般为人民币',
  `CCY_CTRL_BRANCH` varchar(50) DEFAULT NULL COMMENT '结售汇平盘机构|结售汇平盘机构',
  `CHEQUE_ISSUING_BRANCH_FLAG` char(1) DEFAULT NULL COMMENT '是否签发支票|机构是否允许签发出售支票|Y-支票发行行,N-非支票发行',
  `INT_TAX_LEVY` char(1) NOT NULL COMMENT '利息税征收标志|利息税征收标志|Y-是,N-否',
  `TAX_RPT_BRANCH` varchar(50) DEFAULT NULL COMMENT '税收机构（总账用）|税收机构（总账用）',
  `SURTAX_TYPE` varchar(30) DEFAULT NULL COMMENT '附加税类型|附加税的税率类型',
  `PBOC_FUND_CHECK_FLAG` char(1) DEFAULT NULL COMMENT '人行备付金检查标志|是否检查人行备付金余额，目前系统暂未使用|Y-检查余额,N-不检查余额',
  `PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '利润中心 |利润中心',
  `IP_ADDR` varchar(200) DEFAULT NULL COMMENT '机构IP地址|机构下所有终端的IP地址，英文逗号分隔，目前系统暂未使用',
  `COUNTRY` varchar(3) NOT NULL COMMENT '国家|国家',
  `STATE` varchar(10) NOT NULL COMMENT '省别代码|省、州',
  `CITY` varchar(10) DEFAULT NULL COMMENT '城市|城市',
  `POSTAL_CODE` varchar(10) DEFAULT NULL COMMENT '邮政编码|邮政编码',
  `DISTRICT` varchar(10) DEFAULT NULL COMMENT '区号|区号',
  `AREA_CODE` varchar(5) DEFAULT NULL COMMENT '地区码|地区码',
  `CNY_BUSINESS_UNIT` varchar(10) DEFAULT NULL COMMENT '账套(人民币)|账套(人民币)，目前系统暂未使用',
  `HKD_BUSINESS_UNIT` varchar(10) DEFAULT NULL COMMENT '账套(港币)|账套(港币)，目前系统暂未使用',
  `FX_ORGAN_CODE` varchar(10) DEFAULT NULL COMMENT '外汇金融机构代码|外汇金融机构代码，目前系统暂未使用',
  `CREATE_DATE` datetime DEFAULT NULL COMMENT '创建日期|机构成立创建的日期，不大于正式开门营业的日期，仅登记',
  `START_DATE` datetime NOT NULL COMMENT '开始日期|机构参数启用开始日期，也是机构正式开门营业的日期',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|机构参数失效日期，为空默认永不失效，一般在机构撤并时会给被撤并机构设置此日期',
  `EOD_FLAG` char(1) DEFAULT NULL COMMENT '日终标识|表示当前机构是否处于日终状态，目前系统暂未使用|Y-日终状态,N-营业状态',
  `DEFAULT_TELLER_LOGIN` char(1) NOT NULL COMMENT '默认柜员登录认证方式|当前机构下柜员新增时默认的登录方式，目前系统暂未使用|1-密码,2-指纹,3-密码+指纹',
  `ABNORMAL_OPEN_CONTROL` char(1) DEFAULT NULL COMMENT '非正常时间开门控制方式|定义机构在非正常时间开门时的控制方式，目前系统暂未使用|1-不控制,2-拒绝,3-提醒',
  `OPER_MAX_LEVEL` varchar(5) DEFAULT NULL COMMENT '操作最高级别|目前系统暂未使用',
  `AUTH_FLAG` char(1) DEFAULT NULL COMMENT '授权标志|用于机构二次开门时，需要先授权，授权后此标志修改为Y，开门接口就可以正常开门了，目前系统暂未使用|Y-已授权,N-未授权',
  `TAILBOX_DETACH_FLAG` char(1) NOT NULL COMMENT '尾箱控制方式|柜员正式签退和网点日终时，对于没有脱离绑定关系的尾箱，根据此配置默认进行脱离处理|Y-必须上缴,N-不上缴,O-不控制',
  `VOUCHER_USER_CONTRAL_FLAG` char(1) DEFAULT NULL COMMENT '是否限制凭证入库柜员|行外和机构间凭证出入库时，是否必须由凭证库管操作|Y-限制,N-不限制',
  `ACCOUNTING_BRANCH_FLAG` char(1) DEFAULT NULL COMMENT '是否核算机构|当前机构是否是核算机构',
  `ACCOUNTING_BRANCH` varchar(50) DEFAULT NULL COMMENT '核算机构|当前机构对应的核算机构编号，可以是自己',
  `AUTO_CREATE_INTERNAL_ACCT_FLAG` char(1) DEFAULT NULL COMMENT '自动开立内部户标志|自动开立内部户标志|Y-是,N-否',
  `NORMAL_OPEN_TIME` varchar(8) DEFAULT NULL COMMENT '正常开门时间|配置机构在此时间之前不能对外开门营业，对应JAVA类型：HH:mm:ss，举例：13:25:45，目前系统暂未使用',
  `NORMAL_CLOSE_TIME` varchar(8) DEFAULT NULL COMMENT '正常关门时间|配置机构在此时间后不能再对外营业，对应JAVA类型：HH:mm:ss，举例：13:25:45，目前系统暂未使用',
  `CITY_BRANCH_FLAG` char(1) NOT NULL COMMENT '市区支行标志|市区支行标志|Y-是,N-否',
  `PBOC_FINANCING_NO` varchar(30) DEFAULT NULL COMMENT '人行金融机构编码|人行金融机构编码，目前系统暂未使用',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `bic_code` varchar(11) DEFAULT NULL,
  PRIMARY KEY (`BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构信息表|机构信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_branch`
--


--
-- Table structure for table `fm_branch_ccy`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_branch_ccy` (
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BRANCH`,`CCY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构币种表|机构币种表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_branch_ccy`
--


--
-- Table structure for table `fm_branch_check_def`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_branch_check_def` (
  `BRANCH_CHECK_TYPE` varchar(10) NOT NULL COMMENT '机构检查定义类检查类型|机构检查定义类检查类型|close-关门,change-机构撤并',
  `BRANCH_CHECK_ITEM` varchar(50) NOT NULL COMMENT '机构检查事项|机构检查事项',
  `BRANCH_CHECK_ITEM_DESC` varchar(50) NOT NULL COMMENT '机构检查事项描述|机构检查事项描述',
  `BRANCH_CHECK_ITEM_STATUS` char(1) NOT NULL COMMENT '机构检查事项状态|机构检查事项状态|Y-启用,N-停用',
  `BRANCH_CHECK_ITEM_TYPE` varchar(10) NOT NULL COMMENT '机构检查事项类型|机构检查事项类型|refuse-拒绝,warn-提示',
  `BRANCH_CHECK_ITEM_MESSAGE` varchar(200) NOT NULL COMMENT '机构检查事项提示信息|机构检查事项提示信息',
  `BRANCH_CHECK_CLASS` varchar(50) NOT NULL COMMENT '机构检查类别|定义机构关门、撤并等检查的类别',
  `BRANCH_CHECK_DESC` varchar(100) NOT NULL COMMENT '机构检查定义类描述|机构检查定义类描述',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BRANCH_CHECK_TYPE`,`BRANCH_CHECK_ITEM`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='网点日终检查定义表|网点日终检查定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_branch_check_def`
--

LOCK TABLES `fm_branch_check_def` WRITE;
/*!40000 ALTER TABLE `fm_branch_check_def` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_branch_check_def` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_branch_holiday`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_branch_holiday` (
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `HOLIDAY_TYPE` char(1) NOT NULL COMMENT '假日类型|假日类型|S-标准假日,N-非标准假日',
  `HOLIDAY_DESC` varchar(50) NOT NULL COMMENT '假日描述|假日描述',
  `HOLIDAY_DATE` datetime NOT NULL COMMENT '假日日期|假日日期',
  `WORKING_HOLIDAY` char(1) NOT NULL COMMENT '工作日/假日|工作日/假日|W-工作日,H-假日',
  `APPLY_IND` char(1) NOT NULL COMMENT '适用范围|适用范围|I-个人,E-公司,B-个人和公司',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `LAST_CHANGE_DATE` datetime NOT NULL COMMENT '最后修改日期|最后修改日期',
  PRIMARY KEY (`BRANCH`,`HOLIDAY_TYPE`,`HOLIDAY_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构假日表|机构假日表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_branch_holiday`
--

LOCK TABLES `fm_branch_holiday` WRITE;
/*!40000 ALTER TABLE `fm_branch_holiday` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_branch_holiday` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_branch_status_detail`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_branch_status_detail` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `REG_TYPE` char(1) DEFAULT NULL COMMENT '登记类型|登记类型|1-开户/卡,2-销户/卡',
  `REG_VALUE` varchar(50) DEFAULT NULL COMMENT '登记值|登记值',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构状态历史登记明细表|登记机构开关门历史记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_branch_status_detail`
--

LOCK TABLES `fm_branch_status_detail` WRITE;
/*!40000 ALTER TABLE `fm_branch_status_detail` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_branch_status_detail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_business_parameter`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_business_parameter` (
  `PARA_KEY` varchar(50) NOT NULL COMMENT '参数名|参数名',
  `PARA_VALUE` varchar(200) NOT NULL COMMENT '参数值|参数值',
  `PARA_DESC` varchar(200) DEFAULT NULL COMMENT '参数描述|参数描述',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PARA_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='FM业务参数表|FM业务参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_business_parameter`
--

LOCK TABLES `fm_business_parameter` WRITE;
/*!40000 ALTER TABLE `fm_business_parameter` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_business_parameter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_cash_inout_category`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_cash_inout_category` (
  `CLIENT_BRANCH_TYPE` char(1) NOT NULL COMMENT '客户机构类型|客户机构类型 ，现金出入库 默认取值S-同业|S-同业',
  `CLIENT_TYPE` varchar(3) NOT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `CATEGORY_TYPE` varchar(3) DEFAULT NULL COMMENT '客户细分类型|客户细分类型',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`CLIENT_BRANCH_TYPE`,`CLIENT_TYPE`,`PROD_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='现金出入库对手信息表|配置现金从人行同业出入库时，对方内部账户对应的客户号产品等配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_cash_inout_category`
--

--
-- Table structure for table `fm_ccy_holiday`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_ccy_holiday` (
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `HOLIDAY_TYPE` char(1) NOT NULL COMMENT '假日类型|假日类型|S-标准假日,N-非标准假日',
  `HOLIDAY_DESC` varchar(50) NOT NULL COMMENT '假日描述|假日描述',
  `APPLY_IND` char(1) NOT NULL COMMENT '适用范围|适用范围|I-个人,E-公司,B-个人和公司',
  `HOLIDAY_DATE` datetime NOT NULL COMMENT '假日日期|假日日期',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `HUB_BATCH_FLAG` char(1) DEFAULT NULL COMMENT '标识HUB是否跑批的标识|标识HUB是否跑批的标识',
  `LAST_CHANGE_DATE` datetime NOT NULL COMMENT '最后修改日期|最后修改日期',
  PRIMARY KEY (`CCY`,`HOLIDAY_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='币种节假日定义表|币种节假日定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_ccy_holiday`
--

LOCK TABLES `fm_ccy_holiday` WRITE;
/*!40000 ALTER TABLE `fm_ccy_holiday` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_ccy_holiday` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_channel`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_channel` (
  `CHANNEL` varchar(10) NOT NULL COMMENT '渠道|渠道细类|JZ-集中作业,MT-柜面,ZD-智能网点自助交易平台,VT-远程智能自助银行,QT-XBankT10,QE-快窗智能高柜,CP-手机银行,EB-个人网上银行,CB-企业网上银行,CM-现金管理,WB-微信银行',
  `CHANNEL_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道细类|渠道细类',
  `CHANNEL_CLASS` varchar(3) DEFAULT NULL COMMENT '渠道分类|渠道大类|DEV-设备,ELE-电子渠道',
  `CHANNEL_DESC` varchar(500) NOT NULL COMMENT '渠道描述|渠道描述',
  `CHANNEL_SHORT` varchar(200) DEFAULT NULL COMMENT '渠道简称|渠道简称',
  `COUNTER_FLAG` char(1) DEFAULT NULL COMMENT '柜面标志|是否柜面标志|Y-是,N-否',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`CHANNEL`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='渠道类型表|定义渠道类型参数，对应source_type字段，也用于柜面下拉列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_channel`
--



--
-- Table structure for table `fm_channel_class`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_channel_class` (
  `CHANNEL_CLASS` varchar(3) NOT NULL COMMENT '渠道分类|渠道大类|DEV-设备,ELE-电子渠道',
  `CHANNEL_CLASS_DESC` varchar(50) DEFAULT NULL COMMENT '渠道大类描述|渠道大类描述',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`CHANNEL_CLASS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='渠道大类表|渠道大类表，华兴项目用于对渠道进行归类';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_channel_class`
--

LOCK TABLES `fm_channel_class` WRITE;
/*!40000 ALTER TABLE `fm_channel_class` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_channel_class` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_channel_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_channel_type` (
  `CHANNEL_TYPE` varchar(10) NOT NULL COMMENT '渠道细类|渠道细类',
  `CHANNEL_TYPE_DESC` varchar(50) DEFAULT NULL COMMENT '渠道细类描述|渠道细类描述',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`CHANNEL_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='渠道细类表|渠道细类表，华兴项目用于对渠道进行归类';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_channel_type`
--

LOCK TABLES `fm_channel_type` WRITE;
/*!40000 ALTER TABLE `fm_channel_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_channel_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_city`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_city` (
  `COUNTRY` varchar(3) NOT NULL COMMENT '国家|国家',
  `STATE` varchar(10) NOT NULL COMMENT '省别代码|省、州',
  `CITY` varchar(10) NOT NULL COMMENT '城市|城市',
  `CITY_DESC` varchar(50) NOT NULL COMMENT '城市描述|城市描述',
  `ATTACHED_TO` varchar(50) DEFAULT NULL COMMENT '所属上级|用于配置区/县对应上级市',
  `CITY_TEL` varchar(5) NOT NULL COMMENT '电话区号|电话区号',
  `POSTAL_CODE` varchar(10) DEFAULT NULL COMMENT '邮政编码|邮政编码',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`COUNTRY`,`STATE`,`CITY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='城市代码表|城市代码表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_city`
--

--
-- Table structure for table `fm_client_corp_size`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_client_corp_size` (
  `REF_LANG` varchar(20) NOT NULL COMMENT '参数语言|尾箱参数语言',
  `FIELD_VALUE` varchar(10) NOT NULL COMMENT '取值范围|取值范围',
  `MEANING` varchar(200) NOT NULL COMMENT '说明|说明',
  `NARRATIVE1` varchar(500) DEFAULT NULL COMMENT '备注 |备注',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`REF_LANG`,`FIELD_VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='企业规模表|企业规模表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_client_corp_size`
--

LOCK TABLES `fm_client_corp_size` WRITE;
/*!40000 ALTER TABLE `fm_client_corp_size` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_client_corp_size` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_client_indvl_post`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_client_indvl_post` (
  `REF_LANG` varchar(20) NOT NULL COMMENT '参数语言|尾箱参数语言',
  `FIELD_VALUE` varchar(10) NOT NULL COMMENT '取值范围|取值范围',
  `MEANING` varchar(200) NOT NULL COMMENT '说明|说明',
  `NARRATIVE1` varchar(500) DEFAULT NULL COMMENT '备注 |备注',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`REF_LANG`,`FIELD_VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='职位表|职位表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_client_indvl_post`
--

LOCK TABLES `fm_client_indvl_post` WRITE;
/*!40000 ALTER TABLE `fm_client_indvl_post` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_client_indvl_post` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_client_out_info`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_client_out_info` (
  `OK_FLAG` varchar(1) DEFAULT NULL COMMENT '是否已完成|是否已完成|Y-是,N-否 ',
  `BUSI_DATA` blob COMMENT '业务数据|业务数据',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `CLASS_NAME` varchar(200) DEFAULT NULL COMMENT '分段映射实体类名|分段映射实体类名',
  `CHANGE_DATE` datetime DEFAULT NULL COMMENT '交换日期|交换日期',
  `OLD_CLIENT_NO` varchar(20) NOT NULL COMMENT '原客户号|原客户号',
  `TABLE_NAME` varchar(50) DEFAULT NULL COMMENT '表名|表名',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|新客户号',
  `MERGE_NO` varchar(50) NOT NULL COMMENT '合并编号|合并编号',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户信息迁出表|客户信息迁出表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_client_out_info`
--

LOCK TABLES `fm_client_out_info` WRITE;
/*!40000 ALTER TABLE `fm_client_out_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_client_out_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_company`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_company` (
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `COMPANY_NAME` varchar(50) NOT NULL COMMENT '公司名称 |公司名称',
  `COUNTRY` varchar(3) DEFAULT NULL COMMENT '国家|国家',
  `COMPANY_CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '法人内部客户号 |法人内部客户号',
  `HEAD_OFFICE_CODE` varchar(50) DEFAULT NULL COMMENT '总行机构代码|总行机构代码',
  `ATTACHED_TO` varchar(50) DEFAULT NULL COMMENT '所属上级|用于配置某个参数数据的上级，如区/县对应上级市，机构上级机构，报表项目上级项目等等',
  `BANK_CODE` varchar(20) DEFAULT NULL COMMENT '银行代码|在同一个表中两个不同字段的中文注释都为银行代码',
  `CNY_BUSINESS_UNIT` varchar(10) DEFAULT NULL COMMENT '账套(人民币)|账套(人民币)',
  `HKD_BUSINESS_UNIT` varchar(10) DEFAULT NULL COMMENT '账套(港币)|账套(港币)',
  `MULTI_CORP_QUERY_ALLOW` char(1) DEFAULT NULL COMMENT '是否允许跨法人查询|多法人是否允许跨法人查询标志|Y-允许,N-不允许',
  `MULTI_CORP_QUERY_SCOPE` varchar(200) DEFAULT NULL COMMENT '允许跨法人查询法人范围|多法人允许跨法人查询法人范围',
  `MULTI_CORP_TRAN_ALLOW` char(1) DEFAULT NULL COMMENT '是否允许跨法人交易|多法人是否允许跨法人交易|Y-允许,N-不允许',
  `MULTI_CORP_TRAN_SCOPE` varchar(200) DEFAULT NULL COMMENT '允许跨法人交易法人范围|多法人允许跨法人交易法人范围',
  `MULTI_CORP_SHARE_ALLOW` char(1) DEFAULT NULL COMMENT '是否允许客户信息共享|多法人是否允许跨法人客户信息共享|Y-允许,N-不允许',
  `MULTI_CORP_SHARE_SCOPE` varchar(200) DEFAULT NULL COMMENT '允许客户信息共享法人范围|多法人允许跨法人客户信息共享法人范围',
  `ALL_DEP_COMPANY` varchar(20) DEFAULT NULL COMMENT '通存法人代码|通存法人代码',
  `ALL_DRA_COMPANY` varchar(20) DEFAULT NULL COMMENT '通兑法人代码|通兑法人代码',
  `SYSTEM_PHASE` varchar(3) DEFAULT NULL COMMENT '系统所处的阶段|系统所处的阶段|INP-日间,EOD-日终,SOD-日始',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='法人定义表|法人定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_company`
--

--
-- Table structure for table `fm_counter_party`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_counter_party` (
  `COUNTER_PARTY` varchar(50) NOT NULL COMMENT '对手|对手',
  `COUNTER_PARTY_DESC` varchar(50) NOT NULL COMMENT '对手描述|对手描述',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`COUNTER_PARTY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='交易对手定义表|交易对手定义表，有初始业务参数，但核心暂未使用';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_counter_party`
--

LOCK TABLES `fm_counter_party` WRITE;
/*!40000 ALTER TABLE `fm_counter_party` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_counter_party` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_country`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_country` (
  `COUNTRY` varchar(3) NOT NULL COMMENT '国家|国家',
  `COUNTRY_DESC` varchar(50) NOT NULL COMMENT '国家名称|国家名称',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `REGION` varchar(2) DEFAULT NULL COMMENT '地区|地区(洲代码）',
  `COUNTRY_TEL` varchar(5) DEFAULT NULL COMMENT '国家电话区号|国家电话区号',
  `ISO_CODE` varchar(3) DEFAULT NULL COMMENT 'ISO编码|ISO编码',
  `NCCT_FLAG` char(1) DEFAULT NULL COMMENT '非合作国家|非合作国家|Y-是,N-否',
  `PSC_FLAG` char(1) DEFAULT NULL COMMENT '政治敏感国家|政治敏感国家|Y-是,N-否',
  `SAFE_CODE` varchar(10) DEFAULT NULL COMMENT 'SAFE编码|SAFE编码',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`COUNTRY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='国家代码表|国家的基本信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_country`
--

LOCK TABLES `fm_country` WRITE;
/*!40000 ALTER TABLE `fm_country` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_country` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_currency`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_currency` (
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `CCY_DESC` varchar(50) NOT NULL COMMENT '币种描述|币种描述',
  `ROUND_TRUNC` char(1) NOT NULL COMMENT '舍入|舍入|R-四舍五入,T-舍位',
  `DECI_PLACES` varchar(10) NOT NULL COMMENT '小数位|小数位',
  `INTEGER_DESC` varchar(50) DEFAULT NULL COMMENT '整数描述|整数描述',
  `DECI_DESC` varchar(50) DEFAULT NULL COMMENT '小数描述|小数描述',
  `QUOTE_TYPE` char(1) NOT NULL COMMENT '牌价类型|牌价类型|D-直接,I-间接',
  `DAY_BASIS` varchar(5) NOT NULL COMMENT '基准天数|基准天数,ACT,360',
  `FIXING_DAYS` int DEFAULT NULL COMMENT '固定日期|固定日期',
  `SPOT_DATE` datetime DEFAULT NULL COMMENT '即期日期|即期日期',
  `WEEKEND1` varchar(3) DEFAULT NULL COMMENT '周末1|周末1|MON-周一,TUE-周二,WED-周三,THU-周四,FRI-周五,SAT-周六,SUN-周日',
  `WEEKEND2` varchar(3) DEFAULT NULL COMMENT '周末2|周末2|MON-周一,TUE-周二,WED-周三,THU-周四,FRI-周五,SAT-周六,SUN-周日',
  `PAY_ADVICE_DAYS` int NOT NULL COMMENT '付/收款通知日|付/收款通知日',
  `POSITION_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '净头寸限额|净头寸限额',
  `CCY_GROUP_FLAG` char(1) DEFAULT NULL COMMENT '是否货币组|是否货币组|Y-是,N-否',
  `CCY_GROUP_CODE` varchar(3) DEFAULT NULL COMMENT '货币组代码|货币组代码',
  `CCY_SYMBOL` varchar(3) DEFAULT NULL COMMENT '币种符号|币种符号',
  `CCY_INT_CODE` varchar(3) DEFAULT NULL COMMENT '币种内部码|币种内部码',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`CCY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='币种基本信息表|币种基本信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_currency`
--


--
-- Table structure for table `fm_data_storage_reg`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_data_storage_reg` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `DEAL_FLAG` char(1) DEFAULT NULL COMMENT '处理标识|处理标识|1-未处理  ,2-已处理 ',
  `DEAL_DATE` datetime NOT NULL COMMENT '处理日期|处理日期',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`TABLE_NAME`,`DEAL_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载入库文件处理结果表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_data_storage_reg`
--

LOCK TABLES `fm_data_storage_reg` WRITE;
/*!40000 ALTER TABLE `fm_data_storage_reg` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_data_storage_reg` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_data_unload_param`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_data_unload_param` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `MODULE_NAME` varchar(50) DEFAULT NULL COMMENT '模块名称|模块名称',
  `TARGET_SYSTEM` varchar(50) DEFAULT NULL COMMENT '目标系统|目标系统',
  `FREQUENCE` varchar(5) DEFAULT NULL COMMENT '频率|频率',
  PRIMARY KEY (`TABLE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载参数配置表|用于数据据卸载参数配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_data_unload_param`
--

LOCK TABLES `fm_data_unload_param` WRITE;
/*!40000 ALTER TABLE `fm_data_unload_param` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_data_unload_param` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_default_rate_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_default_rate_type` (
  `RATE_TYPE` varchar(10) NOT NULL COMMENT '汇率类型|汇率类型',
  `TRAN_SCENE` varchar(50) NOT NULL COMMENT '交易场景|交易场景',
  `TRAN_SCENE_DESC` varchar(200) DEFAULT NULL COMMENT '交易场景描述|交易场景描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`RATE_TYPE`,`TRAN_SCENE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='公共汇率类型定义表|公共汇率类型定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_default_rate_type`
--

LOCK TABLES `fm_default_rate_type` WRITE;
/*!40000 ALTER TABLE `fm_default_rate_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_default_rate_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_department`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_department` (
  `DEPARTMENT` varchar(10) NOT NULL COMMENT '部门|部门',
  `DEPARTMENT_DESC` varchar(50) NOT NULL COMMENT '部门名称|部门名称',
  `PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '利润中心 |利润中心 ',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`DEPARTMENT`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='部门信息表|部门信息表，核心暂未使用，目前用于柜面下拉列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_department`
--

LOCK TABLES `fm_department` WRITE;
/*!40000 ALTER TABLE `fm_department` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_department` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_dist_code`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_dist_code` (
  `DIST_CODE` varchar(10) NOT NULL COMMENT '地区代码|地区代码',
  `DIST_NAME` varchar(200) NOT NULL COMMENT '地区名称|地区名称',
  `DIST_GRADE` varchar(10) NOT NULL COMMENT '地区代码级别|地区代码级别',
  `PROVINCE` varchar(30) NOT NULL COMMENT '省|省',
  `STATE` varchar(10) DEFAULT NULL COMMENT '省别代码|省、州',
  `CITY` varchar(10) NOT NULL COMMENT '城市|城市',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`DIST_CODE`,`PROVINCE`,`CITY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='地区代码|地区代码，核心暂未使用，目前用于柜面下拉列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_dist_code`
--

LOCK TABLES `fm_dist_code` WRITE;
/*!40000 ALTER TABLE `fm_dist_code` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_dist_code` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_econ_dist`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_econ_dist` (
  `ECON_DIST` varchar(5) NOT NULL COMMENT '经济特区|经济特区',
  `ECON_DIST_DESC` varchar(50) NOT NULL COMMENT '经济特区描述|经济特区描述',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`ECON_DIST`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='经济特区|经济特区，核心暂未使用，目前用于柜面下拉列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_econ_dist`
--

LOCK TABLES `fm_econ_dist` WRITE;
/*!40000 ALTER TABLE `fm_econ_dist` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_econ_dist` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_ex_unchange_branch`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_ex_unchange_branch` (
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `UNCHANGE_BRANCH` varchar(50) NOT NULL COMMENT '平盘机构号|平盘机构号',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='汇率平盘机构定义|汇率平盘机构定义';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_ex_unchange_branch`
--

LOCK TABLES `fm_ex_unchange_branch` WRITE;
/*!40000 ALTER TABLE `fm_ex_unchange_branch` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_ex_unchange_branch` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_exchange_tran_code`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_exchange_tran_code` (
  `TRAN_CODE` varchar(50) NOT NULL COMMENT '传输代码 |为方便上送系统自定义传输地址',
  `TRAN_CODE_DESC` varchar(200) DEFAULT NULL COMMENT '结售汇项目编码描述|结售汇项目编码描述',
  `INC_EXP_IND` char(1) NOT NULL COMMENT '收支标志|收支标志|I-收入 ,O-支出 ,W-收支一体',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`TRAN_CODE`,`INC_EXP_IND`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='结售汇项目编码表|结售汇项目编码表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_exchange_tran_code`
--

LOCK TABLES `fm_exchange_tran_code` WRITE;
/*!40000 ALTER TABLE `fm_exchange_tran_code` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_exchange_tran_code` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_external_branch`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_external_branch` (
  `BANK_CODE` varchar(20) NOT NULL COMMENT '银行代码|在同一个表中两个不同字段的中文注释都为银行代码',
  `BRANCH_CODE` varchar(50) NOT NULL COMMENT '外部金融机构代码|外部金融机构代码',
  `BRANCH_NAME` varchar(200) DEFAULT NULL COMMENT '机构名称|机构名称',
  `COUNTRY` varchar(3) DEFAULT NULL COMMENT '国家|国家',
  `STATE` varchar(10) DEFAULT NULL COMMENT '省别代码|省、州',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BANK_CODE`,`BRANCH_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='外部金融机构分行表|外部金融机构分行表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_external_branch`
--

LOCK TABLES `fm_external_branch` WRITE;
/*!40000 ALTER TABLE `fm_external_branch` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_external_branch` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_fake_coin_def`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_fake_coin_def` (
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `BOND_TYPE_ID` varchar(30) NOT NULL COMMENT '国债券别代码|国债券别代码',
  `BOND_NAME` varchar(50) DEFAULT NULL COMMENT '券别名称|券别名称',
  `BOND_VERSION_NUM` varchar(20) NOT NULL COMMENT '版别|版别',
  `BOND_NUMBER` int NOT NULL COMMENT '套数|套数',
  `BOND_NOTES` decimal(17,2) NOT NULL COMMENT '面额|面额',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`CCY`,`BOND_TYPE_ID`,`BOND_VERSION_NUM`,`BOND_NUMBER`,`BOND_NOTES`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='劵别信息表|定义各个版别的套数面额等详细信息，可用于柜面下拉列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_fake_coin_def`
--

LOCK TABLES `fm_fake_coin_def` WRITE;
/*!40000 ALTER TABLE `fm_fake_coin_def` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_fake_coin_def` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_fta_branch`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_fta_branch` (
  `FTA_CODE` varchar(10) NOT NULL COMMENT '自贸区代码|自贸区代码',
  `FTA_DESC` varchar(50) NOT NULL COMMENT '自贸区名称|自贸区名称',
  `FTA_TYPE` varchar(2) NOT NULL COMMENT '自贸区类型|自贸区类型|01-转口集散型,02-贸工结合,03-出口加工型,04-保税仓储型',
  `FTA_RATE_TYPE` varchar(10) NOT NULL COMMENT '自贸区汇率类型|自贸区汇率类型',
  `FTA_NATURE` varchar(2) NOT NULL COMMENT '自贸区属性|自贸区属性|01-商业自由区,02-工业自由区',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`FTA_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='自贸区结构表|自贸区结构表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_fta_branch`
--

LOCK TABLES `fm_fta_branch` WRITE;
/*!40000 ALTER TABLE `fm_fta_branch` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_fta_branch` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_fta_def`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_fta_def` (
  `FTA_CODE` varchar(10) NOT NULL COMMENT '自贸区代码|自贸区代码',
  `FTA_TYPE` varchar(2) DEFAULT NULL COMMENT '自贸区类型|自贸区类型|01-转口集散型,02-贸工结合,03-出口加工型,04-保税仓储型',
  `FTA_DESC` varchar(50) DEFAULT NULL COMMENT '自贸区名称|自贸区名称',
  `FTA_NATURE` varchar(2) DEFAULT NULL COMMENT '自贸区属性|自贸区属性|01-商业自由区,02-工业自由区',
  `COUNTRY` varchar(3) DEFAULT NULL COMMENT '国家|国家',
  `CITY` varchar(10) DEFAULT NULL COMMENT '城市|城市',
  `STATE` varchar(10) DEFAULT NULL COMMENT '省别代码|省、州',
  `FTE_FLAG` varchar(10) DEFAULT NULL COMMENT '本岸企业客户前缀|本岸企业客户前缀',
  `FTF_FLAG` varchar(10) DEFAULT NULL COMMENT '个人非居民标志|个人非居民标志，账号前缀',
  `FTI_FLAG` varchar(10) DEFAULT NULL COMMENT '区内个人居民账户标志|个人居民账户标志，账号前缀',
  `FTN_FLAG` varchar(10) DEFAULT NULL COMMENT '离岸企业客户标志|离岸企业客户标志，账户前缀',
  `FTU_FLAG` varchar(10) DEFAULT NULL COMMENT '同业客户账户前缀|同业客户账户前缀',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`FTA_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='自贸区代码定义|自贸区代码定义';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_fta_def`
--

LOCK TABLES `fm_fta_def` WRITE;
/*!40000 ALTER TABLE `fm_fta_def` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_fta_def` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_fx_code`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_fx_code` (
  `FX_CODE` varchar(3) NOT NULL COMMENT '货币交换类型|货币交换类型',
  `FX_CODE_DESC` varchar(50) NOT NULL COMMENT '货币交换类型描述|货币交换类型描述',
  `SELL_BUY_IND` char(1) NOT NULL COMMENT '买卖固定方|买卖固定方|B-结汇,S-售汇,E-外币兑换',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`FX_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='货币交换类型表|货币交换类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_fx_code`
--

LOCK TABLES `fm_fx_code` WRITE;
/*!40000 ALTER TABLE `fm_fx_code` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_fx_code` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_lang_translation`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_lang_translation` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `TRANS_COLUMN` varchar(50) NOT NULL COMMENT '国际化字段|国际化字段',
  `TRANS_COLUMN_VALUE` varchar(500) DEFAULT NULL COMMENT '国际化字段取值|国际化字段取值',
  `BUSI_KEY` varchar(200) DEFAULT NULL COMMENT '业务主键|业务主键',
  `BUSI_KEY_VALUE` varchar(500) NOT NULL COMMENT '业务主键取值|业务主键取值',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `LANGUAGE` varchar(10) NOT NULL COMMENT '语言|语言',
  PRIMARY KEY (`TABLE_NAME`,`TRANS_COLUMN`,`BUSI_KEY_VALUE`,`LANGUAGE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='业务参数多语言码值定义表|保存业务参数表中国际化字段在多语言中的取值信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_lang_translation`
--

--
-- Table structure for table `fm_language`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_language` (
  `LANGUAGE_CODE` char(1) NOT NULL COMMENT '用户语言|用户语言|E-英文,C-中文',
  `LANGUAGE_DESC` varchar(50) NOT NULL COMMENT '用户语言描述|用户语言描述',
  `CHAR_VALUE` char(1) NOT NULL COMMENT '字符值|字符值   字符数字等价标志|A-14,B-2,C-3,D-7,E-0,F-8,G-5,H-6,I-10,J-15,K-16,L-4,M-17,N-11,O-18,P-19,Q-20,R-1,S-21,T-9,U-12,V-13,W-22,X-23,Y-24,Z-25',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`LANGUAGE_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='用户语言表|用户语言表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_language`
--

LOCK TABLES `fm_language` WRITE;
/*!40000 ALTER TABLE `fm_language` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_language` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_loc_holiday`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_loc_holiday` (
  `HOLIDAY_TYPE` char(1) NOT NULL COMMENT '假日类型|假日类型|S-标准假日,N-非标准假日',
  `HOLIDAY_DESC` varchar(50) NOT NULL COMMENT '假日描述|假日描述',
  `COUNTRY` varchar(3) NOT NULL COMMENT '国家|国家',
  `STATE` varchar(10) NOT NULL COMMENT '省别代码|省、州',
  `HOLIDAY_DATE` datetime NOT NULL COMMENT '假日日期|假日日期',
  `WORKING_HOLIDAY` char(1) NOT NULL COMMENT '工作日/假日|工作日/假日|W-工作日,H-假日',
  `APPLY_IND` char(1) NOT NULL COMMENT '适用范围|适用范围|I-个人,E-公司,B-个人和公司',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `LAST_CHANGE_DATE` datetime NOT NULL COMMENT '最后修改日期|最后修改日期',
  PRIMARY KEY (`COUNTRY`,`STATE`,`HOLIDAY_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='地区节假日|地区节假日';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_loc_holiday`
--

--
-- Table structure for table `fm_marital_status`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_marital_status` (
  `REF_LANG` varchar(20) NOT NULL COMMENT '参数语言|尾箱参数语言',
  `FIELD_VALUE` varchar(10) NOT NULL COMMENT '取值范围|取值范围',
  `MEANING` varchar(200) NOT NULL COMMENT '说明|说明',
  `NARRATIVE1` varchar(500) DEFAULT NULL COMMENT '备注 |备注',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`REF_LANG`,`FIELD_VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='婚姻状况表|婚姻状况表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_marital_status`
--

LOCK TABLES `fm_marital_status` WRITE;
/*!40000 ALTER TABLE `fm_marital_status` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_marital_status` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_max_degree`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_max_degree` (
  `REF_LANG` varchar(20) NOT NULL COMMENT '参数语言|尾箱参数语言',
  `FIELD_VALUE` varchar(10) NOT NULL COMMENT '取值范围|取值范围',
  `MEANING` varchar(200) NOT NULL COMMENT '说明|说明',
  `NARRATIVE1` varchar(500) DEFAULT NULL COMMENT '备注 |备注',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`REF_LANG`,`FIELD_VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='学位表|学位表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_max_degree`
--

LOCK TABLES `fm_max_degree` WRITE;
/*!40000 ALTER TABLE `fm_max_degree` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_max_degree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_module`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_module` (
  `MODULE_ID` varchar(2) NOT NULL COMMENT '模块|模块|RB-存款 ,CL-贷款 ,GL-总账',
  `MODULE_NAME` varchar(50) NOT NULL COMMENT '模块名称|模块名称',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`MODULE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='模块说明表|模块说明表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_module`
--

LOCK TABLES `fm_module` WRITE;
/*!40000 ALTER TABLE `fm_module` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_module` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_multi_corp_exception`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_multi_corp_exception` (
  `INCREASE_ID` varchar(30) NOT NULL COMMENT '自增ID|自增ID',
  `EXCEPTION_TYPE` varchar(50) NOT NULL COMMENT '多法人例外类型|多法人例外类型',
  `EXCEPTION_OBJECT` varchar(10) NOT NULL COMMENT '多法人例外种类|多法人例外种类',
  `EXCEPTION_VALUE` varchar(100) NOT NULL COMMENT '多法人例外值|多法人例外值',
  `SWITCH_YN` char(1) NOT NULL COMMENT '开关|开关|Y-开,N-关 ',
  `EXCEPTION_SCOPE` varchar(200) DEFAULT NULL COMMENT '允许跨法人例外范围|允许跨法人例外范围',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`INCREASE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='多法人例外参数表|用于配置多法人例外';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_multi_corp_exception`
--

LOCK TABLES `fm_multi_corp_exception` WRITE;
/*!40000 ALTER TABLE `fm_multi_corp_exception` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_multi_corp_exception` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_parameter`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_parameter` (
  `PARA_KEY` varchar(50) NOT NULL COMMENT '参数名|参数名',
  `PARA_VALUE` varchar(200) NOT NULL COMMENT '参数值|参数值',
  `PARA_DESC` varchar(200) DEFAULT NULL COMMENT '参数描述|参数描述',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PARA_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='FM技术参数表|FM技术参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_parameter`
--

LOCK TABLES `fm_parameter` WRITE;
/*!40000 ALTER TABLE `fm_parameter` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_parameter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_password_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_password_type` (
  `PASSWORD_TYPE` varchar(2) NOT NULL COMMENT '密码类型编号|密码类型编号|WD-支取密码,QY-查询密码,MA-账户管理密码',
  `PASSWORD_TYPE_DESC` varchar(50) DEFAULT NULL COMMENT '密码类型描述|密码类型描述',
  `CD_CATEGORY` varchar(3) NOT NULL COMMENT '卡细类|卡片细类|0-借记卡,1-贷记卡,2-借贷一体卡,3-单位结算卡,ALL-均支持',
  `ERROR_CHANNEL_FLAG` char(1) DEFAULT NULL COMMENT '是否按渠道进行错误次数定义|是否按渠道进行错误次数定义|Y-是,N-否',
  `MAX_FAILUER_TIMES` int DEFAULT NULL COMMENT '密码最大错误次数|密码最大错误次数',
  `RESET_IND` char(1) DEFAULT NULL COMMENT '是否当日清零方式|是否当日清零方式|Y-当日清零,N-非当日清零',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `LAST_CHANGE_DATE` datetime DEFAULT NULL COMMENT '最后修改日期|最后修改日期',
  `LAST_CHANGE_OFFICER` varchar(30) DEFAULT NULL COMMENT '上次修改柜员|上次修改柜员',
  `LAST_CHANGE_TIME` varchar(26) DEFAULT NULL COMMENT '上次修改时间|上次修改时间',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `CHANNEL` varchar(10) NOT NULL COMMENT '渠道|渠道细类|JZ-集中作业,MT-柜面,ZD-智能网点自助交易平台,VT-远程智能自助银行,QT-XBankT10,QE-快窗智能高柜,CP-手机银行,EB-个人网上银行,CB-企业网上银行,CM-现金管理,WB-微信银行A ,PT-支付',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `IS_CORP_SETTLE_CARD` char(1) DEFAULT NULL COMMENT '单位结算卡标志|单位结算卡标志|Y-是,N-否 ',
  PRIMARY KEY (`PASSWORD_TYPE`,`CD_CATEGORY`,`CHANNEL`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='密码类型参数表|密码类型参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_password_type`
--

LOCK TABLES `fm_password_type` WRITE;
/*!40000 ALTER TABLE `fm_password_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_password_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_period_freq`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_period_freq` (
  `PERIOD_FREQ` varchar(5) NOT NULL COMMENT '频率id|频率id',
  `PERIOD_FREQ_DESC` varchar(50) NOT NULL COMMENT '周期频率描述|周期频率描述',
  `FORCE_WORK_DAY` char(1) DEFAULT NULL COMMENT '节假日提前顺延标志 |节假日是否顺延|Y-顺延,N-不顺延,A-提前',
  `ADD_NO` int NOT NULL COMMENT '每期数量|每期数量',
  `DAY_MTH` char(1) NOT NULL COMMENT '每期单位|每期单位|Y-年,Q-季,M-月,W-周,D-日',
  `DAY_NUM` int NOT NULL COMMENT '每期天数|DAY_MTH为D时，取ADD_NO值；为M时，值为7* ADD_NO；为Y时，值为360* ADD_NO',
  `PRIOR_DAYS` int DEFAULT NULL COMMENT '期限单位值|期限单位值',
  `FIRST_LAST` char(1) NOT NULL COMMENT '期初/期末|期初/期末|F-期初,L-期末,A-实际天数',
  `HALF_MONTH` char(1) NOT NULL COMMENT '半月标识|半月标识|Y-是,N-否',
  `CLIENT_SPREAD` decimal(15,8) DEFAULT NULL COMMENT '客户浮动|客户浮动',
  `STANDARD_FLAG` char(1) DEFAULT NULL COMMENT '是否标准模板|是否标准模板|Y-是,N-否',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PERIOD_FREQ`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='周期频率表|周期频率表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_period_freq`
--

--
-- Table structure for table `fm_profit_center`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_profit_center` (
  `PROFIT_CENTER` varchar(20) NOT NULL COMMENT '利润中心 |利润中心',
  `PROFIT_CENTER_TYPE` char(1) NOT NULL COMMENT '利润中心类型|利润中心类型|S-Sub记账类,C-Control控制类',
  `PROFIT_CENTER_DESC` varchar(50) NOT NULL COMMENT '利润中心中文说明  |利润中心中文说明',
  `PROFIT_CENTER_DESC_EN` varchar(50) DEFAULT NULL COMMENT '利润中心英文说明  |利润中心英文说明',
  `PROFIT_CENTER_LEVEL` char(1) DEFAULT NULL COMMENT '利润中心级别  |利润中心级别',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PROFIT_CENTER`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='利润中心|利润中心';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_profit_center`
--

LOCK TABLES `fm_profit_center` WRITE;
/*!40000 ALTER TABLE `fm_profit_center` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_profit_center` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_ref_code`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_ref_code` (
  `REF_LANG` varchar(20) NOT NULL COMMENT '参数语言|尾箱参数语言',
  `DOMAIN` varchar(50) NOT NULL COMMENT '表字段|表字段',
  `FIELD_VALUE` varchar(10) NOT NULL COMMENT '取值范围|取值范围',
  `ABBREVIATION` varchar(50) DEFAULT NULL COMMENT '系统基础参数缩写描述|系统基础参数缩写描述',
  `MEANING` varchar(200) NOT NULL COMMENT '说明|说明',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`REF_LANG`,`DOMAIN`,`FIELD_VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='系统内部预定义参数表|系统内部预定义参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_ref_code`
--

LOCK TABLES `fm_ref_code` WRITE;
/*!40000 ALTER TABLE `fm_ref_code` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_ref_code` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_region`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_region` (
  `REGION` varchar(2) NOT NULL COMMENT '地区|地区(洲代码）',
  `REGION_DESC` varchar(50) NOT NULL COMMENT '地区描述|地区描述',
  `INTERNAL_CODE` char(1) DEFAULT NULL COMMENT '内部码|内部码|0-亚洲,1-非亚洲',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`REGION`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='区域代码地区基本信息|区域代码地区基本信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_region`
--

LOCK TABLES `fm_region` WRITE;
/*!40000 ALTER TABLE `fm_region` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_region` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_region_code_userdef`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_region_code_userdef` (
  `REGION_CODE` varchar(10) NOT NULL COMMENT '区域代码|区域代码',
  `REGION_CODE_DESC` varchar(50) NOT NULL COMMENT '区域代码描述|区域代码描述',
  `REGION_TYPE_CODE` varchar(10) NOT NULL COMMENT '区域类型代码|区域类型代码',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`REGION_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='自定义区域代码定义|自定义区域代码定义，阜新项目新增，目前核心暂未使用';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_region_code_userdef`
--

LOCK TABLES `fm_region_code_userdef` WRITE;
/*!40000 ALTER TABLE `fm_region_code_userdef` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_region_code_userdef` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_region_type_userdef`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_region_type_userdef` (
  `REGION_TYPE` varchar(10) NOT NULL COMMENT '区域类型|区域类型',
  `REGION_TYPE_DESC` varchar(50) NOT NULL COMMENT '区域类型描述|区域类型描述',
  `CROSS_REGION_FLAG` char(1) NOT NULL COMMENT '是否允许机构跨区域存在|是否允许机构跨区域存在|Y-是,N-否',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`REGION_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='自定义区域类型定义|自定义区域类型定义，阜新项目新增，目前核心暂未使用';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_region_type_userdef`
--

LOCK TABLES `fm_region_type_userdef` WRITE;
/*!40000 ALTER TABLE `fm_region_type_userdef` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_region_type_userdef` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_restraint_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_restraint_type` (
  `RESTRAINT_TYPE` varchar(3) NOT NULL COMMENT '限制类型|限制类型',
  `RESTRAINT_TYPE_DESC` varchar(200) DEFAULT NULL COMMENT '限制类型描述|限制类型描述',
  `RESTRAINT_CLASS` varchar(2) DEFAULT NULL COMMENT '限制类型类别|限制类型类别|AH-有权机关资金冻结,AS-中止用户所有操作,CF-白金钻石卡年费/管理费资金圈存,CO-只允许现金,DA-账户余额止付,FH-资金冻结,HA-账户保留余额,IN-警告信息,MD-不允许现金存入,MW-不允许现金支取,OW-挂失专用止付,SC-支票止付,SD-停止存入交易,SW-停止支取交易,TD-不允许转贷,TW-不允许转借',
  `RES_PRIORITY` varchar(2) DEFAULT NULL COMMENT '冻结级别|限制级别',
  `MANUAL_RES_FLAG` char(1) DEFAULT NULL COMMENT '手工冻结标志|手工冻结标志|Y-允许,N-不允许',
  `MANUAL_UNRES_FLAG` char(1) DEFAULT NULL COMMENT '手工解冻标志|手工解冻标志|Y-允许,N-不允许',
  `STOP_WTD_FLAG` char(1) DEFAULT NULL COMMENT '全额止付标志|全额止付标志|Y-是,N-否',
  `SYSTEM_USE_FLAG` char(1) DEFAULT NULL COMMENT '是否系统专用标志|是否系统专用标志|Y-是,N-否',
  `AH_BU_FLAG` char(1) DEFAULT NULL COMMENT '是否有权机关冻结|是否有权机关冻结|Y-是,N-否',
  `EOD_IMPOUND_FLAG` char(1) DEFAULT NULL COMMENT 'EOD扣款标志|EOD扣款标志|Y-是,N-否',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `ALLOW_CUSTOMER_APPLY` varchar(1) DEFAULT NULL COMMENT '是否允许客户自行申请|是否允许客户自行申请|Y-允许,N-不允许',
  `ALLOWED_MOD_CHANNELS` varchar(200) DEFAULT NULL COMMENT '允许维护的渠道(多个用逗号分隔)',
  `ALLOWED_DUTY_CHANNELS` varchar(200) DEFAULT NULL COMMENT '允许解冻的渠道(多个用逗号分隔)',
  `AUTO_DUTY_DAYS` varchar(30) DEFAULT NULL COMMENT '自动解冻天数',
  `AMOUNT_MARKUP_PERCENTAGE` decimal(17,2) DEFAULT NULL COMMENT '金额上浮比例(%)',
  PRIMARY KEY (`RESTRAINT_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='账户限制类型定义表|账户限制类型定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_restraint_type`
--

LOCK TABLES `fm_restraint_type` WRITE;
/*!40000 ALTER TABLE `fm_restraint_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_restraint_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_run_date_notice`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_run_date_notice` (
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `SWITCH_YN` char(1) NOT NULL COMMENT '开关|是否为外部日期接受|Y-开,N-关 ',
  `NEXT_RUN_DATE` datetime DEFAULT NULL COMMENT '下一运行日|下一运行日',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`TRAN_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='营业日期通知表|营业日期通知表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_run_date_notice`
--


--
-- Table structure for table `fm_seq_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_seq_type` (
  `SEQ_TYPE` varchar(3) NOT NULL COMMENT '序列类型代码|序列类型代码',
  `START_NO` varchar(50) NOT NULL COMMENT '起始号码数字串|起始号码，以数字类型存储',
  `END_NO` varchar(50) NOT NULL COMMENT '终止号码数字串|终止号码，以数字类型存储',
  `PROD_TYPE_RESET_FLAG` char(1) DEFAULT NULL COMMENT '产品重置|产品重置|Y-是,N-否',
  `BRANCH_RESET_FLAG` char(1) DEFAULT NULL COMMENT '分行重置|分行重置|Y-是,N-否',
  `CCY_RESET_FLAG` char(1) DEFAULT NULL COMMENT '币种重置|币种重置|Y-是,N-否',
  `INDVL_RESET_FLAG` char(1) DEFAULT NULL COMMENT '客户公私属性重置|客户公私属性重置|Y-是,N-否',
  `CARD_BIN_RESET_FLAG` char(1) DEFAULT NULL COMMENT '卡bin重置标志|是否根据卡bin重置序号',
  `ACCT_CLASS_RESET_FLAG` char(1) DEFAULT NULL COMMENT '账户分类重置标志|账户分类重置标志',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SEQ_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='序列类型定义表|序列类型定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_seq_type`
--

LOCK TABLES `fm_seq_type` WRITE;
/*!40000 ALTER TABLE `fm_seq_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_seq_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_seq_type_dtl`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_seq_type_dtl` (
  `SEQ_TYPE` varchar(3) NOT NULL COMMENT '序列类型代码|序列类型代码',
  `START_NO` varchar(50) NOT NULL COMMENT '起始号码数字串|起始号码，以数字类型存储',
  `END_NO` varchar(50) NOT NULL COMMENT '终止号码数字串|终止号码，以数字类型存储',
  `LAST_NO` varchar(50) DEFAULT NULL COMMENT '最后使用号|最后使用号',
  `SEQ_RESET_PARAM` varchar(50) NOT NULL COMMENT '序列重置参数|序列重置参数',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SEQ_TYPE`,`SEQ_RESET_PARAM`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='序列类型详细信息表|序列类型详细信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_seq_type_dtl`
--

LOCK TABLES `fm_seq_type_dtl` WRITE;
/*!40000 ALTER TABLE `fm_seq_type_dtl` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_seq_type_dtl` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_seq_type_rule`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_seq_type_rule` (
  `SEQ_TYPE` varchar(3) NOT NULL COMMENT '序列类型代码|序列类型代码',
  `RULE_TYPE` varchar(10) NOT NULL COMMENT '规则类型|规则类型',
  `START_NO` varchar(50) NOT NULL COMMENT '起始号码数字串|起始号码，以数字类型存储',
  `END_NO` varchar(50) NOT NULL COMMENT '终止号码数字串|终止号码，以数字类型存储',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SEQ_TYPE`,`RULE_TYPE`,`START_NO`,`END_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='序列规则定义表|序列规则定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_seq_type_rule`
--

LOCK TABLES `fm_seq_type_rule` WRITE;
/*!40000 ALTER TABLE `fm_seq_type_rule` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_seq_type_rule` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_service_access_conf`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_service_access_conf` (
  `SERVICE_ID` varchar(30) NOT NULL COMMENT '服务ID|服务ID',
  `AGENT_ACCOUNT_FLAG` char(1) DEFAULT NULL COMMENT '代理记账标志|接口是否支持柜员代理记账|Y-支持,N-不支持',
  PRIMARY KEY (`SERVICE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='代理记账可访问的接口配置表|代理记账可访问的接口配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_service_access_conf`
--

LOCK TABLES `fm_service_access_conf` WRITE;
/*!40000 ALTER TABLE `fm_service_access_conf` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_service_access_conf` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_settle_method`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_settle_method` (
  `SETTLE_METHOD` varchar(3) NOT NULL COMMENT '结算方法|结算方法|R-结算户,I-内部户,N-往账,V-来账',
  `SETTLE_METHOD_DESC` varchar(50) NOT NULL COMMENT '结算方法描述|结算方法描述',
  `PAY_REC` char(1) NOT NULL COMMENT '收付标志|收付标志|P-付 ,R-收',
  `SETTLE_ACCT_TYPE` char(1) NOT NULL COMMENT '结算账户类型|结算账户类型|R-Retail零售账户,I-Internal内部账户,N-Nostro 往账账户,V-Vostro 往来账账户,C-Card Account 卡号',
  `DEST_CLIENT_TYPE` char(1) NOT NULL COMMENT '目标客户类型|目标客户类型|C-交易对手,I-内部,L-贷方,N-不产生消息,S-结算',
  `ROUTE` varchar(10) NOT NULL COMMENT '联系方式类型|联系方式类型|SWIFT-SWIFT电文,POSTAL-邮寄',
  `FORMAT` varchar(10) NOT NULL COMMENT '电位类型|定义电文的类型，例如,FT103，FT195等',
  `MEDIA` varchar(10) NOT NULL COMMENT '报表格式|报表格式|REPORT-报表,FILE-文件',
  `VERIFY_SECURITY` char(1) DEFAULT NULL COMMENT '安全复合|安全复合  0:Lowest Level,1:Teller,2:Clerk,3:Senior Clerk,4:Assistant Supervisor,5:Supervisor,6:Senior Supervisor,7:Officer,8:Senior Officer,9:Manager|安全复合  0:Lowest Level,1:Teller,2:Clerk,3:Senior Clerk,4:Assistant Supervisor,5:Supervisor,6:Senior Supervisor,7:Officer,8:Senior Officer,9:Manager',
  `RELEASE_SECURITY` char(1) NOT NULL COMMENT '安全释放|安全释放|0-Lowest Level,1-Teller,2-Clerk,3-Senior Clerk,4-Assistant Supervisor,5-Supervisor,6-Senior Supervisor,7-Officer,8-Senior Officer,9-Manager',
  `DEST_ID` varchar(50) DEFAULT NULL COMMENT '目标ID|目标ID',
  `DEST_TYPE` char(1) DEFAULT NULL COMMENT '目标类型|目标类型|P-打印机,T-磁带',
  `PRINT_MODE` varchar(5) DEFAULT NULL COMMENT '打印模式|打印模式  w272',
  `DP_SETTLE_FLAG` char(1) DEFAULT NULL COMMENT '是否为DP清算|是否为DP清算|Y-是,N-否',
  `CONTACT_TYPE` varchar(20) DEFAULT NULL COMMENT '联系类型|联系类型',
  `IS_CASH` char(1) DEFAULT NULL COMMENT '是否现金|是否现金|Y-是,N-否',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `SENDERS_CONTACT_TYPE` varchar(2) DEFAULT NULL COMMENT '发报方联系类型|发报方联系类型|11-单位联系信息,12-家庭联系信息,13-居住地联系信息 ,14-移动联系信息,15-电子邮箱信息,16-传真信息,17-对账单邮寄地址1,18-对账单邮寄地址2,19-其他信息',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SETTLE_METHOD`,`PAY_REC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='结算方法定义表|结算方法定义表，用于定义结算方法的枚举值，目前用于柜面下拉列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_settle_method`
--

LOCK TABLES `fm_settle_method` WRITE;
/*!40000 ALTER TABLE `fm_settle_method` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_settle_method` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_state`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_state` (
  `COUNTRY` varchar(3) NOT NULL COMMENT '国家|国家',
  `STATE` varchar(10) NOT NULL COMMENT '省别代码|省、州',
  `STATE_DESC` varchar(50) NOT NULL COMMENT '省名称|省名称',
  `WEEKEND1` varchar(3) DEFAULT NULL COMMENT '周末1|周末1|MON-周一,TUE-周二,WED-周三,THU-周四,FRI-周五,SAT-周六,SUN-周日',
  `WEEKEND2` varchar(3) DEFAULT NULL COMMENT '周末2|周末2|MON-周一,TUE-周二,WED-周三,THU-周四,FRI-周五,SAT-周六,SUN-周日',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`COUNTRY`,`STATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='省、州信息表|省、州信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_state`
--

--
-- Table structure for table `fm_structure_attr`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_structure_attr` (
  `ATTR` varchar(20) NOT NULL COMMENT '属性|结构属性|A/N/AN/ANS',
  `DESCRIPTION` varchar(50) NOT NULL COMMENT '结构属性说明|结构属性说明',
  `CHAR_ARRAY` varchar(200) NOT NULL COMMENT '结构属性取值范围|结构属性取值范围',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`ATTR`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='结构属性定义表|结构属性定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_structure_attr`
--

LOCK TABLES `fm_structure_attr` WRITE;
/*!40000 ALTER TABLE `fm_structure_attr` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_structure_attr` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_structure_conv`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_structure_conv` (
  `CHAR_VALUE` char(1) NOT NULL COMMENT '字符值|字符值   字符数字等价标志|A-14,B-2,C-3,D-7,E-0,F-8,G-5,H-6,I-10,J-15,K-16,L-4,M-17,N-11,O-18,P-19,Q-20,R-1,S-21,T-9,U-12,V-13,W-22,X-23,Y-24,Z-25',
  `NUMERIC_EQUIV` varchar(50) NOT NULL COMMENT '同等的数字|同等的数字',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`CHAR_VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='结构转化规则定义表|结构转化规则定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_structure_conv`
--

LOCK TABLES `fm_structure_conv` WRITE;
/*!40000 ALTER TABLE `fm_structure_conv` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_structure_conv` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_structure_digit_pos`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_structure_digit_pos` (
  `STRUCTURE_TYPE` varchar(3) NOT NULL COMMENT '结构类型|结构类型',
  `DIGIT_POS` varchar(50) NOT NULL COMMENT '数字位置|数字位置',
  `CHECK_DIGIT_IND` char(1) NOT NULL COMMENT '是否进行数字的权重计算|是否进行数字的权重计算|Y-是,N-否',
  `WEIGHT` varchar(20) DEFAULT NULL COMMENT '权重|权重',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`STRUCTURE_TYPE`,`DIGIT_POS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='结构数字校验位定义表|结构数字校验位定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_structure_digit_pos`
--

LOCK TABLES `fm_structure_digit_pos` WRITE;
/*!40000 ALTER TABLE `fm_structure_digit_pos` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_structure_digit_pos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_structure_param`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_structure_param` (
  `STRUCTURE_TYPE` varchar(3) NOT NULL COMMENT '结构类型|结构类型',
  `PARAM_TYPE` varchar(2) NOT NULL COMMENT '参数类型|参数类型|AN-Basic Account Number 基本账号,BK-Bank Code,BR-Branch Code 机构号,CC-Country Code 国家代码,CD-Check Digit 校验数字,CL-Client Number 客户号,CY-Currency Code 币种,DT-Date Value 生效日期,PC Padding Character,PF-product family 产品家族,PI-Program ID 程序编号,PT-Product Type 产品类型,RN-region 地区号,SN-Sequence Number 序列号,SP-System Phase 系统阶段,ST-String Value 字符串值,UI-User ID 柜员ID',
  `LENGTH` varchar(50) NOT NULL COMMENT '长度|长度',
  `START_POS` varchar(50) NOT NULL COMMENT '起始位置|起始位置',
  `END_POS` varchar(50) NOT NULL COMMENT '结束位置|结束位置',
  `PADDING_CHAR` varchar(10) DEFAULT NULL COMMENT '填充字符|填充字符',
  `SEQ_TYPE` varchar(3) DEFAULT NULL COMMENT '序列类型代码|序列类型代码',
  `STRING_VALUE` varchar(50) DEFAULT NULL COMMENT '字符串值|字符串值',
  `START_INDEX` int DEFAULT NULL COMMENT '起始索引|起始索引',
  `END_INDEX` int DEFAULT NULL COMMENT '终止索引|终止索引',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`STRUCTURE_TYPE`,`PARAM_TYPE`,`START_POS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='结构参数定义表|结构参数定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_structure_param`
--

LOCK TABLES `fm_structure_param` WRITE;
/*!40000 ALTER TABLE `fm_structure_param` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_structure_param` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_structure_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_structure_type` (
  `STRUCTURE_TYPE` varchar(3) NOT NULL COMMENT '结构类型|结构类型',
  `STRUCTURE_ATTR` varchar(3) DEFAULT NULL COMMENT '结构属性|结构属性|A/N/AN/ANS',
  `STRUCTURE_DESC` varchar(50) NOT NULL COMMENT '结构描述|结构描述',
  `STRUCTURE_LENGTH` varchar(5) NOT NULL COMMENT '结构长度|结构长度',
  `STRUCTURE_CLASS` varchar(2) NOT NULL COMMENT '结构分类|结构分类|CN-客户号,AN-账号,AL-所有对象都可以使用,RF-报表文件名称,RS-报表缓冲池路径',
  `DELIMITER_IND` char(1) NOT NULL COMMENT '是否设置分隔符|是否设置分隔符|Y-是,N-否',
  `RESTRICTED_DELIMITER` varchar(50) DEFAULT NULL COMMENT '不容许的分隔符|不容许的分隔符',
  `COMPLETE_FLAG` char(1) NOT NULL COMMENT '是否完成标志|是否完成标志|Y-是,N-否',
  `CHECK_DIGIT_FORMULA` varchar(3) DEFAULT NULL COMMENT '检查规则|检查规则',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`STRUCTURE_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='结构类型定义表|结构类型定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_structure_type`
--

LOCK TABLES `fm_structure_type` WRITE;
/*!40000 ALTER TABLE `fm_structure_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_structure_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_sub_district_code`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_sub_district_code` (
  `SUB_DISTRICT_CODE` varchar(10) NOT NULL COMMENT '子区代码|子区代码',
  `SUB_DISTRICT_NAME` varchar(50) DEFAULT NULL COMMENT '子区名字|子区名字',
  `DIST_CODE` varchar(10) DEFAULT NULL COMMENT '地区代码|地区代码',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SUB_DISTRICT_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='地区子区定义表|定义地区的子区的码值描述，印尼项目新增';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_sub_district_code`
--

LOCK TABLES `fm_sub_district_code` WRITE;
/*!40000 ALTER TABLE `fm_sub_district_code` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_sub_district_code` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_sys_lang_translation`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_sys_lang_translation` (
  `LANGUAGE` varchar(10) NOT NULL COMMENT '语言|语言',
  `TRANS_COLUMN` varchar(50) NOT NULL COMMENT '国际化字段|国际化字段',
  `BUSI_KEY` varchar(200) NOT NULL COMMENT '业务主键|业务主键',
  `TRANS_COLUMN_VALUE` varchar(500) DEFAULT NULL COMMENT '国际化字段取值|国际化字段取值',
  PRIMARY KEY (`LANGUAGE`,`TRANS_COLUMN`,`BUSI_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='系统参数多语言码值定义表|保存系统参数表中国际化字段在多语言中的取值信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_sys_lang_translation`
--

--
-- Table structure for table `fm_system`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_system` (
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|系统运行默认法人编号',
  `COY_NAME` varchar(50) NOT NULL COMMENT '银行全称|银行全称',
  `COY_SHORT` varchar(50) NOT NULL COMMENT '银行简称|银行简称',
  `DEFAULT_BRANCH` varchar(50) DEFAULT NULL COMMENT '默认机构|默认机构，一般为默认法人的总行清算中心',
  `LAST_RUN_DATE` datetime DEFAULT NULL COMMENT '上一运行日期|上一运行日期',
  `RUN_DATE` datetime NOT NULL COMMENT '运行日期|系统当前运行会计日期',
  `NEXT_RUN_DATE` datetime NOT NULL COMMENT '下一运行日|下一运行日',
  `MTH_END_DATE` datetime NOT NULL COMMENT '本月月末日期|本月月末日期',
  `QUR_END_DATE` datetime NOT NULL COMMENT '季末日期|季末日期',
  `HALF_END_DATE` datetime NOT NULL COMMENT '半年末日期|半年末日期',
  `YR_END_DATE` datetime NOT NULL COMMENT '本年年末日期|本年年末日期',
  `MAIN_BRANCH_CODE` varchar(50) DEFAULT NULL COMMENT '总行层级代码|总行层级代码，目前系统暂未使用',
  `HEAD_OFFICE_CLIENT` varchar(20) DEFAULT NULL COMMENT '总行清算行内部客户|总行清算行内部客户，目前系统暂未使用',
  `SYSTEM_PHASE` varchar(3) NOT NULL COMMENT '系统所处的阶段|当前系统所处的运行阶段|INP-日间,EOD-日终,SOD-日始',
  `AUTO_CLIENT_GEN_FLAG` char(1) DEFAULT NULL COMMENT '是否自动生成客户号|是否自动生成客户号|Y-是,N-否',
  `CLIENT_NO_STRUCTURE_TYPE` varchar(3) DEFAULT NULL COMMENT '客户号结构类型|客户号结构类型，目前系统暂未使用',
  `AUTO_COLL_GEN_FLAG` char(1) DEFAULT NULL COMMENT '是否自动生成抵质押编号|是否自动生成抵质押编号|Y-是,N-否',
  `AUTO_LOCK_BL_CLIENT_FLAG` char(1) DEFAULT NULL COMMENT '自动冻结黑名单客户|自动冻结黑名单客户|Y-是,N-否',
  `MULTI_CORPORATION_FLAG` char(1) DEFAULT NULL COMMENT '是否多法人系统|是否多法人系统|Y-是,N-否',
  `MULTI_CORPORATION_METHOD` char(1) DEFAULT NULL COMMENT '多法人机构间清算方式|多法人机构间清算方式|I-系统内清算模式,P-支付清算模式,N-不允许法人间通存通兑',
  `MULTI_ALL_DEP_FLAG` char(1) DEFAULT NULL COMMENT '法人间通存标志|法人间通存标志',
  `MULTI_ALL_DRA_FLAG` char(1) DEFAULT NULL COMMENT '法人间通兑标志|法人间通兑标志',
  `DEP_DRA_TRAN_BRANCH_FLAG` char(1) DEFAULT NULL COMMENT '通存通兑是否过交易行|目前用作跨法人清算时是否过交易所在法人',
  `LOCAL_CCY` varchar(3) DEFAULT NULL COMMENT '当地币种|当地币种',
  `BASE_CCY` varchar(3) DEFAULT NULL COMMENT '基础币种|基础币种',
  `REPORT_CCY` varchar(3) DEFAULT NULL COMMENT '报表币种|报表币种，目前系统暂未使用',
  `LIMIT_CCY` varchar(3) DEFAULT NULL COMMENT '限制币种|限制币种，目前系统暂未使用',
  `DEFAULT_CHARGE_RATE_TYPE` varchar(10) DEFAULT NULL COMMENT '结售汇内部平盘汇率类型|结售汇内部平盘汇率类型',
  `DEFAULT_PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '默认利润中心|默认利润中心',
  `DEFAULT_RATE_TYPE` varchar(10) DEFAULT NULL COMMENT '本地币种汇率类型|本地币种汇率类型',
  `DEFAULT_RATE_TYPE_LOCAL` varchar(10) DEFAULT NULL COMMENT '默认本地汇率类型|默认本地汇率类型',
  `ALLOW_BACKQRY_DAY` int DEFAULT NULL COMMENT '允许查询的历史天数|允许查询的历史天数',
  `BATCH_CHECK_FLAG` char(1) DEFAULT NULL COMMENT '批处理检查标志|批处理检查标志|Y -是,N -否',
  `BATCH_DEFAULT_USER_ID` varchar(30) DEFAULT NULL COMMENT '默认批处理用户|批处理期间系统自动交易的交易柜员',
  `BATCH_MODULE` varchar(2) DEFAULT NULL COMMENT '当前批处理的模块号|当前批处理的模块号，目前系统暂未使用|RB-存款 ,CL-贷款 ,GL-总账',
  `BATCH_UNIT` varchar(50) DEFAULT NULL COMMENT '当前批处理的业务组编号|当前批处理的业务组编号，目前系统暂未使用',
  `CAPITAL_FUNDS` decimal(17,2) DEFAULT NULL COMMENT '投资资金|投资资金',
  `CLIENT_BLOCK_FLAG` char(1) DEFAULT NULL COMMENT '资料不全客户冻结标志|客户资料不全时是否增加客户冻结|Y-是,N-否',
  `CONTINUOUS_RUN` char(1) DEFAULT NULL COMMENT '是否连续使用指定的数字区间标志|是否连续使用指定的数字区间标志，目前系统暂未使用|Y-是,N-否',
  `CR_DR_CHECK_FLAG` char(1) DEFAULT NULL COMMENT '借贷检查标志|借贷检查标志，目前系统暂未使用|Y-是,N-否',
  `EBH_BRANCH` varchar(50) DEFAULT NULL COMMENT '电子银行机构|电子银行机构，目前系统暂未使用',
  `EXCHANGE_RATE_VARIANCE` decimal(5,2) DEFAULT NULL COMMENT '汇率浮动百分比|汇率浮动百分比，目前系统暂未使用',
  `INTERNAL_RATE_CHARGE_FLAG` char(1) DEFAULT NULL COMMENT '是否行内结售汇平盘|是否行内结售汇平盘|Y-是,N-否',
  `INTER_BRANCH_ACCT_HO` varchar(20) DEFAULT NULL COMMENT '分行间清算科目-同业存放|分行间清算科目-同业存放',
  `INTER_BRANCH_IND` char(1) DEFAULT NULL COMMENT '是否为内部银行|是否为内部银行|Y-是,N-否',
  `GAP_TYPE` varchar(20) DEFAULT NULL COMMENT '敞口类型|敞口类型',
  `PROCESS_SPLIT_IND` char(1) NOT NULL COMMENT '批处理阶段标志|系统当前是否处于批处理阶段|Y-批处理阶段,N-非批处理阶段',
  `PRODUCT30E_FLAG` char(1) DEFAULT NULL COMMENT '是否产品版30E计算天数|是否产品版30E计算天数|Y-是使用产品版30E计算天数,N-否使用成都版30E计算天数',
  `GL_IND` char(1) NOT NULL COMMENT 'SYMBOLS总账分离标志|SYMBOLS总账分离标志，目前系统暂未使用|Y-使用SYMBOLS总账,N-不使用SYMBOLS总账',
  `DAC_IND` char(1) DEFAULT NULL COMMENT 'DAC校验标志|DAC校验标志|Y-使用DAC校验,N-不使用DAC校验',
  `IS_DEBUG` char(1) DEFAULT NULL COMMENT '是否记录业务数据信息|是否记录业务数据信息|Y-是,N-否',
  `IS_ERROR` char(1) DEFAULT NULL COMMENT '是否记录出错时的业务数据信息|是否记录出错时的业务数据信息|Y-是,N-否',
  `RB_RESTRAINT_TYPE` varchar(3) DEFAULT NULL COMMENT '默认存款账户限制类型|默认存款账户限制类型',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `DEFAULT_LANGUAGE` varchar(50) DEFAULT NULL COMMENT '默认语言标志|默认语言标志',
  PRIMARY KEY (`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='系统业务参数表|系统业务参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_system`
--



--
-- Table structure for table `fm_system_def`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_system_def` (
  `SYSTEM_ID` varchar(20) NOT NULL COMMENT '系统ID|系统ID',
  `SYSTEM_ID_DESC` varchar(50) DEFAULT NULL COMMENT '业务范围名称|系统名称简称',
  `SYSTEM_NAME` varchar(50) DEFAULT NULL COMMENT '系统名称|系统名称全称',
  `SYSTEM_CODE` varchar(200) DEFAULT NULL COMMENT '系统编码|系统编码',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SYSTEM_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='业务范围设置|定义银行所有系统编号和名称，晋商项目为大总账新增，目前标准版本暂未使用';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_system_def`
--

LOCK TABLES `fm_system_def` WRITE;
/*!40000 ALTER TABLE `fm_system_def` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_system_def` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_taxpayer_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_taxpayer_type` (
  `REF_LANG` varchar(20) NOT NULL COMMENT '参数语言|尾箱参数语言',
  `FIELD_VALUE` varchar(10) NOT NULL COMMENT '取值范围|取值范围',
  `MEANING` varchar(200) NOT NULL COMMENT '说明|说明',
  `NARRATIVE1` varchar(500) DEFAULT NULL COMMENT '备注 |备注',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`REF_LANG`,`FIELD_VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='纳税人类型表|纳税人类型表，用于客户模块柜面下拉列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_taxpayer_type`
--

LOCK TABLES `fm_taxpayer_type` WRITE;
/*!40000 ALTER TABLE `fm_taxpayer_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_taxpayer_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_tran_info`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_tran_info` (
  `TRAN_SEQ_NO` varchar(50) NOT NULL COMMENT '交易序号|交易序号',
  `SOURCE_MODULE` varchar(3) DEFAULT NULL COMMENT '源模块|源模块|RB-存款,CL-贷款,GL-总账,ALL-所有',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|渠道类型',
  `TERMINAL_NO` varchar(50) DEFAULT NULL COMMENT '终端ID|终端ID',
  `SERVICE_CODE` varchar(20) DEFAULT NULL COMMENT '服务代码|服务代码',
  `MESSAGE_TYPE` varchar(10) DEFAULT NULL COMMENT '接口服务类型|接口服务类型|1000-金融服务 ,1200-非金融 ,1300-冲正 ,1400-查询',
  `MESSAGE_CODE` varchar(10) DEFAULT NULL COMMENT '接口服务代码|接口服务代码',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号|渠道流水号',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `PROGRAM_ID` varchar(20) DEFAULT NULL COMMENT '交易代码|交易代码',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `APPR_USER_ID` varchar(30) DEFAULT NULL COMMENT '复核柜员|复核柜员',
  `APPROVAL_DATE` datetime DEFAULT NULL COMMENT '复核日期|复核日期',
  `AUTH_USER_ID` varchar(30) DEFAULT NULL COMMENT '授权柜员|授权柜员',
  `OPERATE_TYPE` varchar(2) DEFAULT NULL COMMENT '变更操作方式|变更操作方式|SC-单笔,BC-批量',
  `TRAN_DESC` varchar(200) DEFAULT NULL COMMENT '交易描述|交易描述',
  `BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '账户币种|账户币种 对于AIO账户和一本通账户',
  `ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '账户序号|账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `TRAN_AMT` decimal(17,2) DEFAULT NULL COMMENT '交易金额|交易金额',
  `TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型|交易类型',
  `OTH_BASE_ACCT_NO` varchar(50) DEFAULT NULL COMMENT '对方账号/卡号|对方账号/卡号',
  `OTH_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '对方账户产品类型|对方账户产品类型',
  `OTH_ACCT_CCY` varchar(3) DEFAULT NULL COMMENT '对方账户币种|对方账户币种',
  `OTH_ACCT_SEQ_NO` varchar(5) DEFAULT NULL COMMENT '对方账户序列号|对方账户序列号',
  `OTH_ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '对方账户名称|对方账户名称',
  `OTH_TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '对方交易类型|对方交易类型',
  `CHARGE_AMT` decimal(17,2) DEFAULT NULL COMMENT '收费金额|收费金额',
  `DOC_TYPE` varchar(10) DEFAULT NULL COMMENT '凭证类型|凭证类型',
  `PREFIX` varchar(10) DEFAULT NULL COMMENT '前缀|前缀',
  `VOUCHER_START_NO` varchar(50) DEFAULT NULL COMMENT '凭证起始号码|凭证起始号码',
  `VOUCHER_END_NO` varchar(50) DEFAULT NULL COMMENT '凭证终止号码|凭证终止号码',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `REVERSAL_FLAG` char(1) DEFAULT NULL COMMENT '交易是否已冲正|交易是否已冲正|Y-是,N-否 ',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `PARTITION_FLAG` char(1) DEFAULT NULL COMMENT '分区标志|分区标志|Y-是,N-否 ',
  `COMMISSION_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '代办人名称|代办人名称',
  `COMMISSION_DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '代办人证件类型|代办人证件类型',
  `COMMISSION_DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '代办人证件号码|代办人证件号码',
  `CHECK_RESULT` char(1) DEFAULT NULL COMMENT '结果|结果|S-成功,F-失败',
  `RET_STATUS` varchar(2) DEFAULT NULL COMMENT '结果状态|结果状态|CS-贷方成功,CF-贷方失败,DS-借方成功,DF-借方失败,F-失败,S-成功,P-预处理',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|26位时间戳，格式为：JAVA时间戳（yyyy-MM-dd HH:mm:ss.SSSSSS）。举例：2021-09-02 13:26:21.128323',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`TRAN_SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='公共统一流水信息表|在FM模块登记记录统一交易流水基本信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_tran_info`
--

LOCK TABLES `fm_tran_info` WRITE;
/*!40000 ALTER TABLE `fm_tran_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_tran_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_uc_areacode`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_uc_areacode` (
  `AREA_CODE` varchar(5) NOT NULL COMMENT '地区码|地区码',
  `AREA_NAME` varchar(50) NOT NULL COMMENT '区域名称|地区名称',
  `PARENT_CITY_AREA_CODE` varchar(5) NOT NULL COMMENT '所属市地区码|所属市地区码',
  `PARENT_PROV_AREA_CODE` varchar(5) NOT NULL COMMENT '所属省地区码|所属省地区码',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`AREA_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='银联地区编码表|银联地区编码表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_uc_areacode`
--

LOCK TABLES `fm_uc_areacode` WRITE;
/*!40000 ALTER TABLE `fm_uc_areacode` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_uc_areacode` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_uc_areacode_mapping`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_uc_areacode_mapping` (
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `BRANCH_NAME` varchar(200) NOT NULL COMMENT '机构名称|机构名称',
  `UC_CITY_AREA_CODE` varchar(5) NOT NULL COMMENT '银联地区码(市级)|银联地区码(市级)',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='开户机构与银联地区(市级)映射表|开户机构与银联地区(市级)映射表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_uc_areacode_mapping`
--

LOCK TABLES `fm_uc_areacode_mapping` WRITE;
/*!40000 ALTER TABLE `fm_uc_areacode_mapping` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_uc_areacode_mapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_unload_conf`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_unload_conf` (
  `SYSTEM_ID` varchar(20) NOT NULL COMMENT '系统ID|系统ID',
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `UNLOAD_FLAG` char(1) NOT NULL COMMENT '是否需要卸数|是否需要卸数|1-卸数,0-不卸数',
  `SCREEN_CONDITION` varchar(500) NOT NULL COMMENT '数据筛选条件|查询卸载数据的筛选条件',
  `NODE_ID` varchar(50) DEFAULT NULL COMMENT '数据库节点ID|对于全局参数来说，只需要卸载一个分片库的数据时，这里配上对应的数据源；不配置的话此张表涉及到的数据源都会卸载。',
  `SEGMENT_FLAG` char(1) NOT NULL COMMENT '针对大表，是否需要分段|1-分段,0-不分段',
  `SEGMENT_FIELD` varchar(50) DEFAULT NULL COMMENT '分段字段|分段字段，当SEGMENT_FLAG为1时生效',
  `SEGMENT_SIZE` int DEFAULT NULL COMMENT '分段大小|分段大小',
  `PAGE_SIZE` int DEFAULT NULL COMMENT '分页大小|分页大小',
  `FILE_MERGE_FLAG` char(1) DEFAULT NULL COMMENT '联合贷是否合并文件标志|文件是否合并|Y-是,N-否',
  `FILE_SEGMENT_SIZE` int DEFAULT NULL COMMENT '文件分段大小|文件分段大小',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`TABLE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载参数配置表|数据卸载参数配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_unload_conf`
--

LOCK TABLES `fm_unload_conf` WRITE;
/*!40000 ALTER TABLE `fm_unload_conf` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_unload_conf` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_unload_file_result`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_unload_file_result` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `FILE_NAME` varchar(200) NOT NULL COMMENT '文件名称|文件名称',
  `SEGMENT_START` varchar(50) NOT NULL COMMENT '大段起始值|大段起始值',
  `SEGMENT_END` varchar(50) NOT NULL COMMENT '大段终止值|大段终止值',
  `SEGMENT_SIZE` int NOT NULL COMMENT '分段大小|分段大小',
  `DEAL_DATE` datetime NOT NULL COMMENT '处理日期|处理日期',
  `DEAL_RESULT_FLAG` char(1) NOT NULL COMMENT '处理结果标志|处理结果标志|1-未处理,2-已处理,3-处理中,4-处理失败',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`FILE_NAME`,`SEGMENT_START`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载文件结果表|数据卸载文件结果表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_unload_file_result`
--

LOCK TABLES `fm_unload_file_result` WRITE;
/*!40000 ALTER TABLE `fm_unload_file_result` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_unload_file_result` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_unload_running`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_unload_running` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `FILE_NAME` varchar(200) NOT NULL COMMENT '文件名称|文件名称',
  `SEGMENT_START` varchar(50) DEFAULT NULL COMMENT '大段起始值|大段起始值',
  `SEGMENT_END` varchar(50) DEFAULT NULL COMMENT '大段终止值|大段终止值',
  `DEAL_RESULT_FLAG` char(1) NOT NULL COMMENT '处理结果标志|处理结果标志|1-未处理,2-已处理,3-处理中,4-处理失败',
  `FILE_INFO_SUM` varchar(10) DEFAULT NULL COMMENT '文件数据条数|文件数据条数',
  `DEAL_DATE` datetime NOT NULL COMMENT '处理日期|处理日期',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`FILE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载运行表|数据卸载运行表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_unload_running`
--

LOCK TABLES `fm_unload_running` WRITE;
/*!40000 ALTER TABLE `fm_unload_running` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_unload_running` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_user`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_user` (
  `USER_ID` varchar(30) NOT NULL COMMENT '交易柜员|柜员编号，一般复用银行员工编号',
  `USER_NAME` varchar(200) NOT NULL COMMENT '柜员名称|柜员名称',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `USER_TYPE` varchar(20) NOT NULL COMMENT '柜员类别|柜员是普通临柜柜员还是虚拟自助设备柜员|DUMMY_TELLER-虚拟柜员,TELLER_USER-普通柜员',
  `USER_SUB_TYPE` char(1) DEFAULT NULL COMMENT '柜员细类|临柜柜员时细类为Y，虚拟柜员时标记具体的虚拟柜员细类|A-ATM柜员,I-ITM柜员,Q-圈存机柜员,S-系统级虚拟柜员,Y-实体柜员',
  `USER_DESC` varchar(50) DEFAULT NULL COMMENT '柜员描述信息|柜员描述信息，目前系统暂未使用',
  `USER_LANG` varchar(30) DEFAULT NULL COMMENT '柜员语言|用于控制柜员登录柜面后界面的文字显示语种，目前系统暂未使用|E-英文,C-中文',
  `USER_LEVEL` char(1) DEFAULT NULL COMMENT '柜员级别|柜员级别，目前系统暂未使用|0-无级别,1-一级,2-二级,3-三级',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|柜员在管理上的归属机构',
  `DEPARTMENT` varchar(10) DEFAULT NULL COMMENT '部门|部门，目前系统暂未使用',
  `ROLE_ID` varchar(200) DEFAULT NULL COMMENT '角色|柜员的岗位编号或角色编号，英文逗号分隔，可以给角色上绑定尾箱限额',
  `PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '利润中心 |利润中心',
  `ACCT_EXEC` varchar(30) DEFAULT NULL COMMENT '客户经理|客户经理，目前系统暂未使用',
  `TBOOK` varchar(2) DEFAULT NULL COMMENT '账薄|账薄，目前系统暂未使用',
  `ACCOUNT_STATUS` char(1) NOT NULL COMMENT '柜员状态|柜员是正常状态还是已经删除|A-有效,D-删除',
  `APPLICATION_USER_FLAG` char(1) DEFAULT NULL COMMENT '是否应用柜员|是否应用柜员，目前系统暂未使用|Y-是,N-否',
  `EOD_SOD_ENABLED_FLAG` char(1) DEFAULT NULL COMMENT '是否批处理用户|是否批处理用户，目前系统暂未使用|Y-是,N-否',
  `INTER_BRANCH_CL` char(1) DEFAULT NULL COMMENT '是否贷款业务机构|柜员是否可以办理贷款业务|Y-是,N-否',
  `INTER_BRANCH_IND` char(1) DEFAULT NULL COMMENT '是否为内部银行|是否为内部银行，目前系统暂未使用|Y-是,N-否',
  `AUTH_LEVEL` char(1) DEFAULT NULL COMMENT '授权级别|授权级别，目前系统暂未使用|Y-允许授权,N-不允许授权',
  `APPR_USER_ID` varchar(30) DEFAULT NULL COMMENT '复核柜员|复核柜员，目前系统暂未使用',
  `CHECK_DATE` datetime DEFAULT NULL COMMENT '检查日期|检查日期，目前系统暂未使用',
  `CREATION_USER_ID` varchar(30) DEFAULT NULL COMMENT '创建柜员|创建柜员，目前系统暂未使用',
  `MAKE_DATE` datetime DEFAULT NULL COMMENT '柜员创建日期|柜员创建日期',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|柜员创建渠道，目前系统暂未使用',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`USER_ID`,`BRANCH`),
  KEY `IDX_FM_USER_2` (`BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='核心柜员信息表|核心柜员信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_user`
--

LOCK TABLES `fm_user` WRITE;
/*!40000 ALTER TABLE `fm_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fm_user_login_branch`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fm_user_login_branch` (
  `USER_ID` varchar(30) NOT NULL COMMENT '交易柜员|柜员ID',
  `LOGIN_BRANCH` varchar(20) NOT NULL COMMENT '可登录机构|默认配置柜员与除本机构之外的可登录的机构的关系数据。如果一个柜员可以登录多个机构，则配置多条关系数据。',
  PRIMARY KEY (`USER_ID`,`LOGIN_BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='柜员登录机构表|该表默认配置柜员与除本机构之外的可登录的机构的关系数据。如果一个柜员可以登录多个机构，则配置多条关系数据。';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fm_user_login_branch`
--

LOCK TABLES `fm_user_login_branch` WRITE;
/*!40000 ALTER TABLE `fm_user_login_branch` DISABLE KEYS */;
/*!40000 ALTER TABLE `fm_user_login_branch` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fw_tran_info`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fw_tran_info` (
  `SERVICE_ID` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '服务ID',
  `SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '服务唯一识别号',
  `TRAN_DATE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易日期',
  `TRAN_TIME` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易时间',
  `IN_MSG` longblob COMMENT '输入报文',
  `OUT_MSG` longblob COMMENT '输出报文',
  `RESPONSE_TYPE` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '输出响应类型',
  `END_TIME` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易完成时间',
  `SOURCE_TYPE` varchar(20) DEFAULT NULL,
  `SEQ_NO` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '渠道流水号',
  `PROGRAM_ID` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易屏幕标识',
  `STATUS` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '状态',
  `REFERENCE` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '业务参考号',
  `PLATFORM_ID` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '平台流水号',
  `USER_ID` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作柜员',
  `IP_ADDRESS` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'IP地址',
  `BRANCH_ID` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '网点',
  `COMPENSATE_SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '待补偿原交易唯一识别号',
  `WEEK_DAY` decimal(1,0) DEFAULT NULL COMMENT '日期',
  `CREATE_DATE` datetime NOT NULL COMMENT '记录创建日期',
  PRIMARY KEY (`SERVICE_NO`,`CREATE_DATE`) USING BTREE,
  KEY `FW_TRAN_INFO_IDX1` (`TRAN_DATE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='交易流水表'
/*!50100 PARTITION BY RANGE (to_days(`CREATE_DATE`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fw_tran_info`
--


--
-- Table structure for table `mb_attr_class`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_attr_class` (
  `ATTR_CLASS` varchar(10) NOT NULL COMMENT '参数分类|参数分类|ACCT-账户信息,AGREEMENT-协议信息,BALANCE-余额信息,CLIENT-客户信息,CM_CARD-卡信息,FREQUCY-期限信息,MEDIA-介质信息,PRICE-定价信息,RISK-风险信息',
  `ATTR_CLASS_DESC` varchar(50) NOT NULL COMMENT '参数分类描述|参数分类描述',
  `ATTR_CLASS_LEVEL` char(1) DEFAULT NULL COMMENT '参数分类层级|参数分类层级|1-1级,2-2级',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `PARENT_ATTR_CLASS` varchar(5) DEFAULT NULL COMMENT '上级参数分类|上级参数分类',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`ATTR_CLASS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='参数分类定义表|参数分类定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_attr_class`
--


--
-- Table structure for table `mb_attr_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_attr_type` (
  `ATTR_TYPE` varchar(10) DEFAULT NULL COMMENT '参数数据类型|参数数据类型|STRING-字符型,DOUBLE-数值型',
  `ATTR_DESC` varchar(50) NOT NULL COMMENT '属性描述|属性描述',
  `ATTR_KEY` varchar(30) NOT NULL COMMENT '参数KEY值|参数KEY值',
  `ATTR_CLASS` varchar(10) NOT NULL COMMENT '参数分类|参数分类|ACCT-账户信息,AGREEMENT-协议信息,BALANCE-余额信息,CLIENT-客户信息,CM_CARD-卡信息,FREQUCY-期限信息,MEDIA-介质信息,PRICE-定价信息,RISK-风险信息',
  `BUSI_CATEGORY` varchar(20) DEFAULT NULL COMMENT '业务分类|业务分类|RB-存款,CL-贷款,MM-货币市场,GL-总账',
  `SET_VALUE_FLAG` char(1) DEFAULT NULL COMMENT '参数值设置方式|参数值设置方式|S,M',
  `USE_METHOD` varchar(20) NOT NULL COMMENT '使用方式|使用方式|EVAL-赋值类,CTR-控制类,IND-处理逻辑,DESC-静态描述',
  `VALUE_METHOD` varchar(5) NOT NULL COMMENT '取值方式|取值方式|FD-固定,VL-取值自MB_ATTR_VALUE,RF-取值自其它参数表,MB_ATTR_VALUE中REF_TABLE中定义表名,REF_CONDITION中定义条件,YN-取值Y或N',
  `STATUS` char(1) NOT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`ATTR_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='参数定义表|参数定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_attr_type`
--


--
-- Table structure for table `mb_attr_value`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_attr_value` (
  `ATTR_KEY` varchar(30) NOT NULL COMMENT '参数KEY值|参数KEY值',
  `ATTR_VALUE` varchar(500) NOT NULL COMMENT '属性值|属性值',
  `VALUE_DESC` varchar(200) NOT NULL COMMENT '参数值描述|参数值描述',
  `REF_TABLE` varchar(50) DEFAULT NULL COMMENT '引用表名|引用表名',
  `REF_COLUMNS` varchar(50) DEFAULT NULL COMMENT '关联表描述列|关联表描述列',
  `REF_CONDITION` varchar(2000) DEFAULT NULL COMMENT '引用条件|引用条件',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  PRIMARY KEY (`ATTR_KEY`,`ATTR_VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='参数值定义表|参数值定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_attr_value`
--

--
-- Table structure for table `mb_basis_rate`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_basis_rate` (
  `INT_BASIS` varchar(10) NOT NULL COMMENT '基准利率类型|基准利率类型',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `INT_BASIS_RATE` decimal(15,8) NOT NULL COMMENT '基准利率|利率',
  `EFFECT_DATE` datetime NOT NULL COMMENT '生效日期|生效日期',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `UPDATE_DATE` datetime DEFAULT NULL COMMENT '更新日期|更新日期',
  PRIMARY KEY (`INT_BASIS`,`CCY`,`EFFECT_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='基准利率信息表|基准利率信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_basis_rate`
--


--
-- Table structure for table `mb_branch_prod`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_branch_prod` (
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `PROD_DESC` varchar(200) NOT NULL COMMENT '产品描述|解释产品具体特性',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人，重点说明，该字段含义是产品的法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BRANCH`,`PROD_TYPE`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构产品关联表|机构产品关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_branch_prod`
--

--
-- Table structure for table `mb_ccy_point_rate`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_ccy_point_rate` (
  `EXCHANGE_TYPE` varchar(10) NOT NULL COMMENT '结售汇类型|结售汇类型',
  `RATE_TYPE` varchar(10) NOT NULL COMMENT '汇率类型|汇率类型',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `FLOAT_POINT` decimal(15,8) NOT NULL COMMENT '浮动点差|浮动点差',
  `EFFECT_DATE` datetime NOT NULL COMMENT '生效日期|生效日期',
  `HIERARCHY_CODE` varchar(50) NOT NULL COMMENT '层级代码|层级代码',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`EXCHANGE_TYPE`,`RATE_TYPE`,`CCY`,`BRANCH`,`FLOAT_POINT`,`EFFECT_DATE`,`HIERARCHY_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='汇率浮动点差表|汇率浮动点差表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_ccy_point_rate`
--



--
-- Table structure for table `mb_ccy_rate`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_ccy_rate` (
  `RATE_TYPE` varchar(10) NOT NULL COMMENT '汇率类型|汇率类型',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `QUOTE_TYPE` char(1) NOT NULL COMMENT '牌价类型|牌价类型|D-直接,I-间接',
  `EXCH_BUY_RATE` decimal(15,8) NOT NULL COMMENT '汇买价|汇买价',
  `EXCH_SELL_RATE` decimal(15,8) NOT NULL COMMENT '汇卖价|汇卖价',
  `MIDDLE_RATE` decimal(15,8) NOT NULL COMMENT '中间价|中间价',
  `NOTES_BUY_RATE` decimal(15,8) DEFAULT NULL COMMENT '钞买价|钞买价',
  `NOTES_SELL_RATE` decimal(15,8) DEFAULT NULL COMMENT '钞卖价|钞卖价',
  `MAX_FLOAT_RATE` decimal(15,8) DEFAULT NULL COMMENT '最大浮动点|最大浮动点',
  `TACK_OVER_CCY_RATE_TWO` decimal(15,8) DEFAULT NULL COMMENT '基础币种2汇率中间价|该币种对基础币种2（GBP）的汇率中间价，汇丰项目专用',
  `TACK_OVER_CCY_RATE_ONE` decimal(15,8) DEFAULT NULL COMMENT '基础币种1汇率中间价|该币种对基础币种1（USD）的汇率中间价，汇丰项目专用',
  `CENTRAL_BANK_RATE` decimal(15,8) DEFAULT NULL COMMENT '央行参考汇率|央行参考汇率',
  `EFFECT_DATE` datetime NOT NULL COMMENT '生效日期|生效日期',
  `EFFECT_TIME` varchar(6) NOT NULL COMMENT '生效时间  |生效时间  ',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `COUNTRY` varchar(3) DEFAULT NULL COMMENT '国家|国家',
  PRIMARY KEY (`RATE_TYPE`,`CCY`,`BRANCH`,`EFFECT_DATE`,`EFFECT_TIME`),
  KEY `IDX_MB_CCY_RATE_1` (`CCY`,`BRANCH`,`RATE_TYPE`,`EFFECT_DATE`,`EFFECT_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='汇率牌价表|汇率牌价表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_ccy_rate`
--


--
-- Table structure for table `mb_ccy_rate_hist`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_ccy_rate_hist` (
  `RATE_TYPE` varchar(10) NOT NULL COMMENT '汇率类型|汇率类型',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `QUOTE_TYPE` char(1) NOT NULL COMMENT '牌价类型|牌价类型|D-直接,I-间接',
  `EXCH_BUY_RATE` decimal(15,8) NOT NULL COMMENT '汇买价|汇买价',
  `EXCH_SELL_RATE` decimal(15,8) NOT NULL COMMENT '汇卖价|汇卖价',
  `MIDDLE_RATE` decimal(15,8) NOT NULL COMMENT '中间价|中间价',
  `NOTES_BUY_RATE` decimal(15,8) DEFAULT NULL COMMENT '钞买价|钞买价',
  `NOTES_SELL_RATE` decimal(15,8) DEFAULT NULL COMMENT '钞卖价|钞卖价',
  `MAX_FLOAT_RATE` decimal(15,8) DEFAULT NULL COMMENT '最大浮动点|最大浮动点',
  `CENTRAL_BANK_RATE` decimal(15,8) DEFAULT NULL COMMENT '央行参考汇率|央行参考汇率',
  `EFFECT_DATE` datetime NOT NULL COMMENT '生效日期|生效日期',
  `EFFECT_TIME` varchar(6) NOT NULL COMMENT '生效时间  |生效时间  ',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  PRIMARY KEY (`RATE_TYPE`,`CCY`,`BRANCH`,`EFFECT_DATE`,`EFFECT_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='汇率牌价历史表|汇率牌价历史表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_ccy_rate_hist`
--

--
-- Table structure for table `mb_cof_vof_rate`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_cof_vof_rate` (
  `COUNTRY` varchar(3) NOT NULL COMMENT '国家|国家',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|所属机构号',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `BEGIN_DATE` varchar(10) NOT NULL COMMENT '起始日期|起始日期',
  `TERM` varchar(5) NOT NULL COMMENT '存期期限|期限',
  `COFR_RATE` decimal(15,8) DEFAULT NULL COMMENT '资金成本率|资金成本率',
  `VOFR_RATE` decimal(15,8) DEFAULT NULL COMMENT '资金收益利率|资金收益利率',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `TERMINATE_DATE` varchar(10) DEFAULT NULL COMMENT '结束日期|结束日期',
  PRIMARY KEY (`COUNTRY`,`BRANCH`,`CCY`,`PROD_TYPE`,`BEGIN_DATE`,`TERM`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='司库利率表|司库利率(汇丰项目专用)';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_cof_vof_rate`
--

LOCK TABLES `mb_cof_vof_rate` WRITE;
/*!40000 ALTER TABLE `mb_cof_vof_rate` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_cof_vof_rate` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_define_column_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_define_column_type` (
  `ATTR_TYPE` varchar(10) DEFAULT NULL COMMENT '参数数据类型|参数数据类型|STRING-字符型,DATE-日期类型,INT-数字类型',
  `ATTR_DESC` varchar(50) NOT NULL COMMENT '属性描述|属性描述',
  `ATTR_KEY` varchar(30) NOT NULL COMMENT '参数KEY值|参数KEY值',
  `BUSI_CATEGORY` varchar(20) DEFAULT NULL COMMENT '业务分类|业务分类|RB-存款,CL-贷款,ALL-所有',
  `VALUE_METHOD` varchar(5) NOT NULL COMMENT '取值方式|取值方式|FD-固定,VL-取值自MB_ATTR_VALUE,RF-取值自其它参数表,MB_ATTR_VALUE中REF_TABLE中定义表名,YN-取值Y或N',
  `ATTR_LENGTH` varchar(5) DEFAULT NULL COMMENT '参数精度|参数精度',
  `STATUS` char(1) NOT NULL COMMENT '状态|状态|A-有效,F-无效',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `LENGTH` varchar(50) DEFAULT NULL COMMENT '长度|长度',
  PRIMARY KEY (`ATTR_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='自定义字段参数定义表|自定义字段参数定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_define_column_type`
--

LOCK TABLES `mb_define_column_type` WRITE;
/*!40000 ALTER TABLE `mb_define_column_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_define_column_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_define_column_value`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_define_column_value` (
  `ATTR_KEY` varchar(30) NOT NULL COMMENT '参数KEY值|参数KEY值',
  `ATTR_VALUE` varchar(500) NOT NULL COMMENT '属性值|属性值',
  `VALUE_DESC` varchar(200) NOT NULL COMMENT '参数值描述|参数值描述',
  `REF_TABLE` varchar(50) DEFAULT NULL COMMENT '引用表名|引用表名',
  `REF_COLUMNS` varchar(50) DEFAULT NULL COMMENT '关联表描述列|关联表描述列',
  `REF_CONDITION` varchar(2000) DEFAULT NULL COMMENT '引用条件|引用条件',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  PRIMARY KEY (`ATTR_KEY`,`ATTR_VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='参数值定义表|参数值定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_define_column_value`
--

LOCK TABLES `mb_define_column_value` WRITE;
/*!40000 ALTER TABLE `mb_define_column_value` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_define_column_value` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_duad_ccy_rate`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_duad_ccy_rate` (
  `RATE_TYPE` varchar(10) NOT NULL COMMENT '汇率类型|汇率类型',
  `QUOTE_TYPE` char(1) NOT NULL COMMENT '牌价类型|牌价类型|D-直接,I-间接',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `SOURCE_CCY` varchar(3) NOT NULL COMMENT '源币种|源币种',
  `TARGET_CCY` varchar(3) NOT NULL COMMENT '目标币种|目标币种',
  `NOTES_BUY_RATE` decimal(15,8) DEFAULT NULL COMMENT '钞买价|钞买价',
  `NOTES_SELL_RATE` decimal(15,8) DEFAULT NULL COMMENT '钞卖价|钞卖价',
  `EXCH_BUY_RATE` decimal(15,8) NOT NULL COMMENT '汇买价|汇买价',
  `EXCH_SELL_RATE` decimal(15,8) NOT NULL COMMENT '汇卖价|汇卖价',
  `MIDDLE_RATE` decimal(15,8) NOT NULL COMMENT '中间价|中间价',
  `MAX_FLOAT_RATE` decimal(15,8) DEFAULT NULL COMMENT '最大浮动点|最大浮动点',
  `CENTRAL_BANK_RATE` decimal(15,8) DEFAULT NULL COMMENT '央行参考汇率|央行参考汇率',
  `EFFECT_DATE` datetime NOT NULL COMMENT '生效日期|生效日期',
  `EFFECT_TIME` varchar(6) NOT NULL COMMENT '生效时间  |生效时间  ',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`RATE_TYPE`,`BRANCH`,`SOURCE_CCY`,`TARGET_CCY`,`EFFECT_DATE`,`EFFECT_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='货币对汇率牌价表|货币对汇率牌价表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_duad_ccy_rate`
--


--
-- Table structure for table `mb_element`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_element` (
  `ELEMENT_ID` varchar(50) NOT NULL COMMENT '因子名称|因子名称',
  `ELEMENT_DESC` varchar(50) NOT NULL COMMENT '因子描述|因子描述',
  `ELEMENT_TYPE` char(1) NOT NULL COMMENT '因子类型|因子类型|E-汇率因子,F-费用因子,L-利率因子',
  `ELEMENT_ATTR` varchar(10) NOT NULL COMMENT '因子数据类型|因子数据类型|DATE-日期型,NUMBER-数字型,STRING-字符型',
  `ELEMENT_LENGTH` varchar(10) NOT NULL COMMENT '因子长度|因子长度',
  `TABLE_NAME` varchar(50) DEFAULT NULL COMMENT '表名|表名',
  `IRL_FIELD_VALUE` varchar(50) DEFAULT NULL COMMENT '因子所对应的范围|取值为表里DOMIN对应的，例如事件类型，取值为“EVENT_VALUE”,否则取值为字段加描述，例如“CCY,CCY_DESC”',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`ELEMENT_ID`,`ELEMENT_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='因子表|因子表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_element`
--

LOCK TABLES `mb_element` WRITE;
/*!40000 ALTER TABLE `mb_element` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_element` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_element_group`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_element_group` (
  `GROUP_TYPE` varchar(10) NOT NULL COMMENT '规则分组类型|规则分组类型',
  `ELEMENT_ID` varchar(50) NOT NULL COMMENT '因子名称|因子名称',
  `ELEMENT_DESC` varchar(50) NOT NULL COMMENT '因子描述|因子描述',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`GROUP_TYPE`,`ELEMENT_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='因子分组定义表|因子分组定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_element_group`
--

LOCK TABLES `mb_element_group` WRITE;
/*!40000 ALTER TABLE `mb_element_group` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_element_group` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_event_attr`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_event_attr` (
  `EVENT_TYPE` varchar(20) NOT NULL COMMENT '事件类型|事件类型',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `ASSEMBLE_ID` varchar(50) NOT NULL COMMENT '组件ID|组件ID',
  `ASSEMBLE_TYPE` varchar(10) NOT NULL COMMENT '组件类型|组件类型|EVENT-事件,PART-指标,ATTR-参数',
  `ASSEMBLE_RULE` char(1) DEFAULT NULL COMMENT '指标运行规则|指标运行规则|F-不运行,E-前置指标,C-后置指标',
  `ATTR_VALUE` varchar(500) DEFAULT NULL COMMENT '属性值|属性值',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`EVENT_TYPE`,`SEQ_NO`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='事件参数定义表|事件参数定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_event_attr`
--

LOCK TABLES `mb_event_attr` WRITE;
/*!40000 ALTER TABLE `mb_event_attr` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_event_attr` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_event_class`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_event_class` (
  `EVENT_CLASS` varchar(10) NOT NULL COMMENT '事件分类|事件分类',
  `EVENT_CLASS_LEVEL` char(1) DEFAULT NULL COMMENT '事件分类层级|事件分类层级|1-1级,2-2级',
  `EVENT_CLASS_DESC` varchar(50) NOT NULL COMMENT '事件分类描述|事件分类描述',
  `PARENT_EVENT_CLASS` varchar(20) DEFAULT NULL COMMENT '上级事件分类|上级事件分类',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`EVENT_CLASS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='事件分类定义表|事件分类定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_event_class`
--

LOCK TABLES `mb_event_class` WRITE;
/*!40000 ALTER TABLE `mb_event_class` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_event_class` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_event_default_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_event_default_type` (
  `EVENT_DEFAULT_TYPE` varchar(10) NOT NULL COMMENT '基础事件类型|基础事件类型',
  `EVENT_DEFAULT_DESC` varchar(50) NOT NULL COMMENT '基础事件描述|基础事件描述',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`EVENT_DEFAULT_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品基础事件定义表|产品基础事件定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_event_default_type`
--

LOCK TABLES `mb_event_default_type` WRITE;
/*!40000 ALTER TABLE `mb_event_default_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_event_default_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_event_part`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_event_part` (
  `EVENT_TYPE` varchar(20) NOT NULL COMMENT '事件类型|事件类型',
  `ASSEMBLE_ID` varchar(50) NOT NULL COMMENT '组件ID|组件ID',
  `ATTR_KEY` varchar(30) NOT NULL COMMENT '参数KEY值|参数KEY值',
  `ATTR_VALUE` varchar(500) DEFAULT NULL COMMENT '属性值|属性值',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`EVENT_TYPE`,`ASSEMBLE_ID`,`ATTR_KEY`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='事件与指标关系定义表|事件与指标关系定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_event_part`
--

LOCK TABLES `mb_event_part` WRITE;
/*!40000 ALTER TABLE `mb_event_part` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_event_part` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_event_part_relation`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_event_part_relation` (
  `EVENT_TYPE` varchar(20) NOT NULL COMMENT '事件类型|事件类型',
  `ASSEMBLE_ID` varchar(50) NOT NULL COMMENT '组件ID|组件ID',
  `ASSEMBLE_TYPE` varchar(10) NOT NULL COMMENT '组件类型|组件类型|EVENT-事件,PART-指标,ATTR-参数',
  `PART_DESC` varchar(50) DEFAULT NULL COMMENT '指标类型描述|指标类型描述',
  `STATUS` char(1) DEFAULT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`EVENT_TYPE`,`ASSEMBLE_ID`,`ASSEMBLE_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='事件与指标规则定义表|事件与指标规则定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_event_part_relation`
--

LOCK TABLES `mb_event_part_relation` WRITE;
/*!40000 ALTER TABLE `mb_event_part_relation` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_event_part_relation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_event_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_event_type` (
  `EVENT_TYPE` varchar(20) NOT NULL COMMENT '事件类型|事件类型',
  `EVENT_CLASS` varchar(10) NOT NULL COMMENT '事件分类|事件分类',
  `EVENT_DESC` varchar(100) NOT NULL COMMENT '事件类型描述|事件类型描述',
  `STANDARD_FLAG` char(1) DEFAULT NULL COMMENT '是否标准模板|是否标准模板|Y-是,N-否',
  `PROCESS_METHOD` char(1) NOT NULL COMMENT '指标处理方式|指标处理方式|A-检查类,C-提交类',
  `STATUS` char(1) NOT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`EVENT_TYPE`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='事件类型定义表|事件类型定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_event_type`
--

LOCK TABLES `mb_event_type` WRITE;
/*!40000 ALTER TABLE `mb_event_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_event_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_exchange_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_exchange_type` (
  `RATE_TYPE` varchar(10) NOT NULL COMMENT '汇率类型|汇率类型',
  `RATE_TYPE_DESC` varchar(50) NOT NULL COMMENT '汇率类型描述|汇率类型描述',
  `QUOTE_CCY` varchar(3) NOT NULL COMMENT '报价币种|报价币种',
  `FLOAT_TYPE` varchar(20) DEFAULT NULL COMMENT '浮动方式|浮动方式|02-取最大,03-取最小,04-取平均值,05-取叠加,06-取权重',
  `HBD_FLAG` char(1) NOT NULL COMMENT '货币对汇率标志|货币对汇率标志|Y-是,N-否',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`RATE_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='汇率类型表|汇率类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_exchange_type`
--

LOCK TABLES `mb_exchange_type` WRITE;
/*!40000 ALTER TABLE `mb_exchange_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_exchange_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_fee_catalog`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_fee_catalog` (
  `CATALOG_NO` varchar(50) NOT NULL COMMENT '目录序号|目录序号',
  `FEE_CLASS` varchar(20) NOT NULL COMMENT '费用分类 |目录第一级编号，用1位数字表示',
  `FEE_CLASS_NAME` varchar(200) NOT NULL COMMENT '费用分类名称|费用分类名称',
  `FEE_SUB_CLASS` varchar(20) DEFAULT NULL COMMENT '费用细类|目录第二级编号，用2位数字表示',
  `FEE_SUB_CLASS_NAME` varchar(200) DEFAULT NULL COMMENT '费用细类名称|费用细类名称',
  `FEE_GROUP` varchar(20) DEFAULT NULL COMMENT '费用分组|目录第三级编号，用2位数字表示',
  `FEE_GROUP_NAME` varchar(200) DEFAULT NULL COMMENT '费用分组名称|费用分组名称',
  `BASE_FEE` varchar(20) DEFAULT NULL COMMENT '基础费用，带目录结构|四级目录，用7位数字表示',
  `BASE_FEE_NAME` varchar(200) DEFAULT NULL COMMENT '基础费用名称|基础费用名称',
  `FEE_TYPE` varchar(20) DEFAULT NULL COMMENT '费用类型|五级目录，用12位数字表示',
  `FEE_NAME` varchar(200) DEFAULT NULL COMMENT '费用名称|费用名称',
  `FEE_DESC` varchar(100) DEFAULT NULL COMMENT '费用类型描述|费用类型描述',
  `FEE_STATUS` char(1) DEFAULT NULL COMMENT '费用状态|费用状态|0-待生效,1-生效,2-停办,3-失效',
  `MANAGE_DEPT` varchar(200) DEFAULT NULL COMMENT '管理部门|管理部门',
  `SYSTEM_ID` varchar(20) DEFAULT NULL COMMENT '系统ID|系统ID',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`CATALOG_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='费用目录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_fee_catalog`
--

LOCK TABLES `mb_fee_catalog` WRITE;
/*!40000 ALTER TABLE `mb_fee_catalog` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_fee_catalog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_fee_mapping`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_fee_mapping` (
  `IRL_SEQ_NO` varchar(50) NOT NULL COMMENT '费率编号|费率编号',
  `SERVICE_ID_RULE` varchar(200) DEFAULT NULL COMMENT '规则匹配代码|规则匹配代码',
  `FEE_TYPE` varchar(20) NOT NULL COMMENT '费用类型|费率类型',
  `PROD_TYPE_RULE` varchar(200) NOT NULL COMMENT '费用启用规则产品类型|费用启用规则产品类型',
  `BRANCH_RULE` varchar(200) NOT NULL COMMENT '机构匹配规则|机构匹配规则 用|分割多个机构',
  `CLIENT_TYPE_RULE` varchar(10) NOT NULL COMMENT '费用启用规则客户类型|费用启用规则客户类型',
  `CATEGORY_TYPE_RULE` varchar(200) NOT NULL COMMENT '客户类型细类|客户类型细类 费用匹配规则用|拼接',
  `DOC_TYPE_RULE` varchar(200) NOT NULL COMMENT '凭证类型启用规则|凭证类型启用规则',
  `TRAN_TYPE_RULE` varchar(200) NOT NULL COMMENT '费用启用规则交易类型|费用启用规则交易类型',
  `EVENT_TYPE_RULE` varchar(200) NOT NULL COMMENT '费用启用规则事件类型|费用启用规则事件类型',
  `CCY_RULE` varchar(200) NOT NULL COMMENT '费用启用规则币种|费用启用规则币种 费用匹配规则用|拼接',
  `COMPANY_RULE` varchar(200) DEFAULT NULL COMMENT '费用启用规则法人|费用启用规则法人 费用匹配规则用|拼接',
  `IS_LOCAL_RULE` varchar(3) NOT NULL COMMENT '跨行标志|跨行标志|B-跨他行,O-跨机构,S-同机构,ALL-全部',
  `RULE_FLAG` char(1) DEFAULT NULL COMMENT '是否使用规则|是否使用规则|Y-是,N-否',
  `AREA_CODE_RULE` varchar(200) NOT NULL COMMENT '地区匹配规则|地区匹配规则 费用匹配规则 用|拼接',
  `NEW_STATUS_RULE` varchar(200) NOT NULL COMMENT '新凭证状态启用规则|新凭证状态启用规则',
  `OLD_STATUS_RULE` varchar(3) NOT NULL COMMENT '原凭证状态启用规则|原凭证状态启用规则',
  `PROD_GROUP_RULE` varchar(3) NOT NULL COMMENT '费用启用规则产品组|费用启用规则产品组|RB-负债,CL-资产,GL-总账,MM-货币市场,ALL-全部',
  `SOURCE_TYPE_RULE` varchar(200) NOT NULL COMMENT '渠道类型配置值|渠道类型配置值',
  `URGENT_FLAG_RULE` varchar(3) NOT NULL COMMENT '加急标志|加急标志|Y-加急,N-不加急',
  `START_DATE` datetime DEFAULT NULL COMMENT '开始日期|开始日期',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|结束日期',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`IRL_SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='费用启用规则表|费用启用规则表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_fee_mapping`
--

LOCK TABLES `mb_fee_mapping` WRITE;
/*!40000 ALTER TABLE `mb_fee_mapping` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_fee_mapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_fee_matrix`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_fee_matrix` (
  `MATRIX_NO` varchar(50) NOT NULL COMMENT '阶梯序号|阶梯序号',
  `IRL_SEQ_NO` varchar(50) NOT NULL COMMENT '费率编号|费率编号',
  `BOUNDARY` bigint DEFAULT NULL COMMENT '缺口值|缺口值 描述费用计算的金额类型',
  `FEE_AMT` decimal(17,2) DEFAULT NULL COMMENT '费用金额|费用金额',
  `FEE_RATE` decimal(15,8) DEFAULT NULL COMMENT '费率（%）|费率（%）',
  `INT_TYPE` varchar(5) DEFAULT NULL COMMENT '利率类型|利率类型',
  `FLOAT_RATE` decimal(15,8) DEFAULT NULL COMMENT '浮动利率|浮动利率',
  `RULE_EXPRESSION` varchar(200) DEFAULT NULL COMMENT '规则表达式  |规则表达式  ',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`MATRIX_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='费率矩阵信息表|费率矩阵信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_fee_matrix`
--

LOCK TABLES `mb_fee_matrix` WRITE;
/*!40000 ALTER TABLE `mb_fee_matrix` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_fee_matrix` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_fee_package`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_fee_package` (
  `PACKAGE_ID` varchar(50) NOT NULL COMMENT '套餐代码|套餐代码',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `PACKAGE_TYPE` varchar(3) DEFAULT NULL COMMENT '套餐类型|套餐类型|NUM-笔数,AMT-金额,ALL-金额加笔数 ',
  `PACKAGE_DESC` varchar(50) DEFAULT NULL COMMENT '套餐描述|套餐描述',
  `PACKAGE_MODE` varchar(3) DEFAULT NULL COMMENT '套餐模式|套餐模式|SUB-递减,ADD-递加',
  `PACKAGE_CCY` varchar(3) DEFAULT NULL COMMENT '套餐币种取自|套餐币种取自',
  `PACKAGE_FEE_TYPE` varchar(10) DEFAULT NULL COMMENT '套餐费费用类型|套餐费费用类型取自',
  `PACKAGE_PERIOD_FREQ` varchar(5) DEFAULT NULL COMMENT '套餐频率|套餐频率取自',
  `PACKAGE_NUM` int DEFAULT NULL COMMENT '可抵扣笔数|可抵扣笔数',
  `PACKAGE_AMT` decimal(17,2) DEFAULT NULL COMMENT '可抵扣金额|可抵扣金额',
  `PACKAGE_STATUS` char(1) DEFAULT NULL COMMENT '套餐状态|套餐状态|A-启用,C-停用 ',
  `SETTLE_AMT` decimal(17,2) DEFAULT NULL COMMENT '结算金额|结算金额',
  `SETTLE_CCY` varchar(3) DEFAULT NULL COMMENT '结算币种|结算币种',
  `PROCESS_MODE` varchar(3) DEFAULT NULL COMMENT '剩余费用处理方式|剩余费用处理方式|CON-自动延续,CLE-清零 ',
  `PROCESS_ORDER` varchar(3) DEFAULT NULL COMMENT '费用抵扣顺序|抵扣顺序|NTA-先笔数后金额,ATN-先金额后笔数 ',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|结束日期',
  `DEAL_DAY` varchar(2) DEFAULT NULL COMMENT '处理日|处理日',
  `NEXT_DEAL_DATE` datetime DEFAULT NULL COMMENT '下一处理日|下一处理日',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`PACKAGE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='费用套餐表|费用套餐表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_fee_package`
--

LOCK TABLES `mb_fee_package` WRITE;
/*!40000 ALTER TABLE `mb_fee_package` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_fee_package` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_fee_rate`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_fee_rate` (
  `IRL_SEQ_NO` varchar(50) NOT NULL COMMENT '费率编号|费率编号',
  `FEE_TYPE` varchar(20) NOT NULL COMMENT '费用类型|费率类型',
  `EFFECT_DATE` datetime NOT NULL COMMENT '生效日期|生效日期',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `HIGH_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '日间上限|日间上限',
  `LOW_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '日间下限|日间金额下限',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `MIN_RATE` decimal(15,8) DEFAULT NULL COMMENT '最小利率|最小利率',
  `MAX_RATE` decimal(15,8) DEFAULT NULL COMMENT '最大利率|最大利率',
  PRIMARY KEY (`IRL_SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='费率信息表|费率信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_fee_rate`
--

LOCK TABLES `mb_fee_rate` WRITE;
/*!40000 ALTER TABLE `mb_fee_rate` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_fee_rate` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_fee_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_fee_type` (
  `FEE_TYPE` varchar(20) NOT NULL COMMENT '费用类型|费率类型',
  `FEE_DESC` varchar(100) NOT NULL COMMENT '费用类型描述|费用类型描述',
  `PROD_GRP` varchar(3) NOT NULL COMMENT '产品组|产品组|RB-负债,CL-资产,GL-总账,MM-货币市场,ALL-全部',
  `FEE_ITEM` varchar(10) DEFAULT NULL COMMENT '费用项目代码|费用项目代码',
  `FEE_MODE` char(1) NOT NULL COMMENT '费率计算方式|费率计算方式|F-固定金额,R-固定比例,B-固定金额+比例,S-差额累进,T-全额累进,L-按基准利率收费,A-按数量',
  `FEE_AMT_ID` varchar(30) DEFAULT NULL COMMENT '费用计算金额编码|费用计算金额编码',
  `BOUNDARY_AMT_ID` varchar(30) DEFAULT NULL COMMENT '缺口计算金额编码|缺口计算金额编码',
  `BOUNDARY_DESC` varchar(50) DEFAULT NULL COMMENT '缺口描述|缺口描述',
  `BO_IND` char(1) NOT NULL COMMENT '日终/联机标志|日终/联机标志|O-联机,B-日终批量',
  `CCY_FLAG` char(1) NOT NULL COMMENT '收费币种标识|收费币种标识|T-交易币种收费,S-指定币种收费',
  `CONVERT_FLAG` char(1) DEFAULT NULL COMMENT '折算标志 |折算标志 |B-比率前折算,A-比率后折算',
  `DISC_TYPE` varchar(2) NOT NULL COMMENT '折扣类型|费用的折扣方式，打折或者根据定价区不同的费用值|01-折上折,02-取最大,03-取最小,04-取平均值,06-取权重',
  `MB_CCY_TYPE` varchar(3) NOT NULL COMMENT '目标收费币种|当收费币种标识为S时该字段才起作用',
  `PROFIT_ALLOT_FLAG` char(1) DEFAULT NULL COMMENT '是否需要分润|是否需要分润|Y-是,N-否',
  `PROFIT_AMORTIZE_FLAG` char(1) DEFAULT NULL COMMENT '是否需要摊销|是否需要摊销|Y-是,N-否',
  `AMORTIZE_TIME_TYPE` char(1) DEFAULT NULL COMMENT '摊销时间类型|摊销时间类型|F-期初,L-期末,D-周期内固定日期',
  `AMORTIZE_PERIOD_TYPE` char(1) DEFAULT NULL COMMENT '摊销期限类型|摊销期限类型|Y-年,Q-季,M-月,W-周,D-日',
  `AMORTIZE_MONTH` varchar(2) DEFAULT NULL COMMENT '摊销月|摊销月',
  `AMORTIZE_DAY` varchar(2) DEFAULT NULL COMMENT '摊销日|摊销日',
  `TAX_TYPE` varchar(5) DEFAULT NULL COMMENT '税种|税种|VAT-增值税,ADT-附加税',
  `OPEN_BRANCH_PERCENT` decimal(15,8) DEFAULT NULL COMMENT '账户行比例|账户行比例',
  `TRAN_BRANCH_PERCENT` decimal(15,8) DEFAULT NULL COMMENT '交易行比例,记录百分数|交易行比例,记录百分数',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `ACCR_FLAG` char(1) DEFAULT NULL COMMENT '是否需要计提|是否需要计提|Y-是,N-否|费用是否需要计提|是否需要计提|Y-是,N-否|Y-是,N-否',
  PRIMARY KEY (`FEE_TYPE`,`MB_CCY_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='费用类型表|费用类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_fee_type`
--

LOCK TABLES `mb_fee_type` WRITE;
/*!40000 ALTER TABLE `mb_fee_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_fee_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_file_deal_status`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_file_deal_status` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `EVENT_TYPE` varchar(20) DEFAULT NULL COMMENT '事件类型|事件类型',
  `BRANCH_ID` varchar(50) DEFAULT NULL COMMENT '机构号|机构代码',
  `EVENT_KEY` varchar(20) DEFAULT NULL COMMENT '事件关键|事件关键',
  `FILE_PATH` varchar(200) DEFAULT NULL COMMENT '文件路径|文件路径',
  `TASK_ID` varchar(50) NOT NULL COMMENT '运行节点 |运行节点 ',
  `DEAL_STATUS` char(1) DEFAULT NULL COMMENT '处理状态|处理状态|0-未处理,1-已处理',
  `START_DATE` datetime NOT NULL COMMENT '开始日期|开始日期',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|结束日期',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`,`TASK_ID`,`START_DATE`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='文件处理状态表|文件处理状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_file_deal_status`
--

LOCK TABLES `mb_file_deal_status` WRITE;
/*!40000 ALTER TABLE `mb_file_deal_status` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_file_deal_status` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_inner_open_parameter`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_inner_open_parameter` (
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|所属机构号',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `PROD_DESC` varchar(200) DEFAULT NULL COMMENT '产品描述|解释产品具体特性',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `ACCT_NAME` varchar(200) DEFAULT NULL COMMENT '账户名称|账户名称，一般指中文账户名称',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BRANCH`,`PROD_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='内部开户参数|内部开户参数,华兴合并，标版暂未使用';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_inner_open_parameter`
--

LOCK TABLES `mb_inner_open_parameter` WRITE;
/*!40000 ALTER TABLE `mb_inner_open_parameter` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_inner_open_parameter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_int_basis`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_int_basis` (
  `INT_BASIS` varchar(10) NOT NULL COMMENT '基准利率类型|基准利率类型',
  `INT_BASIS_DESC` varchar(50) NOT NULL COMMENT '基准利率类型描述|基准利率类型描述',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`INT_BASIS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='基准利率类型表|基准利率类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_int_basis`
--

LOCK TABLES `mb_int_basis` WRITE;
/*!40000 ALTER TABLE `mb_int_basis` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_int_basis` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_int_effect_rule`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_int_effect_rule` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `INT_TYPE` varchar(5) DEFAULT NULL COMMENT '利率类型|利率类型',
  `EVENT_TYPE` varchar(20) DEFAULT NULL COMMENT '事件类型|事件类型',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `INT_CLASS` varchar(5) DEFAULT NULL COMMENT '利息分类|利息分类|INT-正常利息,ODI-复利,PDUE-超期利息',
  `EFFECT_DATE_TYPE` varchar(10) DEFAULT NULL COMMENT '生效日期类型|生效日期类型',
  `GEAR_AMT` decimal(17,2) DEFAULT NULL COMMENT '档位金额|档位金额',
  `GEAR_DAYS` int DEFAULT NULL COMMENT '档位天数|档位天数',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='利率生效日类型规则表|利率生效日类型规则表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_int_effect_rule`
--

LOCK TABLES `mb_int_effect_rule` WRITE;
/*!40000 ALTER TABLE `mb_int_effect_rule` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_int_effect_rule` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_int_matrix`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_int_matrix` (
  `MATRIX_NO` varchar(50) NOT NULL COMMENT '阶梯序号|阶梯序号',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `INT_TYPE` varchar(5) NOT NULL COMMENT '利率类型|利率类型',
  `YEAR_BASIS` varchar(3) NOT NULL COMMENT '年基准天数|年基准天数|360-按360天计算日利率,365-按365天计算日利率,366-按366天计算日利率',
  `EFFECT_DATE` datetime NOT NULL COMMENT '生效日期|生效日期',
  `INT_BASIS` varchar(10) DEFAULT NULL COMMENT '基准利率类型|基准利率类型',
  `BASE_RATE` decimal(15,8) DEFAULT NULL COMMENT '基础汇率|基础汇率',
  `PERIOD_FREQ` varchar(5) DEFAULT NULL COMMENT '频率id|频率id',
  `DAY_NUM` int DEFAULT NULL COMMENT '每期天数|DAY_MTH为D时，取ADD_NO值；为M时，值为7* ADD_NO；为Y时，值为360* ADD_NO',
  `MATRIX_AMT` decimal(17,2) NOT NULL COMMENT '阶梯金额|阶梯金额',
  `ACTUAL_RATE` decimal(15,8) DEFAULT NULL COMMENT '行内利率|在人行基准利率调整后对客发布的行内利率',
  `DISC_RATE` decimal(15,8) DEFAULT NULL COMMENT '利率折扣|利率折扣',
  `SPREAD_PERCENT` decimal(15,8) DEFAULT NULL COMMENT '浮动百分比|浮动百分比',
  `SPREAD_RATE` decimal(15,8) DEFAULT NULL COMMENT '浮动点数|浮动点数',
  `MAX_PERCENT` decimal(15,8) DEFAULT NULL COMMENT '最大上浮百分比|最大上浮百分比',
  `MIN_PERCENT` decimal(15,8) DEFAULT NULL COMMENT '最小上浮百分比|优先级低于最小利率',
  `MIN_RATE` decimal(15,8) DEFAULT NULL COMMENT '最小利率|最小利率',
  `MAX_RATE` decimal(15,8) DEFAULT NULL COMMENT '最大利率|最大利率',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `MIN_SPREAD_RATE` decimal(15,8) DEFAULT NULL COMMENT '最小浮动点数|最小浮动点数',
  `MIN_SPREAD_PERCENT` decimal(15,8) DEFAULT NULL COMMENT '最小浮动比例|最小浮动比例',
  `MAX_SPREAD_RATE` decimal(15,8) DEFAULT NULL COMMENT '最大浮动点数|最大浮动点数',
  `MAX_SPREAD_PERCENT` decimal(15,8) DEFAULT NULL COMMENT '最大浮动比例|最大浮动比例',
  PRIMARY KEY (`MATRIX_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='利率税率阶梯表|利率税率阶梯表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_int_matrix`
--

LOCK TABLES `mb_int_matrix` WRITE;
/*!40000 ALTER TABLE `mb_int_matrix` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_int_matrix` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_int_rule`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_int_rule` (
  `RULE_NO` varchar(20) NOT NULL COMMENT '规则编号  |规则编号  ',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|产品类型',
  `EVENT_TYPE` varchar(20) DEFAULT NULL COMMENT '事件类型|事件类型',
  `RULE_CODE` varchar(10) DEFAULT NULL COMMENT '规则代码|规则代码',
  `AUTH_LEVEL` char(1) DEFAULT NULL COMMENT '授权级别|授权级别|Y-允许授权,N-不允许授权',
  `MAX_RATE` decimal(15,8) DEFAULT NULL COMMENT '最大利率|最大利率',
  `MIN_RATE` decimal(15,8) DEFAULT NULL COMMENT '最小利率|最小利率',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`RULE_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='利率规则配置表|利率规则配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_int_rule`
--

LOCK TABLES `mb_int_rule` WRITE;
/*!40000 ALTER TABLE `mb_int_rule` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_int_rule` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_int_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_int_type` (
  `INT_TAX_TYPE` varchar(5) NOT NULL COMMENT '利率税率类型|利率税率类型',
  `INT_TAX_TYPE_DESC` varchar(50) NOT NULL COMMENT '利率税率类型描述|利率税率类型描述',
  `PROD_GRP` varchar(3) NOT NULL COMMENT '产品组|产品组|RB-负债,CL-资产,GL-总账,MM-货币市场,ALL-全部',
  `INT_TAX_FLAG` varchar(3) NOT NULL COMMENT '利率类型税率类型标志|利率类型税率类型标志|I-利率类型,T-税率类型',
  `RATE_LADDER` char(1) NOT NULL COMMENT '利息计算模型|利息计算模型|F-固定,M-矩阵',
  `TAX_LADDER` char(1) DEFAULT NULL COMMENT '税率计算模型|税率计算模型|A-增值税,B-个人所得税',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`INT_TAX_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='利率税率类型表|利率税率类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_int_type`
--

LOCK TABLES `mb_int_type` WRITE;
/*!40000 ALTER TABLE `mb_int_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_int_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_matrix_balance`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_matrix_balance` (
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `ACCT_BALANCE` decimal(17,2) NOT NULL COMMENT '账户余额|账户余额，非相等关系，取余额最小值，左闭右开区间',
  `DIST_RATE` decimal(5,2) NOT NULL COMMENT '抵扣百分比|抵扣百分比',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PROD_TYPE`,`ACCT_BALANCE`,`DIST_RATE`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='阶梯余额信息表|登记阶梯余额信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_matrix_balance`
--

LOCK TABLES `mb_matrix_balance` WRITE;
/*!40000 ALTER TABLE `mb_matrix_balance` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_matrix_balance` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_part_attr`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_part_attr` (
  `PART_TYPE` varchar(20) NOT NULL COMMENT '指标类型|指标类型',
  `ATTR_KEY` varchar(30) NOT NULL COMMENT '参数KEY值|参数KEY值',
  `ATTR_VALUE` varchar(500) DEFAULT NULL COMMENT '属性值|属性值',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PART_TYPE`,`ATTR_KEY`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='指标参数定义表|指标参数定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_part_attr`
--

LOCK TABLES `mb_part_attr` WRITE;
/*!40000 ALTER TABLE `mb_part_attr` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_part_attr` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_part_class`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_part_class` (
  `PART_CLASS` varchar(20) NOT NULL COMMENT '指标分类|指标分类',
  `PART_CLASS_DESC` varchar(50) NOT NULL COMMENT '指标分类描述|指标分类描述',
  `PART_CLASS_LEVEL` char(1) DEFAULT NULL COMMENT '指标分类层级|指标分类层级|1-1级,2-2级',
  `PARENT_PART_CLASS` varchar(5) DEFAULT NULL COMMENT '上级指标分类|上级指标分类',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PART_CLASS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='指标分类定义表|指标分类定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_part_class`
--

LOCK TABLES `mb_part_class` WRITE;
/*!40000 ALTER TABLE `mb_part_class` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_part_class` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_part_ctl`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_part_ctl` (
  `TRAN_TYPE` varchar(10) NOT NULL COMMENT '交易类型|交易类型',
  `EVENT_TYPE` varchar(20) NOT NULL COMMENT '事件类型|事件类型',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `AMT_TYPE` varchar(10) NOT NULL COMMENT '金额类型|金额类型|BAL-余额,DDA-发放金额,INTP-逾期利息,LIM-额度金额,OD-透支金额,ODIP-逾期复利,ODPP-逾期罚息,OSL-未到期本金,PRD-逾期本金,PRI-本金',
  `SOURCE_TYPE` varchar(10) NOT NULL COMMENT '渠道类型|渠道类型',
  `ASSEMBLE_ID` varchar(50) NOT NULL COMMENT '组件ID|组件ID',
  `ASSEMBLE_TYPE` varchar(10) NOT NULL COMMENT '组件类型|组件类型|EVENT-事件,PART-指标,ATTR-参数',
  `EXPRESSION` varchar(200) DEFAULT NULL COMMENT '表达式|表达式',
  `ORIG_EVENT_TYPE` varchar(10) DEFAULT NULL COMMENT '源事件类型|源事件类型',
  `STATUS` char(1) NOT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`TRAN_TYPE`,`EVENT_TYPE`,`PROD_TYPE`,`AMT_TYPE`,`SOURCE_TYPE`,`ASSEMBLE_ID`,`ASSEMBLE_TYPE`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='指标控制表|指标控制表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_part_ctl`
--

LOCK TABLES `mb_part_ctl` WRITE;
/*!40000 ALTER TABLE `mb_part_ctl` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_part_ctl` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_part_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_part_type` (
  `PART_TYPE` varchar(20) NOT NULL COMMENT '指标类型|指标类型',
  `PART_DESC` varchar(50) NOT NULL COMMENT '指标类型描述|指标类型描述',
  `DEFAULT_PART` varchar(20) DEFAULT NULL COMMENT '基础指标类型|基础指标类型',
  `PART_CLASS` varchar(20) NOT NULL COMMENT '指标分类|指标分类',
  `BUSI_CATEGORY` varchar(20) DEFAULT NULL COMMENT '业务分类|业务分类|RB-存款,CL-贷款,MM-货币市场,GL-总账',
  `STANDARD_FLAG` char(1) DEFAULT NULL COMMENT '是否标准模板|是否标准模板|Y-是,N-否',
  `PROCESS_METHOD` char(1) DEFAULT NULL COMMENT '指标处理方式|指标处理方式|A-检查类,C-提交类',
  `STATUS` char(1) NOT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PART_TYPE`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='指标类型定义表|指标类型定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_part_type`
--

LOCK TABLES `mb_part_type` WRITE;
/*!40000 ALTER TABLE `mb_part_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_part_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_prod_amend_mapping`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_prod_amend_mapping` (
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `AMEND_PROD_TYPE` varchar(500) DEFAULT NULL COMMENT '可变更的产品类型|定义允许变更的其他产品类型，有多个可变更产品类型时，使用逗号(,)分隔',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PROD_TYPE`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品变更映射表|产品变更映射表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_prod_amend_mapping`
--

LOCK TABLES `mb_prod_amend_mapping` WRITE;
/*!40000 ALTER TABLE `mb_prod_amend_mapping` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_prod_amend_mapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_prod_bal_default`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_prod_bal_default` (
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `CCY` varchar(3) NOT NULL COMMENT '币种|币种',
  `INIT_AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '起存金额|起存金额，账户开户时的最低存入金额',
  `INCREASE_AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '递增金额|递增金额',
  `REMAIN_AMOUNT` decimal(17,2) DEFAULT NULL COMMENT '留底金额|账户支取后，账户最小保留余额，如果支取后剩余余额小于此值，则需要考虑账户销户处理',
  `SG_MIN_AMT` decimal(17,2) DEFAULT NULL COMMENT '单次最小支取金额|单次最小支取金额',
  `SG_MAX_AMT` decimal(17,2) DEFAULT NULL COMMENT '单笔认购最大金额|单笔认购最大金额',
  `MAX_BAL` decimal(17,2) DEFAULT NULL COMMENT '最大余额|最大余额',
  `MIN_BAL` decimal(17,2) DEFAULT NULL COMMENT '最小余额|最小余额',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `INIT_AMOUNT_EXP` varchar(50) DEFAULT NULL COMMENT '产品起存金额表达式|产品起存金额表达式',
  `SG_MIN_AMT_EXP` varchar(50) DEFAULT NULL COMMENT '单次最小支取金额表达式|单次最小支取金额表达式',
  `SG_MAX_AMT_EXP` varchar(50) DEFAULT NULL COMMENT '单次最大支取金额表达式|单次最大支取金额表达式',
  `REMAIN_AMOUNT_EXP` varchar(50) DEFAULT NULL COMMENT '产品留存金额表达式|产品留存金额表达式',
  `CCY_EXP` varchar(200) DEFAULT NULL COMMENT '币种表达式 |币种表达式 ',
  `MIN_AVG_BAL_MTD` decimal(17,2) DEFAULT NULL COMMENT '最低月平均收费余额|最低月平均收费余额',
  `MIN_AVG_BAL_YTD` decimal(17,2) DEFAULT NULL COMMENT '最低年平均收费余额|最低年平均收费余额',
  `MIN_BAL_EARN_INT` decimal(17,2) DEFAULT NULL COMMENT '可获利息余额|可获利息余额',
  `MIN_BAL_FOR_WDRAW` decimal(17,2) DEFAULT NULL COMMENT '取最低余额|取最低余额',
  PRIMARY KEY (`PROD_TYPE`,`CCY`,`COMPANY`),
  KEY `IDX_MB_PROD_BAL_DEFAULT_1` (`PROD_TYPE`,`CCY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品起存金额定义表|产品起存金额定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_prod_bal_default`
--

LOCK TABLES `mb_prod_bal_default` WRITE;
/*!40000 ALTER TABLE `mb_prod_bal_default` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_prod_bal_default` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_prod_catalog`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_prod_catalog` (
  `CATALOG_NO` varchar(50) NOT NULL COMMENT '目录序号|目录序号',
  `PROD_CLASS` varchar(20) NOT NULL COMMENT '产品分类|目录第一级编号，用1位数字表示|1-存款,2-贷款,3-同业投资,4-同业往来,6-表外,7-卡产品,8-组合产品,9-其他资产负债类',
  `PROD_CLASS_NAME` varchar(200) NOT NULL COMMENT '产品分类名称|产品分类名称',
  `PROD_SUB_CLASS` varchar(20) DEFAULT NULL COMMENT '产品细类|目录第二级编号，用2位数字表示',
  `PROD_SUB_CLASS_NAME` varchar(200) DEFAULT NULL COMMENT '产品细类名称|产品细类名称',
  `PROD_GROUP` varchar(20) DEFAULT NULL COMMENT '产品分组|目录第三级编号，用2位数字表示',
  `PROD_GROUP_NAME` varchar(200) DEFAULT NULL COMMENT '产品分组名称|产品分组名称',
  `BASE_PROD` varchar(20) DEFAULT NULL COMMENT '基础产品，带目录结构|四级目录，用7位数字表示',
  `BASE_PROD_NAME` varchar(200) DEFAULT NULL COMMENT '基础产品名称|基础产品名称',
  `PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '产品类型|五级目录，用12位数字表示',
  `PROD_NAME` varchar(200) DEFAULT NULL COMMENT '产品名称 |产品名称 ',
  `PROD_DESC` varchar(200) DEFAULT NULL COMMENT '产品描述|解释产品具体特性',
  `PROD_STATUS` char(1) DEFAULT NULL COMMENT '产品状态|产品状态|0-待生效,1-生效,2-停办,3-失效',
  `MANAGE_DEPT` varchar(200) DEFAULT NULL COMMENT '管理部门|管理部门',
  `SYSTEM_ID` varchar(20) DEFAULT NULL COMMENT '系统ID|系统ID',
  `EFFECT_DATE` datetime DEFAULT NULL COMMENT '生效日期|生效日期',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '失效日期|失效日期',
  `CTLG_LEVEL` char(1) DEFAULT NULL COMMENT '产品层级|产品层级',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`CATALOG_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品目录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_prod_catalog`
--

LOCK TABLES `mb_prod_catalog` WRITE;
/*!40000 ALTER TABLE `mb_prod_catalog` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_prod_catalog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_prod_class`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_prod_class` (
  `PROD_CLASS` varchar(20) NOT NULL COMMENT '产品分类|产品分类',
  `PROD_CLASS_DESC` varchar(50) NOT NULL COMMENT '产品分类描述|产品分类描述',
  `PROD_CLASS_LEVEL` char(1) DEFAULT NULL COMMENT '产品分类层级|产品分类层级|1-1级,2-2级',
  `PARENT_PROD_CLASS` varchar(5) DEFAULT NULL COMMENT '上级产品分类|上级产品分类',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PROD_CLASS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品分类定义表|产品分类定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_prod_class`
--

LOCK TABLES `mb_prod_class` WRITE;
/*!40000 ALTER TABLE `mb_prod_class` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_prod_class` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_prod_define`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_prod_define` (
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `ASSEMBLE_TYPE` varchar(10) NOT NULL COMMENT '组件类型|组件类型|EVENT-事件,PART-指标,ATTR-参数',
  `ASSEMBLE_ID` varchar(50) NOT NULL COMMENT '组件ID|组件ID',
  `ATTR_KEY` varchar(30) DEFAULT NULL COMMENT '参数KEY值|参数KEY值',
  `ATTR_VALUE` varchar(500) DEFAULT NULL COMMENT '属性值|属性值',
  `EVENT_DEFAULT` varchar(10) DEFAULT NULL COMMENT '产品基础事件|产品基础事件',
  `STATUS` char(1) NOT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PROD_TYPE`,`SEQ_NO`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品定义表|产品定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_prod_define`
--

LOCK TABLES `mb_prod_define` WRITE;
/*!40000 ALTER TABLE `mb_prod_define` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_prod_define` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_prod_group`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_prod_group` (
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `PROD_SUB_TYPE` varchar(20) NOT NULL COMMENT '产品子类型|产品子类型',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `DEFAULT_PROD_FLAG` char(1) DEFAULT NULL COMMENT '是否默认产品|是否默认产品|Y-是,N-否 ',
  `ACCT_CLASS` char(1) DEFAULT NULL COMMENT '账户类别|账户类别，用于区分账户类别（一二三类户），满足人行对于电子账户的管理办法|1-一类账户,2-二类账户,3-三类账户',
  `RATIO` decimal(17,2) DEFAULT NULL COMMENT '存益贷产品分层金额|存益贷产品分层金额',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PROD_TYPE`,`PROD_SUB_TYPE`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品组定义表|产品组定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_prod_group`
--

LOCK TABLES `mb_prod_group` WRITE;
/*!40000 ALTER TABLE `mb_prod_group` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_prod_group` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_prod_int`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_prod_int` (
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `EVENT_TYPE` varchar(20) NOT NULL COMMENT '事件类型|事件类型',
  `INT_CLASS` varchar(5) NOT NULL COMMENT '利息分类|利息分类|INT-正常利息,ODI-复利,PDUE-超期利息',
  `INT_TYPE` varchar(5) NOT NULL COMMENT '利率类型|利率类型',
  `TAX_TYPE` varchar(5) DEFAULT NULL COMMENT '税种|税种|VAT-增值税,ADT-附加税',
  `ACCT_RATE_FLAG` char(1) DEFAULT NULL COMMENT '是否使用分户利率标志|是否使用分户利率标志|Y-使用,N-不使用',
  `INT_CALC_METHOD` varchar(2) DEFAULT NULL COMMENT '利息计算方法|利息计算方法|AB-积数计息,EB-分段计息',
  `RATE_LAYER_RULE` char(1) DEFAULT NULL COMMENT '利率分层规则|利率分层规则|A-按金额分层,P-按周期分层,N-不分层',
  `RATE_GEAR_AMT_TYPE` varchar(10) DEFAULT NULL COMMENT '利率靠档金额类型|利率靠档金额类型|BAL-余额,DDA-发放金额,INTP-逾期利息,LIM-额度金额,OD-透支金额,ODIP-逾期复利,ODPP-逾期罚息,OSL-未到期本金,PRD-逾期本金,PRI-本金',
  `GEAR_AMT_IND` char(1) DEFAULT NULL COMMENT '金额靠档方向|金额靠档方向|F-靠下档,C-靠上档',
  `GEAR_AMT_METHOD` char(1) DEFAULT NULL COMMENT '金额靠档方式|金额靠档方式|C-差额累进,Q-全额累进',
  `GEAR_DAYS_IND` char(1) DEFAULT NULL COMMENT '天数靠档方向|天数靠档方向|F-靠下档,C-靠上档',
  `GEAR_DAYS_METHOD` char(1) DEFAULT NULL COMMENT '天数靠档方式|天数靠档方式|C-差额累进,Q-全额累进',
  `INT_CALC_AMT_TYPE` varchar(10) DEFAULT NULL COMMENT '利息计算金额类型|利息计算金额类型|BAL-余额,DDA-发放金额,INTP-逾期利息,LIM-额度金额,OD-透支金额,ODIP-逾期复利,ODPP-逾期罚息,OSL-未到期本金,PRD-逾期本金,PRI-本金',
  `EFFECT_DATE_CALC_METHOD` char(1) DEFAULT NULL COMMENT '计息起始日期取值方法|计息起始日期取值方法|O-开户日期,M-到期日,I-计提日期,C-表示按上一结息日',
  `DAYS_GEAR_TYPE` char(1) DEFAULT NULL COMMENT '靠档天数计算方式|靠档天数计算方式|A-按存期(起息日加存期) ,B-实际天数(起息日开始) ,C-计提天数(上一结息日开始)',
  `INT_APPL_TYPE` char(1) DEFAULT NULL COMMENT '利率启用方式|利率启用方式|A-随基准利率变更,N-不变更,R-按周期变更,S-按计息变更,F-浮动不随基准利率变更',
  `MONTH_BASIS` varchar(3) DEFAULT NULL COMMENT '月基准|月基准|ACT-按实际天数 ,D30-按30天',
  `GROUP_RULE_TYPE` varchar(2) DEFAULT NULL COMMENT '分组规则|分组规则关系|02-取最大,03-取最小,04-取平均值,05-取叠加,06-取权重',
  `INT_MATCH_RULE` varchar(10) DEFAULT NULL COMMENT '利息明细生效规则|利息明细生效规则|DEFAULT-直接生效(e.g. 活期) ,PERIOD-唯一生效(e.g. 整整),ACCROD-协定 ,DEPEND-同时生效(e.g. 幸福存)',
  `INT_RECALC_METHOD` char(1) NOT NULL COMMENT '利息重算方法|利息重算方法|N-重新按模型计算,H-取历史利率计算,I-取历史计提后再按模型计算',
  `MIN_RATE` decimal(15,8) DEFAULT NULL COMMENT '最小利率|最小利率',
  `MAX_RATE` decimal(15,8) DEFAULT NULL COMMENT '最大利率|最大利率',
  `ROLL_DAY` varchar(2) DEFAULT NULL COMMENT '利率变更日|定义利率变动日期，如果利率启用方式为R时，需要指定利率变更的日。系统根据上一个利率变更日，利率变更周期，和这个‘日’计算下一个利率变更日',
  `ROLL_FREQ` varchar(5) DEFAULT NULL COMMENT '利率变更周期|利率变更周期',
  `ROUND_DOWN_FLAG` char(1) DEFAULT NULL COMMENT '是否截位标志|是否截位标志|Y-截位,N-不截位',
  `INT_TYPE_EXP` varchar(50) DEFAULT NULL COMMENT '利率类型表达式|利率类型表达式',
  `MONTH_BASIS_EXP` varchar(50) DEFAULT NULL COMMENT '月基准表达式|利率类型表达式',
  `GROUP_RULE_TYPE_EXP` varchar(50) DEFAULT NULL COMMENT '分组规则表达式|分组规则关系表达式',
  `MIN_RATE_EXP` varchar(50) DEFAULT NULL COMMENT '最小利率表达式|最小利率表达式',
  `MAX_RATE_EXP` varchar(50) DEFAULT NULL COMMENT '最大利率表达式|最大利率表达式',
  `INT_CALC_METHOD_EXP` varchar(50) DEFAULT NULL COMMENT '利息计算方法表达式|利息计算方法表达式',
  `INT_CALC_AMT_TYPE_EXP` varchar(50) DEFAULT NULL COMMENT '利息计算金额类型表达式|利息计算金额类型表达式',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `SPREAD_PERCENT` decimal(15,8) DEFAULT NULL COMMENT '浮动百分比|浮动百分比',
  `SPREAD_RATE` decimal(15,8) DEFAULT NULL COMMENT '浮动点数|浮动点数',
  `CALC_BY_INT` char(1) DEFAULT NULL COMMENT '是否按正常利率浮动|是否按正常利率浮动|Y-按正常利率浮动,N-不按正常利率浮动',
  PRIMARY KEY (`PROD_TYPE`,`EVENT_TYPE`,`INT_CLASS`,`INT_TYPE`,`COMPANY`),
  KEY `IDX_MB_PROD_INT_1` (`PROD_TYPE`,`EVENT_TYPE`,`INT_CLASS`,`INT_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品利率信息表|产品利率信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_prod_int`
--

LOCK TABLES `mb_prod_int` WRITE;
/*!40000 ALTER TABLE `mb_prod_int` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_prod_int` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_prod_preference`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_prod_preference` (
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `PREFERENCE_TYPE` varchar(10) DEFAULT NULL COMMENT '优惠方式|优惠方式',
  `PREFERENCE_VALUE` varchar(50) DEFAULT NULL COMMENT '优惠值定义|优惠值定义',
  `PREFERENCE_FIXED_RATE` decimal(15,8) DEFAULT NULL COMMENT '利率优惠固定利率|利率优惠固定利率',
  `PREFERENCE_PERCENT_RATE` decimal(5,2) DEFAULT NULL COMMENT '利率优惠浮动百分比|利率优惠浮动百分比',
  `PREFERENCE_SPREAD_RATE` decimal(15,8) DEFAULT NULL COMMENT '利率优惠浮动百分点|利率优惠浮动百分点',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PROD_TYPE`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品利率优惠表|产品利率优惠表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_prod_preference`
--

LOCK TABLES `mb_prod_preference` WRITE;
/*!40000 ALTER TABLE `mb_prod_preference` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_prod_preference` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_prod_quota`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_prod_quota` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `YEAR` varchar(5) DEFAULT NULL COMMENT '年度 |年度 ',
  `AREA_CODE` varchar(5) DEFAULT NULL COMMENT '地区码|地区码',
  `CCY` varchar(3) DEFAULT NULL COMMENT '币种|币种',
  `QUOTA_AMT` decimal(17,2) DEFAULT NULL COMMENT '优惠额度|优惠额度',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品销售额度表|产品销售额度表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_prod_quota`
--

LOCK TABLES `mb_prod_quota` WRITE;
/*!40000 ALTER TABLE `mb_prod_quota` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_prod_quota` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_prod_subject_mapping`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_prod_subject_mapping` (
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `PROD_DESC` varchar(200) DEFAULT NULL COMMENT '产品描述|解释产品具体特性',
  `BASE_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '基础产品|基础产品',
  `SUBJECT_CODE` varchar(20) NOT NULL COMMENT '科目代码 |科目代码 ',
  `SUBJECT_DESC` varchar(200) DEFAULT NULL COMMENT '科目描述|科目描述',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`PROD_TYPE`,`SUBJECT_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品科目映射表|产品科目映射表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_prod_subject_mapping`
--

LOCK TABLES `mb_prod_subject_mapping` WRITE;
/*!40000 ALTER TABLE `mb_prod_subject_mapping` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_prod_subject_mapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_prod_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_prod_type` (
  `PROD_TYPE` varchar(20) NOT NULL COMMENT '产品类型|产品类型',
  `PROD_DESC` varchar(200) NOT NULL COMMENT '产品描述|解释产品具体特性',
  `PROD_CLASS` varchar(20) NOT NULL COMMENT '产品分类|产品分类',
  `BASE_PROD_TYPE` varchar(20) DEFAULT NULL COMMENT '基础产品|基础产品',
  `PROD_GROUP_FLAG` char(1) DEFAULT NULL COMMENT '是否产品组|是否产品组|Y-组合产品,N-单一产品',
  `PROD_RANGE` char(1) DEFAULT NULL COMMENT '产品作用范围|产品作用范围|B-基础产品,S-可售产品',
  `GL_MERGE_TYPE_FLAG` char(1) DEFAULT NULL COMMENT '计提是否合并标志|计提是否合并标志|Y-是,N-否',
  `STATUS` char(1) NOT NULL COMMENT '状态|状态|A-有效,F-无效,O-未过账,P-已过账',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `RULE_EXPRESS` varchar(200) DEFAULT NULL COMMENT '规则表达式|规则表达式',
  `RULE_DESC` varchar(200) DEFAULT NULL COMMENT '规则描述|规则描述',
  PRIMARY KEY (`PROD_TYPE`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='产品类型定义表|产品类型定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_prod_type`
--

LOCK TABLES `mb_prod_type` WRITE;
/*!40000 ALTER TABLE `mb_prod_type` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_prod_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_purpose_def`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_purpose_def` (
  `PURPOSE_ID` varchar(50) NOT NULL COMMENT '用途编号|用途编号',
  `DAY_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '单日发放额度|单日发放额度',
  `ONCE_LIMIT` decimal(17,2) DEFAULT NULL COMMENT '单次发放额度|单次发放额度',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`PURPOSE_ID`,`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='卡易贷贷款用途定义表|卡易贷贷款用途定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_purpose_def`
--

LOCK TABLES `mb_purpose_def` WRITE;
/*!40000 ALTER TABLE `mb_purpose_def` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_purpose_def` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_rule_group`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_rule_group` (
  `GROUP_TYPE` varchar(10) NOT NULL COMMENT '规则分组类型|规则分组类型',
  `GROUP_TYPE_DESC` varchar(50) NOT NULL COMMENT '规则分组描述|规则分组描述',
  `GROUP_CLASS` char(1) NOT NULL COMMENT '分组归属分类|分组归属分类|D-折扣,L-利率,E-汇率',
  `GROUP_MATCH_TYPE` varchar(2) NOT NULL COMMENT '组内规则关系|组内规则关系|当GROUP_CLASS=D时-,01-折上折,02-取最大,03-取最小,04-取平均值,05-取叠加,06-取权重,07-取固定,当GROUP_CLASS=L时-,02-取最大,03-取最小,04-取平均值,05-取叠加,06-取权重,当GROUP_CLASS=E时-,02-取最大,03-取最小,04-取平均值,05-取叠加,06-取权重',
  `WEIGHT` varchar(20) DEFAULT NULL COMMENT '权重|权重',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`GROUP_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='规则分组定义表|规则分组定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_rule_group`
--

LOCK TABLES `mb_rule_group` WRITE;
/*!40000 ALTER TABLE `mb_rule_group` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_rule_group` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_rule_message`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_rule_message` (
  `IRL_SEQ_NO` varchar(50) NOT NULL COMMENT '费率编号|费率编号',
  `GROUP_TYPE` varchar(10) DEFAULT NULL COMMENT '规则分组类型|规则分组类型',
  `RULE_DESC` varchar(200) NOT NULL COMMENT '规则描述|规则描述',
  `GROUP_CCY` varchar(10) DEFAULT NULL COMMENT '币种组|币种组目标币种+源币种',
  `RULE_FLAG` char(1) NOT NULL COMMENT '是否使用规则|是否使用规则|Y-是,N-否',
  `CHECK_TYPE` char(1) DEFAULT NULL COMMENT '约束类型|约束类型|1-拒绝,2-授权,3-提示',
  `CREATE_DATE` datetime NOT NULL COMMENT '创建日期|创建日期',
  `START_DATE` datetime NOT NULL COMMENT '开始日期|开始日期',
  `END_DATE` datetime NOT NULL COMMENT '结束日期|结束时间',
  `RULE_CLASS1` varchar(20) NOT NULL COMMENT '规则分类1|规则分类1|INRATE-利率,FEE-费用,EXRATE-汇率,DISC-折扣',
  `RULE_CLASS2` varchar(20) DEFAULT NULL COMMENT '规则分类2|规则分类2',
  `RULE_CLASS3` varchar(20) DEFAULT NULL COMMENT '规则分类3|规则分类3',
  `RULE_EXPRESS` varchar(200) NOT NULL COMMENT '规则表达式|规则表达式',
  `RULE_WEIGHT` decimal(5,2) DEFAULT NULL COMMENT '规则权重|规则权重',
  `RULE_STATUS` char(1) NOT NULL COMMENT '规则状态|规则状态|0-不启用,1-启用',
  `IMPORT_MESSAGE` varchar(10) DEFAULT NULL COMMENT '规则引用信息|规则引用信息',
  `SPECIAL_RULE_PROCESS` varchar(2000) DEFAULT NULL COMMENT '特殊规则浮动信息|特殊规则浮动信息',
  `INT_FLOAT_TYPE` char(1) DEFAULT NULL COMMENT '浮动类型|浮动类型|1-按行内利率,2-按固定值',
  `FLOAT_VALUE` varchar(10) DEFAULT NULL COMMENT '浮动值|浮动值',
  `USER_ID` varchar(30) NOT NULL COMMENT '交易柜员|交易柜员',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`IRL_SEQ_NO`),
  KEY `IDX_MB_RULE_MESSAGE_1` (`RULE_CLASS1`,`RULE_CLASS3`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='组内规则表|组内规则表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_rule_message`
--

LOCK TABLES `mb_rule_message` WRITE;
/*!40000 ALTER TABLE `mb_rule_message` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_rule_message` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_taxrate_define`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_taxrate_define` (
  `TAX_TYPE` varchar(5) NOT NULL COMMENT '税种|税种|VAT-增值税,ADT-附加税',
  `TAX_TYPE_DESC` varchar(50) NOT NULL COMMENT '税率类型描述|税率类型描述',
  `START_METHOD` char(1) NOT NULL COMMENT '启动方式|启动方式|A-实时生效,S-按周期生效,F-固定生效',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`TAX_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='税率类型定义表|税率类型定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_taxrate_define`
--

LOCK TABLES `mb_taxrate_define` WRITE;
/*!40000 ALTER TABLE `mb_taxrate_define` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_taxrate_define` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mb_taxrate_info`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mb_taxrate_info` (
  `TAX_RATE_CODE` varchar(3) NOT NULL COMMENT '税率代码|税率代码',
  `EFFECT_DATE` datetime NOT NULL COMMENT '生效日期|生效日期',
  `COUNTRY` varchar(3) NOT NULL COMMENT '国家|国家',
  `PROVINCE` varchar(30) NOT NULL COMMENT '省|省',
  `TAX_RATE` decimal(15,8) NOT NULL COMMENT '税率|税率',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`TAX_RATE_CODE`,`EFFECT_DATE`,`COUNTRY`,`PROVINCE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='税率类型信息表|税率类型信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mb_taxrate_info`
--

LOCK TABLES `mb_taxrate_info` WRITE;
/*!40000 ALTER TABLE `mb_taxrate_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `mb_taxrate_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mq_consumer_msg`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mq_consumer_msg` (
  `MESSAGE_ID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息ID',
  `DESTINATION` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息目的地',
  `RECEIVE_TIME` datetime NOT NULL COMMENT '生产者接收时间',
  `STATUS` int DEFAULT NULL COMMENT '消息状态：1-接收成功，3:消费成功，4:消费失败',
  `UPDATE_TIME` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后一次更新状态时间',
  `REMARK` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`MESSAGE_ID`,`RECEIVE_TIME`) USING BTREE,
  KEY `MQ_CONSUMER_MSG_IDX1` (`UPDATE_TIME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ消费者信息消息表(UPRIGHT)'
/*!50100 PARTITION BY RANGE (to_days(`RECEIVE_TIME`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mq_consumer_msg`
--

LOCK TABLES `mq_consumer_msg` WRITE;
/*!40000 ALTER TABLE `mq_consumer_msg` DISABLE KEYS */;
/*!40000 ALTER TABLE `mq_consumer_msg` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mq_consumer_msg_hist`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mq_consumer_msg_hist` (
  `MESSAGE_ID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息ID',
  `DESTINATION` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息目的地',
  `RECEIVE_TIME` datetime NOT NULL COMMENT '生产者接收时间',
  `STATUS` int DEFAULT NULL COMMENT '消息状态：1-接收成功，3:消费成功，4:消费失败',
  `UPDATE_TIME` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后一次更新状态时间',
  `REMARK` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`MESSAGE_ID`,`RECEIVE_TIME`) USING BTREE,
  KEY `MQ_CONSUMER_MSG_HIST_IDX1` (`UPDATE_TIME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ消费者信息消息表(历史表)(UPRIGHT)'
/*!50100 PARTITION BY RANGE (to_days(`RECEIVE_TIME`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mq_consumer_msg_hist`
--

LOCK TABLES `mq_consumer_msg_hist` WRITE;
/*!40000 ALTER TABLE `mq_consumer_msg_hist` DISABLE KEYS */;
/*!40000 ALTER TABLE `mq_consumer_msg_hist` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mq_consumer_repeat`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mq_consumer_repeat` (
  `MESSAGE_ID` varchar(50) NOT NULL COMMENT '消息ID',
  `DESTINATION` varchar(128) DEFAULT NULL COMMENT '消息目的地',
  `RECEIVE_TIME` timestamp NULL DEFAULT NULL COMMENT '生产者接收时间',
  `REMARK` varchar(2000) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`MESSAGE_ID`) USING BTREE,
  KEY `MQ_CONSUMER_REPEAT_IDX1` (`MESSAGE_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ消费者信息消息表(UPRIGHT)';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mq_consumer_repeat`
--

LOCK TABLES `mq_consumer_repeat` WRITE;
/*!40000 ALTER TABLE `mq_consumer_repeat` DISABLE KEYS */;
/*!40000 ALTER TABLE `mq_consumer_repeat` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mq_producer_msg`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mq_producer_msg` (
  `MESSAGE_ID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息id',
  `SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务唯一识别号',
  `FLOW_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程id',
  `BROKER_NAME` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'broker名称',
  `OFFSET_MSG_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息发送成功，broker生成 id',
  `MESSAGE` longblob COMMENT '消息内容',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后一次更新时间',
  `STATUS` int DEFAULT NULL COMMENT '状态:1-消息建立；2-待发送；3-发送成 功；4-异常',
  `MESSAGE_TYPE` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息类型',
  `SEQ_NO` int DEFAULT NULL COMMENT '消息序列号',
  `QUEUE_ID` int DEFAULT NULL COMMENT '消息接受队列id',
  PRIMARY KEY (`MESSAGE_ID`,`CREATE_TIME`) USING BTREE,
  KEY `MQ_PRODUCER_MSG_IDX` (`FLOW_ID`,`STATUS`) USING BTREE,
  KEY `MQ_PRODUCER_MSG_IDX1` (`LAST_UPDATE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ生产者信息消息表(UPRIGHT)'
/*!50100 PARTITION BY RANGE (to_days(`CREATE_TIME`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mq_producer_msg`
--

LOCK TABLES `mq_producer_msg` WRITE;
/*!40000 ALTER TABLE `mq_producer_msg` DISABLE KEYS */;
/*!40000 ALTER TABLE `mq_producer_msg` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mq_producer_msg_hist`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mq_producer_msg_hist` (
  `MESSAGE_ID` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息id',
  `SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务唯一识别号',
  `FLOW_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程id',
  `BROKER_NAME` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'broker名称',
  `OFFSET_MSG_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息发送成功，broker生成 id',
  `MESSAGE` longblob COMMENT '消息内容',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后一次更新时间',
  `STATUS` int DEFAULT NULL COMMENT '状态:1-消息建立；2-待发送；3-发送成 功；4-异常',
  `MESSAGE_TYPE` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息类型',
  `SEQ_NO` int DEFAULT NULL COMMENT '消息序列号',
  `QUEUE_ID` int DEFAULT NULL COMMENT '消息接受队列id',
  PRIMARY KEY (`MESSAGE_ID`,`CREATE_TIME`) USING BTREE,
  KEY `MQ_PRODUCER_MSG_HIST_IDX` (`FLOW_ID`,`STATUS`) USING BTREE,
  KEY `MQ_PRODUCER_MSG_HIST_IDX1` (`LAST_UPDATE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ生产者信息消息表(历史表)(UPRIGHT)'
/*!50100 PARTITION BY RANGE (to_days(`CREATE_TIME`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mq_producer_msg_hist`
--

LOCK TABLES `mq_producer_msg_hist` WRITE;
/*!40000 ALTER TABLE `mq_producer_msg_hist` DISABLE KEYS */;
/*!40000 ALTER TABLE `mq_producer_msg_hist` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mq_producer_send_msg`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mq_producer_send_msg` (
  `MQ_MSG_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息id',
  `FLOW_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程id',
  `STATUS` decimal(11,0) DEFAULT NULL COMMENT '状态:1-消息建立；2-待发送；3-发送成功；4-异常',
  PRIMARY KEY (`MQ_MSG_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ生产者待发送消息表(UPRIGHT)';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mq_producer_send_msg`
--

LOCK TABLES `mq_producer_send_msg` WRITE;
/*!40000 ALTER TABLE `mq_producer_send_msg` DISABLE KEYS */;
/*!40000 ALTER TABLE `mq_producer_send_msg` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `orbit_records`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `orbit_records` (
  `CENTER_IND` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '中心标识',
  `IS_LOCAL` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '本地标识',
  `CACHE_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '缓存名称',
  `CACHE_KEY` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '缓存KEY',
  PRIMARY KEY (`CENTER_IND`,`IS_LOCAL`,`CACHE_NAME`,`CACHE_KEY`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='分布式缓存组件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orbit_records`
--

LOCK TABLES `orbit_records` WRITE;
/*!40000 ALTER TABLE `orbit_records` DISABLE KEYS */;
/*!40000 ALTER TABLE `orbit_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `prod_dropdown_parameter`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `prod_dropdown_parameter` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `SQL_ID` varchar(50) NOT NULL COMMENT 'sql编号|sql编号',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`TABLE_NAME`,`SQL_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='参数配置表|prod模块下拉列表参数表，用于前端下拉列表查询配置参数';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `prod_dropdown_parameter`
--

LOCK TABLES `prod_dropdown_parameter` WRITE;
/*!40000 ALTER TABLE `prod_dropdown_parameter` DISABLE KEYS */;
/*!40000 ALTER TABLE `prod_dropdown_parameter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sonic_running_step_lock`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sonic_running_step_lock` (
  `step_run_id` varchar(32) NOT NULL COMMENT 'Step运行时唯一ID',
  `executor_id` varchar(32) DEFAULT NULL COMMENT '执行者ID',
  `lock_id` varchar(32) DEFAULT NULL COMMENT '锁ID',
  `lock_time` timestamp NULL DEFAULT NULL COMMENT '锁时间戳',
  PRIMARY KEY (`step_run_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='Step执行锁';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sonic_running_step_lock`
--

LOCK TABLES `sonic_running_step_lock` WRITE;
/*!40000 ALTER TABLE `sonic_running_step_lock` DISABLE KEYS */;
/*!40000 ALTER TABLE `sonic_running_step_lock` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sonic_step_run_result`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sonic_step_run_result` (
  `step_run_id` varchar(32) NOT NULL COMMENT 'Step运行时唯一ID',
  `complete_status` varchar(10) DEFAULT NULL COMMENT 'Step完成状态',
  `complete_time` timestamp NULL DEFAULT NULL COMMENT 'Step完成时间',
  `executor_id` varchar(32) DEFAULT NULL COMMENT '执行者ID',
  PRIMARY KEY (`step_run_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='Step执行结果';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sonic_step_run_result`
--

LOCK TABLES `sonic_step_run_result` WRITE;
/*!40000 ALTER TABLE `sonic_step_run_result` DISABLE KEYS */;
/*!40000 ALTER TABLE `sonic_step_run_result` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tran_sql_log`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tran_sql_log` (
  `SERVICE_ID` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务ID',
  `SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务唯一识别号',
  `TRAN_DATE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易日期',
  `TRAN_TIME` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易时间',
  `SOURCE_TYPE` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '渠道类型',
  `SEQ_NO` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '渠道流水号',
  `PROGRAM_ID` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易屏幕标识',
  `REFERENCE` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '业务参考号',
  `USER_ID` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作柜员',
  `BRANCH_ID` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '网点',
  `TABLE_NAME` varchar(50) DEFAULT NULL COMMENT '表名',
  `SQL_TYPE` varchar(10) DEFAULT NULL COMMENT '操作类型（UPDATE/DELETE/INSERT)',
  `BEFORE_EXE_DATA` varchar(4000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '执行前数据',
  `AFTER_EXE_DATA` varchar(4000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '执行后数据',
  `CREATE_DATE` varchar(26) DEFAULT NULL COMMENT '创建时间',
  KEY `TRAN_SQL_LOG_IDX1` (`TRAN_DATE`) USING BTREE,
  KEY `TRAN_SQL_LOG_IDX2` (`REFERENCE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='sql数据登记表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tran_sql_log`
--

LOCK TABLES `tran_sql_log` WRITE;
/*!40000 ALTER TABLE `tran_sql_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `tran_sql_log` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-06 15:41:46
