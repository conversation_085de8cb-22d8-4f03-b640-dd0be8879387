package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"sort"
	"strings"

	"github.com/xuri/excelize/v2"
)

type APIInfo struct {
	序号   string `json:"序号"`
	接口名称 string `json:"接口名称"`
	接口定义 string `json:"接口定义"`
	URL  string `json:"URL"`
	功能描述 string `json:"功能描述"`
	业务分类 string `json:"业务分类"`
	功能分类 string `json:"功能分类"`
	消费系统 string `json:"消费系统"`
	备注   string `json:"备注"`
	使用状态 string `json:"使用状态"`
	接口类名 string `json:"接口类名"`
	超时时间 string `json:"超时时间"`
}

type CategorySummary struct {
	分类名称 string    `json:"分类名称"`
	接口数量 int       `json:"接口数量"`
	接口列表 []APIInfo `json:"接口列表"`
}

func main() {
	// 打开 Excel 文件
	f, err := excelize.OpenFile("cif_api.xlsx")
	if err != nil {
		log.Fatal(err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// 读取服务接口清单
	fmt.Println("=== CIF模块功能整理 ===\n")

	rows, err := f.GetRows("服务接口清单")
	if err != nil {
		log.Fatal("读取服务接口清单失败:", err)
	}

	if len(rows) < 2 {
		log.Fatal("服务接口清单数据不足")
	}

	// 先打印前几行来调试数据结构
	fmt.Println("=== 调试信息 ===")
	for i := 0; i < min(5, len(rows)); i++ {
		fmt.Printf("行%d: %v\n", i, rows[i])
	}
	fmt.Println()

	// 解析API信息
	var apis []APIInfo
	for i := 1; i < len(rows); i++ { // 从第二行开始（跳过表头）
		row := rows[i]
		if len(row) < 5 {
			continue
		}

		// 检查是否是有效的API行（序号不为空且为数字）
		序号 := getCell(row, 0)
		接口名称 := getCell(row, 1)

		if 序号 == "" || 接口名称 == "" || 序号 == "序号" {
			continue
		}

		api := APIInfo{
			序号:   序号,
			接口名称: 接口名称,
			接口定义: getCell(row, 2),
			URL:  getCell(row, 3),
			功能描述: getCell(row, 4),
			业务分类: getCell(row, 5),
			功能分类: getCell(row, 6),
			消费系统: getCell(row, 7),
			备注:   getCell(row, 8),
			使用状态: getCell(row, 9),
			接口类名: getCell(row, 10),
			超时时间: getCell(row, 11),
		}

		apis = append(apis, api)
	}

	fmt.Printf("总共发现 %d 个API接口\n\n", len(apis))

	// 按功能分类整理
	categoryMap := make(map[string][]APIInfo)
	for _, api := range apis {
		category := api.功能分类
		if category == "" {
			category = "未分类"
		}
		categoryMap[category] = append(categoryMap[category], api)
	}

	// 生成分类汇总
	var categories []CategorySummary
	for categoryName, apiList := range categoryMap {
		categories = append(categories, CategorySummary{
			分类名称: categoryName,
			接口数量: len(apiList),
			接口列表: apiList,
		})
	}

	// 按接口数量排序
	sort.Slice(categories, func(i, j int) bool {
		return categories[i].接口数量 > categories[j].接口数量
	})

	// 输出功能分类汇总
	fmt.Println("=== 按功能分类汇总 ===")
	for _, category := range categories {
		fmt.Printf("\n【%s】- %d个接口\n", category.分类名称, category.接口数量)
		for _, api := range category.接口列表 {
			fmt.Printf("  • %s (%s)\n", api.接口名称, api.接口定义)
			if api.功能描述 != "" {
				fmt.Printf("    描述: %s\n", api.功能描述)
			}
		}
	}

	// 按业务分类整理
	businessMap := make(map[string][]APIInfo)
	for _, api := range apis {
		business := api.业务分类
		if business == "" {
			business = "未分类"
		}
		businessMap[business] = append(businessMap[business], api)
	}

	fmt.Println("\n\n=== 按业务分类汇总 ===")
	for businessName, apiList := range businessMap {
		if businessName != "" && businessName != "未分类" {
			fmt.Printf("\n【%s】- %d个接口\n", businessName, len(apiList))
			for _, api := range apiList {
				fmt.Printf("  • %s\n", api.接口名称)
			}
		}
	}

	// 生成JSON文件
	jsonData, err := json.MarshalIndent(map[string]interface{}{
		"总接口数量":  len(apis),
		"功能分类汇总": categories,
		"详细接口列表": apis,
	}, "", "  ")
	if err != nil {
		log.Printf("生成JSON失败: %v", err)
	} else {
		err = os.WriteFile("cif_api_summary.json", jsonData, 0644)
		if err != nil {
			log.Printf("写入JSON文件失败: %v", err)
		} else {
			fmt.Println("\n详细信息已保存到 cif_api_summary.json 文件")
		}
	}

	// 分析URL模式
	fmt.Println("\n=== URL路径分析 ===")
	urlPatterns := make(map[string]int)
	for _, api := range apis {
		if api.URL != "" {
			parts := strings.Split(api.URL, "/")
			if len(parts) >= 3 {
				pattern := "/" + parts[1] + "/" + parts[2]
				urlPatterns[pattern]++
			}
		}
	}

	fmt.Println("主要URL模式:")
	for pattern, count := range urlPatterns {
		fmt.Printf("  %s: %d个接口\n", pattern, count)
	}
}

func getCell(row []string, index int) string {
	if index < len(row) {
		return strings.TrimSpace(row[index])
	}
	return ""
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
