# 客户信息查询接口表关系分析

## 接口逻辑流程

### 1. 客户基本信息查询
**查询优先级**：
1. 优先使用 `CLIENT_NO`（客户号）
2. 其次使用三要素：证件号 + 证件类型 + 发证机关

**涉及表**：
- `cif_client` - 客户基本信息主表
- `cif_client_document` - 客户证件信息表

### 2. 业务开关检查
**检查客户注销状态**：
- 查询 `cif_business_parameter` 表中的 `CLIENT_TRAN_STATUS_C_FLAG` 开关
- 判断是否需要检查客户已注销状态

### 3. 个人客户信息查询
**条件**：如果是个人客户
**涉及表**：
- `cif_client_indvl` - 个人客户扩展信息
- `cif_client_verification` - 个人客户身份核实情况表

### 4. NRA信息查询
**涉及表**：
- `cif_client_nra` - 客户NRA主表
- `cif_client_nra_detail` - 客户NRA详细信息表

### 5. 证件信息查询
**涉及表**：
- `cif_client_document` - 客户证件信息
- 判断首选证件到期提醒方式

### 6. 联系信息查询
**涉及表**：
- `cif_client_contact_tbl` - 客户联系信息
- `cif_contact_list` - 客户联系人信息

### 7. 对公客户信息查询
**条件**：如果是对公客户
**涉及表**：
- `cif_client_corp` - 法人客户附加信息表
- `cif_control_contact` - 控制人税收居民联系表
- `cif_crop_compnay_info` - 法人/财务主管详细信息表

### 8. 客户关联信息查询
**涉及表**：
- `cif_client_attach` - 客户信息关联表

### 9. 柜员权限检查
**检查逻辑**：
- RPC调用OB查询柜员身份信息
- 查询 `cif_cross_relations` - 客户关系表
- 判断当前柜员和被查询客户是否存在客户关系

### 10. 信息掩码处理
**检查逻辑**：
- 查询 `cif_business_parameter` 表中的 `CLIENT_CLIENT_USER_LEVEL` 开关
- 对低级别柜员（level=3）的手机和证件号码进行打码处理

### 11. 自定义信息查询
**涉及表**：
- `cif_define_column_information` - 用户自定义信息
- `cif_client_information` - 对公客户附属信息表

## 表关系汇总

### 核心关系链
1. **客户主体** → `cif_client` (主表)
2. **个人扩展** → `cif_client_indvl` (1:1)
3. **企业扩展** → `cif_client_corp` (1:1)
4. **证件信息** → `cif_client_document` (1:N)
5. **联系信息** → `cif_client_contact_tbl` (1:N)
6. **客户关系** → `cif_cross_relations` (N:N)

### 参数配置表
- `cif_business_parameter` - 业务参数开关控制
- `cif_define_column_information` - 自定义字段配置

### 扩展信息表
- `cif_client_verification` - 身份核实
- `cif_client_nra` / `cif_client_nra_detail` - NRA信息
- `cif_client_attach` - 关联信息
- `cif_client_information` - 对公附属信息
- `cif_contact_list` - 联系人列表
- `cif_control_contact` - 控制人联系信息
- `cif_crop_compnay_info` - 法人财务信息

## 查询逻辑特点

### 1. 多层次查询策略
- 主键查询优先（CLIENT_NO）
- 三要素查询备选（证件信息）
- 特殊逻辑处理

### 2. 条件分支查询
- 个人客户 vs 对公客户
- 不同客户类型查询不同扩展表

### 3. 权限控制机制
- 柜员关系检查
- 信息掩码处理
- 级别权限控制

### 4. 参数化配置
- 业务开关控制查询行为
- 自定义字段支持
- 灵活的配置机制

### 5. 完整性保证
- 多表关联查询
- 数据一致性检查
- 业务规则验证
