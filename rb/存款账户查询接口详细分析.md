# 存款账户查询接口详细分析

## 概述

本文档基于提供的接口逻辑和`ens_rb.sql`、`ens_rb001.sql`文件中的表结构，详细分析存款账户查询接口涉及的所有表及其关系。

## 涉及的核心表清单

### 1. 客户信息相关
- **CIF_CLIENT_INFO** (外部接口获取，不在SQL文件中)

### 2. 黑名单校验相关表
- **RC_ALL_LIST** - 黑灰名单信息表
- **RB_UNCOUNTER_RESTRAINTS** - 暂停非柜面账户限制表  
- **RC_LIST_NOT_CHECK_RANGE** - 黑灰名单不检查范围表
- **RC_LIST_TYPE** - 黑名单类型表
- **RC_LIST_CHECK_RANGE** - 黑灰名单检查范围表

### 3. 账户信息相关表
- **RB_BENEFIT_OWNER_INFO** - 账户受益人信息表
- **RB_BASE_ACCT** - 主账户基本信息表
- **RB_ACCT** - 账户基本信息表
- **RB_ACCT_JOINT_INFO** - 联名账户信息登记子表
- **RB_VOUCHER_ACCT_RELATION** - 凭证账户关系表
- **RB_ACCT_CLIENT_RELATION** - 账户客户关系表
- **RB_CM_CD_ACCT** - 现管存管子账户同步记录表

### 4. 卡片信息相关表
- **CD_CARD_BIN** - 卡bin参数表
- **CD_CARD_ARCH** - 卡片基本信息表

### 5. 其他相关表
- **RE_OPEN_CLOSE_REG** - 账户/卡开立注销登记簿 (接口逻辑中提到，但未在SQL文件中找到)

## 核心表结构详解

### RC_ALL_LIST (黑灰名单信息表)
```sql
主键: DATA_TYPE + DATA_VALUE + LIST_TYPE
关键字段:
- DATA_TYPE varchar(20): 数据类型 (AccountNumber-卡/折号, IDType_IDNumber-证件号, ClientNo-客户号等)
- DATA_VALUE varchar(50): 数据值
- LIST_TYPE varchar(10): 名单类型代码
- CLIENT_NO varchar(20): 客户号
- DOCUMENT_TYPE varchar(3): 证件类型
- CLIENT_NAME varchar(200): 客户名称
- ACCT_NAME varchar(200): 账户名称
- LIST_CATEGORY varchar(2): 名单种类代码 (01-人行黑名单, 02-人行灰名单, 11-行内黑名单, 12-行内白名单)
- EFFECT_DATE datetime: 生效日期
- MATURITY_DATE datetime: 到期日期
```

### RB_UNCOUNTER_RESTRAINTS (暂停非柜面账户限制表)
```sql
主键: UNCOUNTER_NO
关键字段:
- UNCOUNTER_NO varchar(50): 编号
- CLIENT_NO varchar(20): 客户号
- DOCUMENT_ID varchar(50): 证件号码
- DOCUMENT_TYPE varchar(3): 证件类型
- CLIENT_NAME varchar(200): 客户名称
- BASE_ACCT_NO varchar(50): 账号/卡号
- ACCT_STATUS char(1): 账户状态
- UNCOUNTER_RESTRAINT_TYPE varchar(3): 暂记非柜面限制类型
- UNCOUNTER_RESTRAINT_STATUS char(1): 暂停非柜面标记 (1-待核实,2-核实通过,3-核实未通过,4-删除名单)
- EFFECT_DATE datetime: 生效日期
- EXPIRE_DATE datetime: 失效日期
```

### RB_BASE_ACCT (主账户基本信息表)
```sql
主键: INTERNAL_KEY + CLIENT_NO
关键字段:
- INTERNAL_KEY bigint: 账户内部键值
- CLIENT_NO varchar(20): 客户号
- CLIENT_TYPE varchar(3): 客户类型
- DOCUMENT_ID varchar(50): 证件号码
- DOCUMENT_TYPE varchar(3): 证件类型
- CARD_NO varchar(50): 卡号
- BASE_ACCT_NO varchar(50): 账号/卡号
- PROD_TYPE varchar(10): 产品类型
- ACCT_SEQ_NO varchar(10): 账户序号
- ACCT_CCY varchar(3): 账户币种
- ACCT_BRANCH varchar(20): 账户开户行
- ACCT_NAME varchar(200): 账户名称
- ACCT_STATUS char(1): 账户状态
- ACCT_TYPE char(1): 账户类型
- DOC_TYPE varchar(10): 凭证类型
- VOUCHER_START_NO varchar(50): 凭证起始号码
- VOUCHER_STATUS varchar(10): 凭证状态
```

### RB_ACCT (账户基本信息表)
```sql
主键: INTERNAL_KEY
关键字段:
- INTERNAL_KEY bigint: 账户内部键值
- CLIENT_NO varchar(20): 客户号
- CLIENT_TYPE varchar(3): 客户类型
- INDIVIDUAL_FLAG char(1): 对公对私标志
- DOCUMENT_ID varchar(50): 证件号码
- DOCUMENT_TYPE varchar(3): 证件类型
- ACCT_CLASS char(1): 账户类别
- CARD_NO varchar(50): 卡号
- BASE_ACCT_NO varchar(50): 账号/卡号
- PROD_TYPE varchar(10): 产品类型
- ACCT_CCY varchar(3): 账户币种
- ACCT_SEQ_NO varchar(10): 账户序号
- ACCT_NAME varchar(200): 账户名称
- ACCT_BRANCH varchar(20): 账户开户行
- ACCT_STATUS char(1): 账户状态
- ACCT_TYPE char(1): 账户类型
- ACCT_NATURE varchar(10): 账户属性
- LEAD_ACCT_FLAG char(1): 主账户标志
- PARENT_INTERNAL_KEY bigint: 上级账户标识符
```

### RB_VOUCHER_ACCT_RELATION (凭证账户关系表)
```sql
主键: BASE_ACCT_NO + DOC_TYPE + VOUCHER_NO + CLIENT_NO
关键字段:
- BASE_ACCT_NO varchar(50): 账号/卡号
- DOC_TYPE varchar(10): 凭证类型
- VOUCHER_NO varchar(50): 凭证号码
- CLIENT_NO varchar(20): 客户号
- PROD_TYPE varchar(10): 产品类型
- ACCT_CCY varchar(3): 账户币种
- ACCT_SEQ_NO varchar(10): 账户序号
- CARD_NO varchar(50): 卡号
- PREFIX varchar(10): 前缀
- DOC_CLASS varchar(10): 凭证种类
- VOUCHER_STATUS varchar(10): 凭证状态
- OLD_STATUS varchar(10): 凭证原状态
- REFERENCE varchar(50): 交易参考号
- COLLAT_IND char(1): 抵质押标志
- COLLAT_NO varchar(50): 抵质押编号
```

### RB_ACCT_CLIENT_RELATION (账户客户关系表)
```sql
主键: CLIENT_NO + BASE_ACCT_NO + ACCT_SEQ_NO + PROD_TYPE + ACCT_CCY + ACTUAL_ACCT_NO
关键字段:
- CLIENT_NO varchar(20): 客户号
- BASE_ACCT_NO varchar(50): 账号/卡号
- ACCT_SEQ_NO varchar(10): 账户序号
- PROD_TYPE varchar(10): 产品类型
- ACCT_CCY varchar(3): 账户币种
- ACTUAL_ACCT_NO varchar(50): 实际账号
- INTERNAL_KEY bigint: 账户内部键值
- CLIENT_TYPE varchar(3): 客户类型
- DOCUMENT_ID varchar(50): 证件号码
- DOCUMENT_TYPE varchar(3): 证件类型
- INDIVIDUAL_FLAG char(1): 对公对私标志
- IS_CARD char(1): 是否卡
- CARD_NO varchar(50): 卡号
- ACCT_BRANCH varchar(20): 账户开户行
- ACCT_NAME varchar(200): 账户名称
- ACCT_CLASS char(1): 账户类别
- ACCT_STATUS char(1): 账户状态
- ACCT_NATURE varchar(10): 账户属性
- ACCT_REAL_FLAG char(1): 账户虚实标志
- DEFAULT_SETTLE_ACCT char(1): 是否默认结算账户
- LEAD_ACCT_FLAG char(1): 主账户标志
- PARENT_INTERNAL_KEY bigint: 上级账户标识符
- REASON_CODE varchar(10): 账户用途
- APP_FLAG char(1): 附属卡标志
- IS_CORP_SETTLE_CARD char(1): 单位结算卡标志
```

### CD_CARD_ARCH (卡片基本信息表)
```sql
主键: CARD_NO + CLIENT_NO
关键字段:
- CARD_NO varchar(50): 卡号
- CLIENT_NO varchar(20): 客户号
- ISSUE_DATE datetime: 发行日期
- ISSUE_USER_ID varchar(20): 发卡柜员
- CARD_STATUS char(1): 卡状态
- CHANGE_CARD_NUM int: 换卡次数
- MAIN_CARD_NO varchar(50): 主卡卡号
- PROD_TYPE varchar(10): 产品类型
- ACCT_SEQ_NO varchar(10): 账户序号
- CARD_CLOSE_REASON varchar(10): 销卡原因
- CARD_CLOSE_DATE datetime: 销卡日期
- VALID_FROM_DATE datetime: 有效期起始日期
- VALID_THRU_DATE datetime: 有效期截止日期
- TRAN_BRANCH varchar(20): 交易机构
- CARD_MEDIUM_TYPE char(1): 卡介质类型
- APPLY_NO varchar(50): 申请编号
- APP_FLAG char(1): 附属卡标志
- IS_CORP_SETTLE_CARD char(1): 单位结算卡标志
- CARD_PB_UNION_FLAG char(1): 卡折合一标志
- SIGN_FLAG char(1): 是否记名卡
- BATCH_JOB_NO varchar(50): 制卡文件批次号
```

## 接口业务流程详解

### 第一阶段：客户信息获取
- **外部接口**: `CifRpc /cif/inq/client/info`
- **功能**: 通过证件信息或客户号获取客户基本信息
- **关联字段**: CLIENT_NO(客户号)、DOCUMENT_ID(证件号码)、DOCUMENT_TYPE(证件类型)、CLIENT_NAME(客户名称)

### 第二阶段：黑名单校验流程

#### 1. 黑名单信息校验 (RC_ALL_LIST)
- **校验条件**: `isCheck != N`
- **校验维度**: 账号、证件号、户名、客户号
- **校验逻辑**: 根据DATA_TYPE匹配不同维度的数据

#### 2. 非柜面限制校验 (RB_UNCOUNTER_RESTRAINTS)
- **校验条件**: `messageType != 1400`
- **校验逻辑**: 通过账户号、证件信息查询是否受限

#### 3. 灰名单校验 (RC_LIST_NOT_CHECK_RANGE)
- **校验条件**: `messageCode` 不在 "1200", "1220", "1300", "1000" 中
- **校验逻辑**: 查询不检查范围表，校验灰名单

#### 4. 黑名单类型处理 (RC_LIST_TYPE)
- **功能**: 对不同限制进行具体报错
- **关联**: 通过LIST_TYPE关联RC_ALL_LIST

#### 5. 黑名单检查范围 (RC_LIST_CHECK_RANGE)
- **功能**: 如果有结果则不报错
- **校验逻辑**: 通过消息码、消息类型等查询检查范围

### 第三阶段：账户信息查询

#### 1. 受益人信息查询 (RB_BENEFIT_OWNER_INFO)
- **查询条件**: 客户号
- **功能**: 获取账户受益人信息

#### 2. 账户列表获取
- **核心表**: RB_BASE_ACCT、RB_ACCT、RB_ACCT_JOINT_INFO
- **查询条件**: 客户号、账户状态、产品类型等

### 第四阶段：账户详细信息处理

#### 路径1：主账户标志处理
**当入参传了主账户标志时**：
- 主账户标志=Y：查询主账户信息，涉及RB_VOUCHER_ACCT_RELATION、RB_ACCT_CLIENT_RELATION
- 主账户标志≠Y：遍历RB_ACCT列表，查询相关表

#### 路径2：凭证类型和凭证号处理
**当传了凭证类型和凭证号时**：
- 通过RB_VOUCHER_ACCT_RELATION获得账户列表
- 遍历账户列表，查询RB_CM_CD_ACCT、CD_CARD_BIN、RB_ACCT_CLIENT_RELATION、RB_ACCT、CD_CARD_ARCH

#### 路径3：默认处理
**当没有传主账户标志和凭证信息时**：
- 通过RB_BASE_ACCT获得账户列表
- 查询RB_VOUCHER_ACCT_RELATION拼装出参信息

## 关键字段映射

### 主要关联字段
- **CLIENT_NO**: 客户号 - 所有表的核心关联字段
- **BASE_ACCT_NO**: 账号/卡号 - 账户相关表的关联字段
- **CARD_NO**: 卡号 - 卡片相关表的关联字段
- **INTERNAL_KEY**: 账户内部键值 - 账户表间的技术关联字段
- **PROD_TYPE**: 产品类型 - 业务分类字段
- **ACCT_CCY**: 账户币种 - 币种维度字段
- **ACCT_SEQ_NO**: 账户序号 - 子账户区分字段

### 状态字段
- **ACCT_STATUS**: 账户状态 (N-新建, H-待激活, A-活动, D-睡眠, S-久悬, O-转营业外, P-逾期, C-关闭)
- **CARD_STATUS**: 卡状态 (A-申请, B-待发, C-活动, D-注销, E-CVN锁定等)
- **VOUCHER_STATUS**: 凭证状态 (NUS-未使用, SOL-已签发, ACP-已承兑等)

## 总结

存款账户查询接口涉及15个核心表，通过客户号、账号、卡号等关键字段建立复杂的关联关系。接口设计体现了银行业务的严谨性，特别是在黑名单校验、账户状态管理、凭证关系维护等方面都有完善的数据支撑。
