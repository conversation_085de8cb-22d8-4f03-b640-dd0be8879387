# RB模块数据结构分析报告

## 概述

RB模块是银行核心系统中的存款业务模块，主要负责卡片管理、账户管理、密码管理、批量处理等核心业务功能。本报告基于`ens_rb.sql`和`ens_rb001.sql`两个数据库脚本文件，对RB模块的数据结构进行全面分析。

## 核心业务领域

### 1. 卡片生命周期管理
- **制卡申请阶段**: `cd_make_card_reg`、`cd_apply_client_reg`
- **制卡文件生成**: `cd_card_file_job`、`cd_make_card_split_msg`
- **卡片前置管理**: `cd_card_prev`
- **卡片正式发行**: `cd_card_arch`
- **卡片变更管理**: `cd_card_chg`

### 2. 账户与卡片关联管理
- **卡账户映射**: `card_account_mapping`
- **对账簿管理**: `cd_acct_book_reg`
- **白名单管理**: `acct_info_white_list`

### 3. 安全管理
- **密码管理**: `cd_password`、`cd_password_hist`、`cd_password_channel`
- **安全锁管理**: `cd_safe_lock`、`cd_card_lock_tbl`、`cd_card_lock_reject_tbl`
- **支取方式管理**: `cd_card_withdraw_type`、`cd_card_withdraw_type_hist`

### 4. 交易与流水管理
- **卡片交易流水**: `cd_card_journal`
- **POS预授权**: `cd_pos_auth_reg`
- **凭证流水**: `cd_voucher_journal`

### 5. 批量处理管理
- **批量文件检查**: `batch_online_check`、`batch_online_check_details`
- **批量文件上传**: `batch_online_upload`

### 6. 系统基础管理
- **机构管理**: `fm_branch`、`fm_branch_holiday`
- **用户管理**: `fm_user`、`fm_user_login_branch`
- **币种管理**: `fm_currency`
- **系统参数**: `fm_system`、`fm_business_parameter`

## 主要数据表分析

### 核心实体表

#### 1. cd_card_arch (卡片基本信息表)
- **主键**: CARD_NO + CLIENT_NO
- **功能**: 记录已发售给客户的卡片基本信息
- **关键字段**: 卡号、客户号、卡状态、产品类型、发行日期、有效期等

#### 2. cd_card_prev (卡片基本前置信息表)
- **主键**: CARD_NO
- **功能**: 记录未出售给客户的卡片信息
- **关键字段**: 卡号、卡状态、产品类型、申请编号、批次号等

#### 3. cd_make_card_reg (制卡申请登记簿)
- **主键**: APPLY_NO
- **功能**: 登记制卡申请信息
- **关键字段**: 申请编号、制卡类型、制卡数量、制卡状态等

#### 4. cd_password (卡密码表)
- **主键**: PWD_TYPE + PWD_KEY + CLIENT_NO
- **功能**: 管理卡片密码信息
- **关键字段**: 密码类型、密码、密码状态、失败次数等

### 业务流程表

#### 制卡业务流程
1. **申请阶段**: `cd_make_card_reg` → `cd_apply_client_reg`
2. **文件生成**: `cd_card_file_job` → `cd_make_card_split_msg`
3. **卡片入库**: `cd_card_prev`
4. **卡片发行**: `cd_card_arch`

#### 卡片变更流程
1. **变更申请**: `cd_card_chg`
2. **新卡生成**: `cd_card_prev` → `cd_card_arch`
3. **旧卡处理**: 状态更新

## 数据关系分析

### 主要外键关系
- `cd_make_card_reg.APPLY_NO` → `cd_card_file_job.APPLY_NO`
- `cd_card_file_job.BATCH_JOB_NO` → `cd_card_prev.BATCH_JOB_NO`
- `cd_card_prev.CARD_NO` → `cd_card_arch.CARD_NO`
- `cd_card_arch.CARD_NO` → `cd_password.PWD_KEY`
- `cd_card_arch.CLIENT_NO` → `cd_password.CLIENT_NO`

### 一对多关系
- 一个申请编号对应多个制卡文件
- 一个批次号对应多张卡片
- 一张卡片对应多个密码类型
- 一个客户对应多张卡片

## 业务规则与约束

### 卡片状态管理
- **申请状态**: A-申请, B-待发, C-活动, D-注销
- **锁定状态**: E-CVN锁定, F-单日密码锁定, H-密码错误次数累计达到上限锁定

### 制卡类型
- **A**: 自编卡
- **N**: 非吉祥卡  
- **V**: 吉祥卡
- **S**: 同号换卡

### 密码类型
- **WD**: 支取密码
- **QY**: 查询密码
- **MA**: 账户管理密码

## 技术特点

### 1. 分区表设计
- `fw_tran_info`: 按日期分区存储交易流水
- `mq_consumer_msg`: 按日期分区存储消息队列信息

### 2. 索引策略
- 主要业务表都建立了合适的索引
- 查询频繁的字段建立了复合索引

### 3. 数据完整性
- 使用外键约束保证数据一致性
- 关键字段设置NOT NULL约束

### 4. 审计跟踪
- 大部分表包含`TRAN_TIMESTAMP`字段记录操作时间
- 历史表记录数据变更轨迹

## 建议与优化

### 1. 性能优化
- 对高频查询字段建立适当索引
- 考虑对大表进行分区优化
- 定期清理历史数据

### 2. 数据治理
- 建立数据字典管理
- 规范字段命名和注释
- 加强数据质量监控

### 3. 安全加固
- 敏感数据加密存储
- 访问权限精细化管理
- 操作日志完整记录

## 总结

RB模块数据结构设计较为完善，涵盖了卡片管理的完整生命周期，具有良好的扩展性和维护性。建议在实际使用中根据业务需求进行适当优化，确保系统的高效稳定运行。
